# -*- coding: utf-8 -*-
# Auto-generated by <PERSON>, do not modify.
# flake8: noqa
# pylint: skip-file

from abc import ABCMeta, abstractmethod
import warnings

from dropbox import account
from dropbox import async_
from dropbox import auth
from dropbox import check
from dropbox import common
from dropbox import contacts
from dropbox import file_properties
from dropbox import file_requests
from dropbox import files
from dropbox import openid
from dropbox import paper
from dropbox import secondary_emails
from dropbox import seen_state
from dropbox import sharing
from dropbox import team
from dropbox import team_common
from dropbox import team_log
from dropbox import team_policies
from dropbox import users
from dropbox import users_common


class DropboxBase(object):
    __metaclass__ = ABCMeta

    @abstractmethod
    def request(self, route, namespace, arg, arg_binary=None):
        pass

    # ------------------------------------------
    # Routes in account namespace

    def account_set_profile_photo(self,
                                  photo):
        """
        Sets a user's profile photo.

        Route attributes:
            scope: account_info.write

        :param photo: Image to set as the user's new profile photo.
        :type photo: :class:`dropbox.account.PhotoSourceArg`
        :rtype: :class:`dropbox.account.SetProfilePhotoResult`
        :raises: :class:`.exceptions.ApiError`

        If this raises, ApiError will contain:
            :class:`dropbox.account.SetProfilePhotoError`
        """
        arg = account.SetProfilePhotoArg(photo)
        r = self.request(
            account.set_profile_photo,
            'account',
            arg,
            None,
        )
        return r

    # ------------------------------------------
    # Routes in auth namespace

    def auth_token_from_oauth1(self,
                               oauth1_token,
                               oauth1_token_secret):
        """
        Creates an OAuth 2.0 access token from the supplied OAuth 1.0 access
        token.

        :param str oauth1_token: The supplied OAuth 1.0 access token.
        :param str oauth1_token_secret: The token secret associated with the
            supplied access token.
        :rtype: :class:`dropbox.auth.TokenFromOAuth1Result`
        :raises: :class:`.exceptions.ApiError`

        If this raises, ApiError will contain:
            :class:`dropbox.auth.TokenFromOAuth1Error`
        """
        arg = auth.TokenFromOAuth1Arg(oauth1_token,
                                      oauth1_token_secret)
        r = self.request(
            auth.token_from_oauth1,
            'auth',
            arg,
            None,
        )
        return r

    def auth_token_revoke(self):
        """
        Disables the access token used to authenticate the call. If there is a
        corresponding refresh token for the access token, this disables that
        refresh token, as well as any other access tokens for that refresh
        token.

        :rtype: None
        """
        arg = None
        r = self.request(
            auth.token_revoke,
            'auth',
            arg,
            None,
        )
        return None

    # ------------------------------------------
    # Routes in check namespace

    def check_app(self,
                  query=''):
        """
        This endpoint performs App Authentication, validating the supplied app
        key and secret, and returns the supplied string, to allow you to test
        your code and connection to the Dropbox API. It has no other effect. If
        you receive an HTTP 200 response with the supplied query, it indicates
        at least part of the Dropbox API infrastructure is working and that the
        app key and secret valid.

        :param str query: The string that you'd like to be echoed back to you.
        :rtype: :class:`dropbox.check.EchoResult`
        """
        arg = check.EchoArg(query)
        r = self.request(
            check.app,
            'check',
            arg,
            None,
        )
        return r

    def check_user(self,
                   query=''):
        """
        This endpoint performs User Authentication, validating the supplied
        access token, and returns the supplied string, to allow you to test your
        code and connection to the Dropbox API. It has no other effect. If you
        receive an HTTP 200 response with the supplied query, it indicates at
        least part of the Dropbox API infrastructure is working and that the
        access token is valid.

        Route attributes:
            scope: account_info.read

        :param str query: The string that you'd like to be echoed back to you.
        :rtype: :class:`dropbox.check.EchoResult`
        """
        arg = check.EchoArg(query)
        r = self.request(
            check.user,
            'check',
            arg,
            None,
        )
        return r

    # ------------------------------------------
    # Routes in contacts namespace

    def contacts_delete_manual_contacts(self):
        """
        Removes all manually added contacts. You'll still keep contacts who are
        on your team or who you imported. New contacts will be added when you
        share.

        Route attributes:
            scope: contacts.write

        :rtype: None
        """
        arg = None
        r = self.request(
            contacts.delete_manual_contacts,
            'contacts',
            arg,
            None,
        )
        return None

    def contacts_delete_manual_contacts_batch(self,
                                              email_addresses):
        """
        Removes manually added contacts from the given list.

        Route attributes:
            scope: contacts.write

        :param List[str] email_addresses: List of manually added contacts to be
            deleted.
        :rtype: None
        :raises: :class:`.exceptions.ApiError`

        If this raises, ApiError will contain:
            :class:`dropbox.contacts.DeleteManualContactsError`
        """
        arg = contacts.DeleteManualContactsArg(email_addresses)
        r = self.request(
            contacts.delete_manual_contacts_batch,
            'contacts',
            arg,
            None,
        )
        return None

    # ------------------------------------------
    # Routes in file_properties namespace

    def file_properties_properties_add(self,
                                       path,
                                       property_groups):
        """
        Add property groups to a Dropbox file. See
        :meth:`file_properties_templates_add_for_user` or
        :meth:`file_properties_templates_add_for_team` to create new templates.

        Route attributes:
            scope: files.metadata.write

        :param str path: A unique identifier for the file or folder.
        :param List[:class:`dropbox.file_properties.PropertyGroup`]
            property_groups: The property groups which are to be added to a
            Dropbox file. No two groups in the input should  refer to the same
            template.
        :rtype: None
        :raises: :class:`.exceptions.ApiError`

        If this raises, ApiError will contain:
            :class:`dropbox.file_properties.AddPropertiesError`
        """
        arg = file_properties.AddPropertiesArg(path,
                                               property_groups)
        r = self.request(
            file_properties.properties_add,
            'file_properties',
            arg,
            None,
        )
        return None

    def file_properties_properties_overwrite(self,
                                             path,
                                             property_groups):
        """
        Overwrite property groups associated with a file. This endpoint should
        be used instead of :meth:`file_properties_properties_update` when
        property groups are being updated via a "snapshot" instead of via a
        "delta". In other words, this endpoint will delete all omitted fields
        from a property group, whereas :meth:`file_properties_properties_update`
        will only delete fields that are explicitly marked for deletion.

        Route attributes:
            scope: files.metadata.write

        :param str path: A unique identifier for the file or folder.
        :param List[:class:`dropbox.file_properties.PropertyGroup`]
            property_groups: The property groups "snapshot" updates to force
            apply. No two groups in the input should  refer to the same
            template.
        :rtype: None
        :raises: :class:`.exceptions.ApiError`

        If this raises, ApiError will contain:
            :class:`dropbox.file_properties.InvalidPropertyGroupError`
        """
        arg = file_properties.OverwritePropertyGroupArg(path,
                                                        property_groups)
        r = self.request(
            file_properties.properties_overwrite,
            'file_properties',
            arg,
            None,
        )
        return None

    def file_properties_properties_remove(self,
                                          path,
                                          property_template_ids):
        """
        Permanently removes the specified property group from the file. To
        remove specific property field key value pairs, see
        :meth:`file_properties_properties_update`. To update a template, see
        :meth:`file_properties_templates_update_for_user` or
        :meth:`file_properties_templates_update_for_team`. To remove a template,
        see :meth:`file_properties_templates_remove_for_user` or
        :meth:`file_properties_templates_remove_for_team`.

        Route attributes:
            scope: files.metadata.write

        :param str path: A unique identifier for the file or folder.
        :param List[str] property_template_ids: A list of identifiers for a
            template created by :meth:`file_properties_templates_add_for_user`
            or :meth:`file_properties_templates_add_for_team`.
        :rtype: None
        :raises: :class:`.exceptions.ApiError`

        If this raises, ApiError will contain:
            :class:`dropbox.file_properties.RemovePropertiesError`
        """
        arg = file_properties.RemovePropertiesArg(path,
                                                  property_template_ids)
        r = self.request(
            file_properties.properties_remove,
            'file_properties',
            arg,
            None,
        )
        return None

    def file_properties_properties_search(self,
                                          queries,
                                          template_filter=file_properties.TemplateFilter.filter_none):
        """
        Search across property templates for particular property field values.

        Route attributes:
            scope: files.metadata.read

        :param List[:class:`dropbox.file_properties.PropertiesSearchQuery`]
            queries: Queries to search.
        :param template_filter: Filter results to contain only properties
            associated with these template IDs.
        :type template_filter: :class:`dropbox.file_properties.TemplateFilter`
        :rtype: :class:`dropbox.file_properties.PropertiesSearchResult`
        :raises: :class:`.exceptions.ApiError`

        If this raises, ApiError will contain:
            :class:`dropbox.file_properties.PropertiesSearchError`
        """
        arg = file_properties.PropertiesSearchArg(queries,
                                                  template_filter)
        r = self.request(
            file_properties.properties_search,
            'file_properties',
            arg,
            None,
        )
        return r

    def file_properties_properties_search_continue(self,
                                                   cursor):
        """
        Once a cursor has been retrieved from
        :meth:`file_properties_properties_search`, use this to paginate through
        all search results.

        Route attributes:
            scope: files.metadata.read

        :param str cursor: The cursor returned by your last call to
            :meth:`file_properties_properties_search` or
            :meth:`file_properties_properties_search_continue`.
        :rtype: :class:`dropbox.file_properties.PropertiesSearchResult`
        :raises: :class:`.exceptions.ApiError`

        If this raises, ApiError will contain:
            :class:`dropbox.file_properties.PropertiesSearchContinueError`
        """
        arg = file_properties.PropertiesSearchContinueArg(cursor)
        r = self.request(
            file_properties.properties_search_continue,
            'file_properties',
            arg,
            None,
        )
        return r

    def file_properties_properties_update(self,
                                          path,
                                          update_property_groups):
        """
        Add, update or remove properties associated with the supplied file and
        templates. This endpoint should be used instead of
        :meth:`file_properties_properties_overwrite` when property groups are
        being updated via a "delta" instead of via a "snapshot" . In other
        words, this endpoint will not delete any omitted fields from a property
        group, whereas :meth:`file_properties_properties_overwrite` will delete
        any fields that are omitted from a property group.

        Route attributes:
            scope: files.metadata.write

        :param str path: A unique identifier for the file or folder.
        :param List[:class:`dropbox.file_properties.PropertyGroupUpdate`]
            update_property_groups: The property groups "delta" updates to
            apply.
        :rtype: None
        :raises: :class:`.exceptions.ApiError`

        If this raises, ApiError will contain:
            :class:`dropbox.file_properties.UpdatePropertiesError`
        """
        arg = file_properties.UpdatePropertiesArg(path,
                                                  update_property_groups)
        r = self.request(
            file_properties.properties_update,
            'file_properties',
            arg,
            None,
        )
        return None

    def file_properties_templates_add_for_user(self,
                                               name,
                                               description,
                                               fields):
        """
        Add a template associated with a user. See
        :meth:`file_properties_properties_add` to add properties to a file. This
        endpoint can't be called on a team member or admin's behalf.

        Route attributes:
            scope: files.metadata.write

        :rtype: :class:`dropbox.file_properties.AddTemplateResult`
        :raises: :class:`.exceptions.ApiError`

        If this raises, ApiError will contain:
            :class:`dropbox.file_properties.ModifyTemplateError`
        """
        arg = file_properties.AddTemplateArg(name,
                                             description,
                                             fields)
        r = self.request(
            file_properties.templates_add_for_user,
            'file_properties',
            arg,
            None,
        )
        return r

    def file_properties_templates_get_for_user(self,
                                               template_id):
        """
        Get the schema for a specified template. This endpoint can't be called
        on a team member or admin's behalf.

        Route attributes:
            scope: files.metadata.read

        :param str template_id: An identifier for template added by route  See
            :meth:`file_properties_templates_add_for_user` or
            :meth:`file_properties_templates_add_for_team`.
        :rtype: :class:`dropbox.file_properties.GetTemplateResult`
        :raises: :class:`.exceptions.ApiError`

        If this raises, ApiError will contain:
            :class:`dropbox.file_properties.TemplateError`
        """
        arg = file_properties.GetTemplateArg(template_id)
        r = self.request(
            file_properties.templates_get_for_user,
            'file_properties',
            arg,
            None,
        )
        return r

    def file_properties_templates_list_for_user(self):
        """
        Get the template identifiers for a team. To get the schema of each
        template use :meth:`file_properties_templates_get_for_user`. This
        endpoint can't be called on a team member or admin's behalf.

        Route attributes:
            scope: files.metadata.read

        :rtype: :class:`dropbox.file_properties.ListTemplateResult`
        :raises: :class:`.exceptions.ApiError`

        If this raises, ApiError will contain:
            :class:`dropbox.file_properties.TemplateError`
        """
        arg = None
        r = self.request(
            file_properties.templates_list_for_user,
            'file_properties',
            arg,
            None,
        )
        return r

    def file_properties_templates_remove_for_user(self,
                                                  template_id):
        """
        Permanently removes the specified template created from
        :meth:`file_properties_templates_add_for_user`. All properties
        associated with the template will also be removed. This action cannot be
        undone.

        Route attributes:
            scope: files.metadata.write

        :param str template_id: An identifier for a template created by
            :meth:`file_properties_templates_add_for_user` or
            :meth:`file_properties_templates_add_for_team`.
        :rtype: None
        :raises: :class:`.exceptions.ApiError`

        If this raises, ApiError will contain:
            :class:`dropbox.file_properties.TemplateError`
        """
        arg = file_properties.RemoveTemplateArg(template_id)
        r = self.request(
            file_properties.templates_remove_for_user,
            'file_properties',
            arg,
            None,
        )
        return None

    def file_properties_templates_update_for_user(self,
                                                  template_id,
                                                  name=None,
                                                  description=None,
                                                  add_fields=None):
        """
        Update a template associated with a user. This route can update the
        template name, the template description and add optional properties to
        templates. This endpoint can't be called on a team member or admin's
        behalf.

        Route attributes:
            scope: files.metadata.write

        :param str template_id: An identifier for template added by  See
            :meth:`file_properties_templates_add_for_user` or
            :meth:`file_properties_templates_add_for_team`.
        :param Nullable[str] name: A display name for the template. template
            names can be up to 256 bytes.
        :param Nullable[str] description: Description for the new template.
            Template descriptions can be up to 1024 bytes.
        :param
            Nullable[List[:class:`dropbox.file_properties.PropertyFieldTemplate`]]
            add_fields: Property field templates to be added to the group
            template. There can be up to 32 properties in a single template.
        :rtype: :class:`dropbox.file_properties.UpdateTemplateResult`
        :raises: :class:`.exceptions.ApiError`

        If this raises, ApiError will contain:
            :class:`dropbox.file_properties.ModifyTemplateError`
        """
        arg = file_properties.UpdateTemplateArg(template_id,
                                                name,
                                                description,
                                                add_fields)
        r = self.request(
            file_properties.templates_update_for_user,
            'file_properties',
            arg,
            None,
        )
        return r

    # ------------------------------------------
    # Routes in file_requests namespace

    def file_requests_count(self):
        """
        Returns the total number of file requests owned by this user. Includes
        both open and closed file requests.

        Route attributes:
            scope: file_requests.read

        :rtype: :class:`dropbox.file_requests.CountFileRequestsResult`
        """
        arg = None
        r = self.request(
            file_requests.count,
            'file_requests',
            arg,
            None,
        )
        return r

    def file_requests_create(self,
                             title,
                             destination,
                             deadline=None,
                             open=True,
                             description=None):
        """
        Creates a file request for this user.

        Route attributes:
            scope: file_requests.write

        :param str title: The title of the file request. Must not be empty.
        :param str destination: The path of the folder in the Dropbox where
            uploaded files will be sent. For apps with the app folder
            permission, this will be relative to the app folder.
        :param Nullable[:class:`dropbox.file_requests.FileRequestDeadline`]
            deadline: The deadline for the file request. Deadlines can only be
            set by Professional and Business accounts.
        :param bool open: Whether or not the file request should be open. If the
            file request is closed, it will not accept any file submissions, but
            it can be opened later.
        :param Nullable[str] description: A description of the file request.
        :rtype: :class:`dropbox.file_requests.FileRequest`
        :raises: :class:`.exceptions.ApiError`

        If this raises, ApiError will contain:
            :class:`dropbox.file_requests.CreateFileRequestError`
        """
        arg = file_requests.CreateFileRequestArgs(title,
                                                  destination,
                                                  deadline,
                                                  open,
                                                  description)
        r = self.request(
            file_requests.create,
            'file_requests',
            arg,
            None,
        )
        return r

    def file_requests_delete(self,
                             ids):
        """
        Delete a batch of closed file requests.

        Route attributes:
            scope: file_requests.write

        :param List[str] ids: List IDs of the file requests to delete.
        :rtype: :class:`dropbox.file_requests.DeleteFileRequestsResult`
        :raises: :class:`.exceptions.ApiError`

        If this raises, ApiError will contain:
            :class:`dropbox.file_requests.DeleteFileRequestError`
        """
        arg = file_requests.DeleteFileRequestArgs(ids)
        r = self.request(
            file_requests.delete,
            'file_requests',
            arg,
            None,
        )
        return r

    def file_requests_delete_all_closed(self):
        """
        Delete all closed file requests owned by this user.

        Route attributes:
            scope: file_requests.write

        :rtype: :class:`dropbox.file_requests.DeleteAllClosedFileRequestsResult`
        """
        arg = None
        r = self.request(
            file_requests.delete_all_closed,
            'file_requests',
            arg,
            None,
        )
        return r

    def file_requests_get(self,
                          id):
        """
        Returns the specified file request.

        Route attributes:
            scope: file_requests.read

        :param str id: The ID of the file request to retrieve.
        :rtype: :class:`dropbox.file_requests.FileRequest`
        """
        arg = file_requests.GetFileRequestArgs(id)
        r = self.request(
            file_requests.get,
            'file_requests',
            arg,
            None,
        )
        return r

    def file_requests_list_v2(self,
                              limit=1000):
        """
        Returns a list of file requests owned by this user. For apps with the
        app folder permission, this will only return file requests with
        destinations in the app folder.

        Route attributes:
            scope: file_requests.read

        :param int limit: The maximum number of file requests that should be
            returned per request.
        :rtype: :class:`dropbox.file_requests.ListFileRequestsV2Result`
        """
        arg = file_requests.ListFileRequestsArg(limit)
        r = self.request(
            file_requests.list_v2,
            'file_requests',
            arg,
            None,
        )
        return r

    def file_requests_list(self):
        """
        Returns a list of file requests owned by this user. For apps with the
        app folder permission, this will only return file requests with
        destinations in the app folder.

        Route attributes:
            scope: file_requests.read

        :rtype: :class:`dropbox.file_requests.ListFileRequestsResult`
        """
        arg = None
        r = self.request(
            file_requests.list,
            'file_requests',
            arg,
            None,
        )
        return r

    def file_requests_list_continue(self,
                                    cursor):
        """
        Once a cursor has been retrieved from :meth:`file_requests_list_v2`, use
        this to paginate through all file requests. The cursor must come from a
        previous call to :meth:`file_requests_list_v2` or
        :meth:`file_requests_list_continue`.

        Route attributes:
            scope: file_requests.read

        :param str cursor: The cursor returned by the previous API call
            specified in the endpoint description.
        :rtype: :class:`dropbox.file_requests.ListFileRequestsV2Result`
        :raises: :class:`.exceptions.ApiError`

        If this raises, ApiError will contain:
            :class:`dropbox.file_requests.ListFileRequestsContinueError`
        """
        arg = file_requests.ListFileRequestsContinueArg(cursor)
        r = self.request(
            file_requests.list_continue,
            'file_requests',
            arg,
            None,
        )
        return r

    def file_requests_update(self,
                             id,
                             title=None,
                             destination=None,
                             deadline=file_requests.UpdateFileRequestDeadline.no_update,
                             open=None,
                             description=None):
        """
        Update a file request.

        Route attributes:
            scope: file_requests.write

        :param str id: The ID of the file request to update.
        :param Nullable[str] title: The new title of the file request. Must not
            be empty.
        :param Nullable[str] destination: The new path of the folder in the
            Dropbox where uploaded files will be sent. For apps with the app
            folder permission, this will be relative to the app folder.
        :param deadline: The new deadline for the file request. Deadlines can
            only be set by Professional and Business accounts.
        :type deadline: :class:`dropbox.file_requests.UpdateFileRequestDeadline`
        :param Nullable[bool] open: Whether to set this file request as open or
            closed.
        :param Nullable[str] description: The description of the file request.
        :rtype: :class:`dropbox.file_requests.FileRequest`
        """
        arg = file_requests.UpdateFileRequestArgs(id,
                                                  title,
                                                  destination,
                                                  deadline,
                                                  open,
                                                  description)
        r = self.request(
            file_requests.update,
            'file_requests',
            arg,
            None,
        )
        return r

    # ------------------------------------------
    # Routes in files namespace

    def files_alpha_get_metadata(self,
                                 path,
                                 include_media_info=False,
                                 include_deleted=False,
                                 include_has_explicit_shared_members=False,
                                 include_property_groups=None,
                                 include_property_templates=None):
        """
        Returns the metadata for a file or folder. This is an alpha endpoint
        compatible with the properties API. Note: Metadata for the root folder
        is unsupported.

        Route attributes:
            scope: files.metadata.read

        :param Nullable[List[str]] include_property_templates: If set to a valid
            list of template IDs, ``FileMetadata.property_groups`` is set for
            files with custom properties.
        :rtype: :class:`dropbox.files.Metadata`
        :raises: :class:`.exceptions.ApiError`

        If this raises, ApiError will contain:
            :class:`dropbox.files.AlphaGetMetadataError`
        """
        warnings.warn(
            'alpha/get_metadata is deprecated. Use get_metadata.',
            DeprecationWarning,
        )
        arg = files.AlphaGetMetadataArg(path,
                                        include_media_info,
                                        include_deleted,
                                        include_has_explicit_shared_members,
                                        include_property_groups,
                                        include_property_templates)
        r = self.request(
            files.alpha_get_metadata,
            'files',
            arg,
            None,
        )
        return r

    def files_alpha_upload(self,
                           f,
                           path,
                           mode=files.WriteMode.add,
                           autorename=False,
                           client_modified=None,
                           mute=False,
                           property_groups=None,
                           strict_conflict=False,
                           content_hash=None):
        """
        Create a new file with the contents provided in the request. Note that
        the behavior of this alpha endpoint is unstable and subject to change.
        Do not use this to upload a file larger than 150 MB. Instead, create an
        upload session with :meth:`files_upload_session_start`.

        Route attributes:
            scope: files.content.write

        :param bytes f: Contents to upload.
        :param Nullable[str] content_hash: A hash of the file content uploaded
            in this call. If provided and the uploaded content does not match
            this hash, an error will be returned. For more information see our
            `Content hash
            <https://www.dropbox.com/developers/reference/content-hash>`_ page.
        :rtype: :class:`dropbox.files.FileMetadata`
        :raises: :class:`.exceptions.ApiError`

        If this raises, ApiError will contain:
            :class:`dropbox.files.UploadError`
        """
        warnings.warn(
            'alpha/upload is deprecated. Use upload.',
            DeprecationWarning,
        )
        arg = files.UploadArg(path,
                              mode,
                              autorename,
                              client_modified,
                              mute,
                              property_groups,
                              strict_conflict,
                              content_hash)
        r = self.request(
            files.alpha_upload,
            'files',
            arg,
            f,
        )
        return r

    def files_copy_v2(self,
                      from_path,
                      to_path,
                      allow_shared_folder=False,
                      autorename=False,
                      allow_ownership_transfer=False):
        """
        Copy a file or folder to a different location in the user's Dropbox. If
        the source path is a folder all its contents will be copied.

        Route attributes:
            scope: files.content.write

        :param bool allow_shared_folder: This flag has no effect.
        :param bool autorename: If there's a conflict, have the Dropbox server
            try to autorename the file to avoid the conflict.
        :param bool allow_ownership_transfer: Allow moves by owner even if it
            would result in an ownership transfer for the content being moved.
            This does not apply to copies.
        :rtype: :class:`dropbox.files.RelocationResult`
        :raises: :class:`.exceptions.ApiError`

        If this raises, ApiError will contain:
            :class:`dropbox.files.RelocationError`
        """
        arg = files.RelocationArg(from_path,
                                  to_path,
                                  allow_shared_folder,
                                  autorename,
                                  allow_ownership_transfer)
        r = self.request(
            files.copy_v2,
            'files',
            arg,
            None,
        )
        return r

    def files_copy(self,
                   from_path,
                   to_path,
                   allow_shared_folder=False,
                   autorename=False,
                   allow_ownership_transfer=False):
        """
        Copy a file or folder to a different location in the user's Dropbox. If
        the source path is a folder all its contents will be copied.

        Route attributes:
            scope: files.content.write

        :param bool allow_shared_folder: This flag has no effect.
        :param bool autorename: If there's a conflict, have the Dropbox server
            try to autorename the file to avoid the conflict.
        :param bool allow_ownership_transfer: Allow moves by owner even if it
            would result in an ownership transfer for the content being moved.
            This does not apply to copies.
        :rtype: :class:`dropbox.files.Metadata`
        :raises: :class:`.exceptions.ApiError`

        If this raises, ApiError will contain:
            :class:`dropbox.files.RelocationError`
        """
        warnings.warn(
            'copy is deprecated. Use copy.',
            DeprecationWarning,
        )
        arg = files.RelocationArg(from_path,
                                  to_path,
                                  allow_shared_folder,
                                  autorename,
                                  allow_ownership_transfer)
        r = self.request(
            files.copy,
            'files',
            arg,
            None,
        )
        return r

    def files_copy_batch_v2(self,
                            entries,
                            autorename=False):
        """
        Copy multiple files or folders to different locations at once in the
        user's Dropbox. This route will replace :meth:`files_copy_batch`. The
        main difference is this route will return status for each entry, while
        :meth:`files_copy_batch` raises failure if any entry fails. This route
        will either finish synchronously, or return a job ID and do the async
        copy job in background. Please use :meth:`files_copy_batch_check_v2` to
        check the job status.

        Route attributes:
            scope: files.content.write

        :param List[:class:`dropbox.files.RelocationPath`] entries: List of
            entries to be moved or copied. Each entry is
            :class:`dropbox.files.RelocationPath`.
        :param bool autorename: If there's a conflict with any file, have the
            Dropbox server try to autorename that file to avoid the conflict.
        :rtype: :class:`dropbox.files.RelocationBatchV2Launch`
        """
        arg = files.RelocationBatchArgBase(entries,
                                           autorename)
        r = self.request(
            files.copy_batch_v2,
            'files',
            arg,
            None,
        )
        return r

    def files_copy_batch(self,
                         entries,
                         autorename=False,
                         allow_shared_folder=False,
                         allow_ownership_transfer=False):
        """
        Copy multiple files or folders to different locations at once in the
        user's Dropbox. This route will return job ID immediately and do the
        async copy job in background. Please use :meth:`files_copy_batch_check`
        to check the job status.

        Route attributes:
            scope: files.content.write

        :param bool allow_shared_folder: This flag has no effect.
        :param bool allow_ownership_transfer: Allow moves by owner even if it
            would result in an ownership transfer for the content being moved.
            This does not apply to copies.
        :rtype: :class:`dropbox.files.RelocationBatchLaunch`
        """
        warnings.warn(
            'copy_batch is deprecated. Use copy_batch.',
            DeprecationWarning,
        )
        arg = files.RelocationBatchArg(entries,
                                       autorename,
                                       allow_shared_folder,
                                       allow_ownership_transfer)
        r = self.request(
            files.copy_batch,
            'files',
            arg,
            None,
        )
        return r

    def files_copy_batch_check_v2(self,
                                  async_job_id):
        """
        Returns the status of an asynchronous job for
        :meth:`files_copy_batch_v2`. It returns list of results for each entry.

        Route attributes:
            scope: files.content.write

        :param str async_job_id: Id of the asynchronous job. This is the value
            of a response returned from the method that launched the job.
        :rtype: :class:`dropbox.files.RelocationBatchV2JobStatus`
        :raises: :class:`.exceptions.ApiError`

        If this raises, ApiError will contain:
            :class:`dropbox.files.PollError`
        """
        arg = async_.PollArg(async_job_id)
        r = self.request(
            files.copy_batch_check_v2,
            'files',
            arg,
            None,
        )
        return r

    def files_copy_batch_check(self,
                               async_job_id):
        """
        Returns the status of an asynchronous job for :meth:`files_copy_batch`.
        If success, it returns list of results for each entry.

        Route attributes:
            scope: files.content.write

        :param str async_job_id: Id of the asynchronous job. This is the value
            of a response returned from the method that launched the job.
        :rtype: :class:`dropbox.files.RelocationBatchJobStatus`
        :raises: :class:`.exceptions.ApiError`

        If this raises, ApiError will contain:
            :class:`dropbox.files.PollError`
        """
        warnings.warn(
            'copy_batch/check is deprecated. Use copy_batch/check.',
            DeprecationWarning,
        )
        arg = async_.PollArg(async_job_id)
        r = self.request(
            files.copy_batch_check,
            'files',
            arg,
            None,
        )
        return r

    def files_copy_reference_get(self,
                                 path):
        """
        Get a copy reference to a file or folder. This reference string can be
        used to save that file or folder to another user's Dropbox by passing it
        to :meth:`files_copy_reference_save`.

        Route attributes:
            scope: files.content.write

        :param str path: The path to the file or folder you want to get a copy
            reference to.
        :rtype: :class:`dropbox.files.GetCopyReferenceResult`
        :raises: :class:`.exceptions.ApiError`

        If this raises, ApiError will contain:
            :class:`dropbox.files.GetCopyReferenceError`
        """
        arg = files.GetCopyReferenceArg(path)
        r = self.request(
            files.copy_reference_get,
            'files',
            arg,
            None,
        )
        return r

    def files_copy_reference_save(self,
                                  copy_reference,
                                  path):
        """
        Save a copy reference returned by :meth:`files_copy_reference_get` to
        the user's Dropbox.

        Route attributes:
            scope: files.content.write

        :param str copy_reference: A copy reference returned by
            :meth:`files_copy_reference_get`.
        :param str path: Path in the user's Dropbox that is the destination.
        :rtype: :class:`dropbox.files.SaveCopyReferenceResult`
        :raises: :class:`.exceptions.ApiError`

        If this raises, ApiError will contain:
            :class:`dropbox.files.SaveCopyReferenceError`
        """
        arg = files.SaveCopyReferenceArg(copy_reference,
                                         path)
        r = self.request(
            files.copy_reference_save,
            'files',
            arg,
            None,
        )
        return r

    def files_create_folder_v2(self,
                               path,
                               autorename=False):
        """
        Create a folder at a given path.

        Route attributes:
            scope: files.content.write

        :param str path: Path in the user's Dropbox to create.
        :param bool autorename: If there's a conflict, have the Dropbox server
            try to autorename the folder to avoid the conflict.
        :rtype: :class:`dropbox.files.CreateFolderResult`
        :raises: :class:`.exceptions.ApiError`

        If this raises, ApiError will contain:
            :class:`dropbox.files.CreateFolderError`
        """
        arg = files.CreateFolderArg(path,
                                    autorename)
        r = self.request(
            files.create_folder_v2,
            'files',
            arg,
            None,
        )
        return r

    def files_create_folder(self,
                            path,
                            autorename=False):
        """
        Create a folder at a given path.

        Route attributes:
            scope: files.content.write

        :param str path: Path in the user's Dropbox to create.
        :param bool autorename: If there's a conflict, have the Dropbox server
            try to autorename the folder to avoid the conflict.
        :rtype: :class:`dropbox.files.FolderMetadata`
        :raises: :class:`.exceptions.ApiError`

        If this raises, ApiError will contain:
            :class:`dropbox.files.CreateFolderError`
        """
        warnings.warn(
            'create_folder is deprecated. Use create_folder.',
            DeprecationWarning,
        )
        arg = files.CreateFolderArg(path,
                                    autorename)
        r = self.request(
            files.create_folder,
            'files',
            arg,
            None,
        )
        return r

    def files_create_folder_batch(self,
                                  paths,
                                  autorename=False,
                                  force_async=False):
        """
        Create multiple folders at once. This route is asynchronous for large
        batches, which returns a job ID immediately and runs the create folder
        batch asynchronously. Otherwise, creates the folders and returns the
        result synchronously for smaller inputs. You can force asynchronous
        behaviour by using the ``CreateFolderBatchArg.force_async`` flag.  Use
        :meth:`files_create_folder_batch_check` to check the job status.

        Route attributes:
            scope: files.content.write

        :param List[str] paths: List of paths to be created in the user's
            Dropbox. Duplicate path arguments in the batch are considered only
            once.
        :param bool autorename: If there's a conflict, have the Dropbox server
            try to autorename the folder to avoid the conflict.
        :param bool force_async: Whether to force the create to happen
            asynchronously.
        :rtype: :class:`dropbox.files.CreateFolderBatchLaunch`
        """
        arg = files.CreateFolderBatchArg(paths,
                                         autorename,
                                         force_async)
        r = self.request(
            files.create_folder_batch,
            'files',
            arg,
            None,
        )
        return r

    def files_create_folder_batch_check(self,
                                        async_job_id):
        """
        Returns the status of an asynchronous job for
        :meth:`files_create_folder_batch`. If success, it returns list of result
        for each entry.

        Route attributes:
            scope: files.content.write

        :param str async_job_id: Id of the asynchronous job. This is the value
            of a response returned from the method that launched the job.
        :rtype: :class:`dropbox.files.CreateFolderBatchJobStatus`
        :raises: :class:`.exceptions.ApiError`

        If this raises, ApiError will contain:
            :class:`dropbox.files.PollError`
        """
        arg = async_.PollArg(async_job_id)
        r = self.request(
            files.create_folder_batch_check,
            'files',
            arg,
            None,
        )
        return r

    def files_delete_v2(self,
                        path,
                        parent_rev=None):
        """
        Delete the file or folder at a given path. If the path is a folder, all
        its contents will be deleted too. A successful response indicates that
        the file or folder was deleted. The returned metadata will be the
        corresponding :class:`dropbox.files.FileMetadata` or
        :class:`dropbox.files.FolderMetadata` for the item at time of deletion,
        and not a :class:`dropbox.files.DeletedMetadata` object.

        Route attributes:
            scope: files.content.write

        :param str path: Path in the user's Dropbox to delete.
        :param Nullable[str] parent_rev: Perform delete if given "rev" matches
            the existing file's latest "rev". This field does not support
            deleting a folder.
        :rtype: :class:`dropbox.files.DeleteResult`
        :raises: :class:`.exceptions.ApiError`

        If this raises, ApiError will contain:
            :class:`dropbox.files.DeleteError`
        """
        arg = files.DeleteArg(path,
                              parent_rev)
        r = self.request(
            files.delete_v2,
            'files',
            arg,
            None,
        )
        return r

    def files_delete(self,
                     path,
                     parent_rev=None):
        """
        Delete the file or folder at a given path. If the path is a folder, all
        its contents will be deleted too. A successful response indicates that
        the file or folder was deleted. The returned metadata will be the
        corresponding :class:`dropbox.files.FileMetadata` or
        :class:`dropbox.files.FolderMetadata` for the item at time of deletion,
        and not a :class:`dropbox.files.DeletedMetadata` object.

        Route attributes:
            scope: files.content.write

        :param str path: Path in the user's Dropbox to delete.
        :param Nullable[str] parent_rev: Perform delete if given "rev" matches
            the existing file's latest "rev". This field does not support
            deleting a folder.
        :rtype: :class:`dropbox.files.Metadata`
        :raises: :class:`.exceptions.ApiError`

        If this raises, ApiError will contain:
            :class:`dropbox.files.DeleteError`
        """
        warnings.warn(
            'delete is deprecated. Use delete.',
            DeprecationWarning,
        )
        arg = files.DeleteArg(path,
                              parent_rev)
        r = self.request(
            files.delete,
            'files',
            arg,
            None,
        )
        return r

    def files_delete_batch(self,
                           entries):
        """
        Delete multiple files/folders at once. This route is asynchronous, which
        returns a job ID immediately and runs the delete batch asynchronously.
        Use :meth:`files_delete_batch_check` to check the job status.

        Route attributes:
            scope: files.content.write

        :type entries: List[:class:`dropbox.files.DeleteArg`]
        :rtype: :class:`dropbox.files.DeleteBatchLaunch`
        """
        arg = files.DeleteBatchArg(entries)
        r = self.request(
            files.delete_batch,
            'files',
            arg,
            None,
        )
        return r

    def files_delete_batch_check(self,
                                 async_job_id):
        """
        Returns the status of an asynchronous job for
        :meth:`files_delete_batch`. If success, it returns list of result for
        each entry.

        Route attributes:
            scope: files.content.write

        :param str async_job_id: Id of the asynchronous job. This is the value
            of a response returned from the method that launched the job.
        :rtype: :class:`dropbox.files.DeleteBatchJobStatus`
        :raises: :class:`.exceptions.ApiError`

        If this raises, ApiError will contain:
            :class:`dropbox.files.PollError`
        """
        arg = async_.PollArg(async_job_id)
        r = self.request(
            files.delete_batch_check,
            'files',
            arg,
            None,
        )
        return r

    def files_download(self,
                       path,
                       rev=None):
        """
        Download a file from a user's Dropbox.

        Route attributes:
            scope: files.content.read

        :param str path: The path of the file to download.
        :param Nullable[str] rev: Please specify revision in ``path`` instead.
        :rtype: (:class:`dropbox.files.FileMetadata`,
                 :class:`requests.models.Response`)
        :raises: :class:`.exceptions.ApiError`

        If this raises, ApiError will contain:
            :class:`dropbox.files.DownloadError`

        If you do not consume the entire response body, then you must call close
        on the response object, otherwise you will max out your available
        connections. We recommend using the `contextlib.closing
        <https://docs.python.org/2/library/contextlib.html#contextlib.closing>`_
        context manager to ensure this.
        """
        arg = files.DownloadArg(path,
                                rev)
        r = self.request(
            files.download,
            'files',
            arg,
            None,
        )
        return r

    def files_download_to_file(self,
                               download_path,
                               path,
                               rev=None):
        """
        Download a file from a user's Dropbox.

        Route attributes:
            scope: files.content.read

        :param str download_path: Path on local machine to save file.
        :param str path: The path of the file to download.
        :param Nullable[str] rev: Please specify revision in ``path`` instead.
        :rtype: :class:`dropbox.files.FileMetadata`
        :raises: :class:`.exceptions.ApiError`

        If this raises, ApiError will contain:
            :class:`dropbox.files.DownloadError`
        """
        arg = files.DownloadArg(path,
                                rev)
        r = self.request(
            files.download,
            'files',
            arg,
            None,
        )
        self._save_body_to_file(download_path, r[1])
        return r[0]

    def files_download_zip(self,
                           path):
        """
        Download a folder from the user's Dropbox, as a zip file. The folder
        must be less than 20 GB in size and any single file within must be less
        than 4 GB in size. The resulting zip must have fewer than 10,000 total
        file and folder entries, including the top level folder. The input
        cannot be a single file. Note: this endpoint does not support HTTP range
        requests.

        Route attributes:
            scope: files.content.read

        :param str path: The path of the folder to download.
        :rtype: (:class:`dropbox.files.DownloadZipResult`,
                 :class:`requests.models.Response`)
        :raises: :class:`.exceptions.ApiError`

        If this raises, ApiError will contain:
            :class:`dropbox.files.DownloadZipError`

        If you do not consume the entire response body, then you must call close
        on the response object, otherwise you will max out your available
        connections. We recommend using the `contextlib.closing
        <https://docs.python.org/2/library/contextlib.html#contextlib.closing>`_
        context manager to ensure this.
        """
        arg = files.DownloadZipArg(path)
        r = self.request(
            files.download_zip,
            'files',
            arg,
            None,
        )
        return r

    def files_download_zip_to_file(self,
                                   download_path,
                                   path):
        """
        Download a folder from the user's Dropbox, as a zip file. The folder
        must be less than 20 GB in size and any single file within must be less
        than 4 GB in size. The resulting zip must have fewer than 10,000 total
        file and folder entries, including the top level folder. The input
        cannot be a single file. Note: this endpoint does not support HTTP range
        requests.

        Route attributes:
            scope: files.content.read

        :param str download_path: Path on local machine to save file.
        :param str path: The path of the folder to download.
        :rtype: :class:`dropbox.files.DownloadZipResult`
        :raises: :class:`.exceptions.ApiError`

        If this raises, ApiError will contain:
            :class:`dropbox.files.DownloadZipError`
        """
        arg = files.DownloadZipArg(path)
        r = self.request(
            files.download_zip,
            'files',
            arg,
            None,
        )
        self._save_body_to_file(download_path, r[1])
        return r[0]

    def files_export(self,
                     path,
                     export_format=None):
        """
        Export a file from a user's Dropbox. This route only supports exporting
        files that cannot be downloaded directly  and whose
        ``ExportResult.file_metadata`` has ``ExportInfo.export_as`` populated.

        Route attributes:
            scope: files.content.read

        :param str path: The path of the file to be exported.
        :param Nullable[str] export_format: The file format to which the file
            should be exported. This must be one of the formats listed in the
            file's export_options returned by :meth:`files_get_metadata`. If
            none is specified, the default format (specified in export_as in
            file metadata) will be used.
        :rtype: (:class:`dropbox.files.ExportResult`,
                 :class:`requests.models.Response`)
        :raises: :class:`.exceptions.ApiError`

        If this raises, ApiError will contain:
            :class:`dropbox.files.ExportError`

        If you do not consume the entire response body, then you must call close
        on the response object, otherwise you will max out your available
        connections. We recommend using the `contextlib.closing
        <https://docs.python.org/2/library/contextlib.html#contextlib.closing>`_
        context manager to ensure this.
        """
        arg = files.ExportArg(path,
                              export_format)
        r = self.request(
            files.export,
            'files',
            arg,
            None,
        )
        return r

    def files_export_to_file(self,
                             download_path,
                             path,
                             export_format=None):
        """
        Export a file from a user's Dropbox. This route only supports exporting
        files that cannot be downloaded directly  and whose
        ``ExportResult.file_metadata`` has ``ExportInfo.export_as`` populated.

        Route attributes:
            scope: files.content.read

        :param str download_path: Path on local machine to save file.
        :param str path: The path of the file to be exported.
        :param Nullable[str] export_format: The file format to which the file
            should be exported. This must be one of the formats listed in the
            file's export_options returned by :meth:`files_get_metadata`. If
            none is specified, the default format (specified in export_as in
            file metadata) will be used.
        :rtype: :class:`dropbox.files.ExportResult`
        :raises: :class:`.exceptions.ApiError`

        If this raises, ApiError will contain:
            :class:`dropbox.files.ExportError`
        """
        arg = files.ExportArg(path,
                              export_format)
        r = self.request(
            files.export,
            'files',
            arg,
            None,
        )
        self._save_body_to_file(download_path, r[1])
        return r[0]

    def files_get_file_lock_batch(self,
                                  entries):
        """
        Return the lock metadata for the given list of paths.

        Route attributes:
            scope: files.content.read

        :param List[:class:`dropbox.files.LockFileArg`] entries: List of
            'entries'. Each 'entry' contains a path of the file which will be
            locked or queried. Duplicate path arguments in the batch are
            considered only once.
        :rtype: :class:`dropbox.files.LockFileBatchResult`
        :raises: :class:`.exceptions.ApiError`

        If this raises, ApiError will contain:
            :class:`dropbox.files.LockFileError`
        """
        arg = files.LockFileBatchArg(entries)
        r = self.request(
            files.get_file_lock_batch,
            'files',
            arg,
            None,
        )
        return r

    def files_get_metadata(self,
                           path,
                           include_media_info=False,
                           include_deleted=False,
                           include_has_explicit_shared_members=False,
                           include_property_groups=None):
        """
        Returns the metadata for a file or folder. Note: Metadata for the root
        folder is unsupported.

        Route attributes:
            scope: files.metadata.read

        :param str path: The path of a file or folder on Dropbox.
        :param bool include_media_info: If true, ``FileMetadata.media_info`` is
            set for photo and video.
        :param bool include_deleted: If true,
            :class:`dropbox.files.DeletedMetadata` will be returned for deleted
            file or folder, otherwise ``LookupError.not_found`` will be
            returned.
        :param bool include_has_explicit_shared_members: If true, the results
            will include a flag for each file indicating whether or not  that
            file has any explicit members.
        :param Nullable[:class:`dropbox.files.TemplateFilterBase`]
            include_property_groups: If set to a valid list of template IDs,
            ``FileMetadata.property_groups`` is set if there exists property
            data associated with the file and each of the listed templates.
        :rtype: :class:`dropbox.files.Metadata`
        :raises: :class:`.exceptions.ApiError`

        If this raises, ApiError will contain:
            :class:`dropbox.files.GetMetadataError`
        """
        arg = files.GetMetadataArg(path,
                                   include_media_info,
                                   include_deleted,
                                   include_has_explicit_shared_members,
                                   include_property_groups)
        r = self.request(
            files.get_metadata,
            'files',
            arg,
            None,
        )
        return r

    def files_get_preview(self,
                          path,
                          rev=None):
        """
        Get a preview for a file. Currently, PDF previews are generated for
        files with the following extensions: .ai, .doc, .docm, .docx, .eps,
        .gdoc, .gslides, .odp, .odt, .pps, .ppsm, .ppsx, .ppt, .pptm, .pptx,
        .rtf. HTML previews are generated for files with the following
        extensions: .csv, .ods, .xls, .xlsm, .gsheet, .xlsx. Other formats will
        return an unsupported extension error.

        Route attributes:
            scope: files.content.read

        :param str path: The path of the file to preview.
        :param Nullable[str] rev: Please specify revision in ``path`` instead.
        :rtype: (:class:`dropbox.files.FileMetadata`,
                 :class:`requests.models.Response`)
        :raises: :class:`.exceptions.ApiError`

        If this raises, ApiError will contain:
            :class:`dropbox.files.PreviewError`

        If you do not consume the entire response body, then you must call close
        on the response object, otherwise you will max out your available
        connections. We recommend using the `contextlib.closing
        <https://docs.python.org/2/library/contextlib.html#contextlib.closing>`_
        context manager to ensure this.
        """
        arg = files.PreviewArg(path,
                               rev)
        r = self.request(
            files.get_preview,
            'files',
            arg,
            None,
        )
        return r

    def files_get_preview_to_file(self,
                                  download_path,
                                  path,
                                  rev=None):
        """
        Get a preview for a file. Currently, PDF previews are generated for
        files with the following extensions: .ai, .doc, .docm, .docx, .eps,
        .gdoc, .gslides, .odp, .odt, .pps, .ppsm, .ppsx, .ppt, .pptm, .pptx,
        .rtf. HTML previews are generated for files with the following
        extensions: .csv, .ods, .xls, .xlsm, .gsheet, .xlsx. Other formats will
        return an unsupported extension error.

        Route attributes:
            scope: files.content.read

        :param str download_path: Path on local machine to save file.
        :param str path: The path of the file to preview.
        :param Nullable[str] rev: Please specify revision in ``path`` instead.
        :rtype: :class:`dropbox.files.FileMetadata`
        :raises: :class:`.exceptions.ApiError`

        If this raises, ApiError will contain:
            :class:`dropbox.files.PreviewError`
        """
        arg = files.PreviewArg(path,
                               rev)
        r = self.request(
            files.get_preview,
            'files',
            arg,
            None,
        )
        self._save_body_to_file(download_path, r[1])
        return r[0]

    def files_get_temporary_link(self,
                                 path):
        """
        Get a temporary link to stream content of a file. This link will expire
        in four hours and afterwards you will get 410 Gone. This URL should not
        be used to display content directly in the browser. The Content-Type of
        the link is determined automatically by the file's mime type.

        Route attributes:
            scope: files.content.read

        :param str path: The path to the file you want a temporary link to.
        :rtype: :class:`dropbox.files.GetTemporaryLinkResult`
        :raises: :class:`.exceptions.ApiError`

        If this raises, ApiError will contain:
            :class:`dropbox.files.GetTemporaryLinkError`
        """
        arg = files.GetTemporaryLinkArg(path)
        r = self.request(
            files.get_temporary_link,
            'files',
            arg,
            None,
        )
        return r

    def files_get_temporary_upload_link(self,
                                        commit_info,
                                        duration=14400.0):
        """
        Get a one-time use temporary upload link to upload a file to a Dropbox
        location.  This endpoint acts as a delayed :meth:`files_upload`. The
        returned temporary upload link may be used to make a POST request with
        the data to be uploaded. The upload will then be perfomed with the
        :class:`dropbox.files.CommitInfo` previously provided to
        :meth:`files_get_temporary_upload_link` but evaluated only upon
        consumption. Hence, errors stemming from invalid
        :class:`dropbox.files.CommitInfo` with respect to the state of the
        user's Dropbox will only be communicated at consumption time.
        Additionally, these errors are surfaced as generic HTTP 409 Conflict
        responses, potentially hiding issue details. The maximum temporary
        upload link duration is 4 hours. Upon consumption or expiration, a new
        link will have to be generated. Multiple links may exist for a specific
        upload path at any given time.  The POST request on the temporary upload
        link must have its Content-Type set to "application/octet-stream".
        Example temporary upload link consumption request:  curl -X POST
        https://content.dropboxapi.com/apitul/1/bNi2uIYF51cVBND --header
        "Content-Type: application/octet-stream" --data-binary @local_file.txt
        A successful temporary upload link consumption request returns the
        content hash of the uploaded data in JSON format.  Example successful
        temporary upload link consumption response: {"content-hash":
        "599d71033d700ac892a0e48fa61b125d2f5994"}  An unsuccessful temporary
        upload link consumption request returns any of the following status
        codes:  HTTP 400 Bad Request: Content-Type is not one of
        application/octet-stream and text/plain or request is invalid. HTTP 409
        Conflict: The temporary upload link does not exist or is currently
        unavailable, the upload failed, or another error happened. HTTP 410
        Gone: The temporary upload link is expired or consumed.  Example
        unsuccessful temporary upload link consumption response: Temporary
        upload link has been recently consumed.

        Route attributes:
            scope: files.content.write

        :param commit_info: Contains the path and other optional modifiers for
            the future upload commit. Equivalent to the parameters provided to
            :meth:`files_upload`.
        :type commit_info: :class:`dropbox.files.CommitInfo`
        :param float duration: How long before this link expires, in seconds.
            Attempting to start an upload with this link longer than this period
            of time after link creation will result in an error.
        :rtype: :class:`dropbox.files.GetTemporaryUploadLinkResult`
        """
        arg = files.GetTemporaryUploadLinkArg(commit_info,
                                              duration)
        r = self.request(
            files.get_temporary_upload_link,
            'files',
            arg,
            None,
        )
        return r

    def files_get_thumbnail(self,
                            path,
                            format=files.ThumbnailFormat.jpeg,
                            size=files.ThumbnailSize.w64h64,
                            mode=files.ThumbnailMode.strict):
        """
        Get a thumbnail for an image. This method currently supports files with
        the following file extensions: jpg, jpeg, png, tiff, tif, gif, webp, ppm
        and bmp. Photos that are larger than 20MB in size won't be converted to
        a thumbnail.

        Route attributes:
            scope: files.content.read

        :param str path: The path to the image file you want to thumbnail.
        :param format: The format for the thumbnail image, jpeg (default) or
            png. For  images that are photos, jpeg should be preferred, while
            png is  better for screenshots and digital arts.
        :type format: :class:`dropbox.files.ThumbnailFormat`
        :param size: The size for the thumbnail image.
        :type size: :class:`dropbox.files.ThumbnailSize`
        :param mode: How to resize and crop the image to achieve the desired
            size.
        :type mode: :class:`dropbox.files.ThumbnailMode`
        :rtype: (:class:`dropbox.files.FileMetadata`,
                 :class:`requests.models.Response`)
        :raises: :class:`.exceptions.ApiError`

        If this raises, ApiError will contain:
            :class:`dropbox.files.ThumbnailError`

        If you do not consume the entire response body, then you must call close
        on the response object, otherwise you will max out your available
        connections. We recommend using the `contextlib.closing
        <https://docs.python.org/2/library/contextlib.html#contextlib.closing>`_
        context manager to ensure this.
        """
        arg = files.ThumbnailArg(path,
                                 format,
                                 size,
                                 mode)
        r = self.request(
            files.get_thumbnail,
            'files',
            arg,
            None,
        )
        return r

    def files_get_thumbnail_to_file(self,
                                    download_path,
                                    path,
                                    format=files.ThumbnailFormat.jpeg,
                                    size=files.ThumbnailSize.w64h64,
                                    mode=files.ThumbnailMode.strict):
        """
        Get a thumbnail for an image. This method currently supports files with
        the following file extensions: jpg, jpeg, png, tiff, tif, gif, webp, ppm
        and bmp. Photos that are larger than 20MB in size won't be converted to
        a thumbnail.

        Route attributes:
            scope: files.content.read

        :param str download_path: Path on local machine to save file.
        :param str path: The path to the image file you want to thumbnail.
        :param format: The format for the thumbnail image, jpeg (default) or
            png. For  images that are photos, jpeg should be preferred, while
            png is  better for screenshots and digital arts.
        :type format: :class:`dropbox.files.ThumbnailFormat`
        :param size: The size for the thumbnail image.
        :type size: :class:`dropbox.files.ThumbnailSize`
        :param mode: How to resize and crop the image to achieve the desired
            size.
        :type mode: :class:`dropbox.files.ThumbnailMode`
        :rtype: :class:`dropbox.files.FileMetadata`
        :raises: :class:`.exceptions.ApiError`

        If this raises, ApiError will contain:
            :class:`dropbox.files.ThumbnailError`
        """
        arg = files.ThumbnailArg(path,
                                 format,
                                 size,
                                 mode)
        r = self.request(
            files.get_thumbnail,
            'files',
            arg,
            None,
        )
        self._save_body_to_file(download_path, r[1])
        return r[0]

    def files_get_thumbnail_v2(self,
                               resource,
                               format=files.ThumbnailFormat.jpeg,
                               size=files.ThumbnailSize.w64h64,
                               mode=files.ThumbnailMode.strict):
        """
        Get a thumbnail for an image. This method currently supports files with
        the following file extensions: jpg, jpeg, png, tiff, tif, gif, webp, ppm
        and bmp. Photos that are larger than 20MB in size won't be converted to
        a thumbnail.

        Route attributes:
            scope: files.content.read

        :param resource: Information specifying which file to preview. This
            could be a path to a file, a shared link pointing to a file, or a
            shared link pointing to a folder, with a relative path.
        :type resource: :class:`dropbox.files.PathOrLink`
        :param format: The format for the thumbnail image, jpeg (default) or
            png. For  images that are photos, jpeg should be preferred, while
            png is  better for screenshots and digital arts.
        :type format: :class:`dropbox.files.ThumbnailFormat`
        :param size: The size for the thumbnail image.
        :type size: :class:`dropbox.files.ThumbnailSize`
        :param mode: How to resize and crop the image to achieve the desired
            size.
        :type mode: :class:`dropbox.files.ThumbnailMode`
        :rtype: (:class:`dropbox.files.PreviewResult`,
                 :class:`requests.models.Response`)
        :raises: :class:`.exceptions.ApiError`

        If this raises, ApiError will contain:
            :class:`dropbox.files.ThumbnailV2Error`

        If you do not consume the entire response body, then you must call close
        on the response object, otherwise you will max out your available
        connections. We recommend using the `contextlib.closing
        <https://docs.python.org/2/library/contextlib.html#contextlib.closing>`_
        context manager to ensure this.
        """
        arg = files.ThumbnailV2Arg(resource,
                                   format,
                                   size,
                                   mode)
        r = self.request(
            files.get_thumbnail_v2,
            'files',
            arg,
            None,
        )
        return r

    def files_get_thumbnail_to_file_v2(self,
                                       download_path,
                                       resource,
                                       format=files.ThumbnailFormat.jpeg,
                                       size=files.ThumbnailSize.w64h64,
                                       mode=files.ThumbnailMode.strict):
        """
        Get a thumbnail for an image. This method currently supports files with
        the following file extensions: jpg, jpeg, png, tiff, tif, gif, webp, ppm
        and bmp. Photos that are larger than 20MB in size won't be converted to
        a thumbnail.

        Route attributes:
            scope: files.content.read

        :param str download_path: Path on local machine to save file.
        :param resource: Information specifying which file to preview. This
            could be a path to a file, a shared link pointing to a file, or a
            shared link pointing to a folder, with a relative path.
        :type resource: :class:`dropbox.files.PathOrLink`
        :param format: The format for the thumbnail image, jpeg (default) or
            png. For  images that are photos, jpeg should be preferred, while
            png is  better for screenshots and digital arts.
        :type format: :class:`dropbox.files.ThumbnailFormat`
        :param size: The size for the thumbnail image.
        :type size: :class:`dropbox.files.ThumbnailSize`
        :param mode: How to resize and crop the image to achieve the desired
            size.
        :type mode: :class:`dropbox.files.ThumbnailMode`
        :rtype: :class:`dropbox.files.PreviewResult`
        :raises: :class:`.exceptions.ApiError`

        If this raises, ApiError will contain:
            :class:`dropbox.files.ThumbnailV2Error`
        """
        arg = files.ThumbnailV2Arg(resource,
                                   format,
                                   size,
                                   mode)
        r = self.request(
            files.get_thumbnail_v2,
            'files',
            arg,
            None,
        )
        self._save_body_to_file(download_path, r[1])
        return r[0]

    def files_get_thumbnail_batch(self,
                                  entries):
        """
        Get thumbnails for a list of images. We allow up to 25 thumbnails in a
        single batch. This method currently supports files with the following
        file extensions: jpg, jpeg, png, tiff, tif, gif, webp, ppm and bmp.
        Photos that are larger than 20MB in size won't be converted to a
        thumbnail.

        Route attributes:
            scope: files.content.read

        :param List[:class:`dropbox.files.ThumbnailArg`] entries: List of files
            to get thumbnails.
        :rtype: :class:`dropbox.files.GetThumbnailBatchResult`
        :raises: :class:`.exceptions.ApiError`

        If this raises, ApiError will contain:
            :class:`dropbox.files.GetThumbnailBatchError`
        """
        arg = files.GetThumbnailBatchArg(entries)
        r = self.request(
            files.get_thumbnail_batch,
            'files',
            arg,
            None,
        )
        return r

    def files_list_folder(self,
                          path,
                          recursive=False,
                          include_media_info=False,
                          include_deleted=False,
                          include_has_explicit_shared_members=False,
                          include_mounted_folders=True,
                          limit=None,
                          shared_link=None,
                          include_property_groups=None,
                          include_non_downloadable_files=True):
        """
        Starts returning the contents of a folder. If the result's
        ``ListFolderResult.has_more`` field is ``True``, call
        :meth:`files_list_folder_continue` with the returned
        ``ListFolderResult.cursor`` to retrieve more entries. If you're using
        ``ListFolderArg.recursive`` set to ``True`` to keep a local cache of the
        contents of a Dropbox account, iterate through each entry in order and
        process them as follows to keep your local state in sync: For each
        :class:`dropbox.files.FileMetadata`, store the new entry at the given
        path in your local state. If the required parent folders don't exist
        yet, create them. If there's already something else at the given path,
        replace it and remove all its children. For each
        :class:`dropbox.files.FolderMetadata`, store the new entry at the given
        path in your local state. If the required parent folders don't exist
        yet, create them. If there's already something else at the given path,
        replace it but leave the children as they are. Check the new entry's
        ``FolderSharingInfo.read_only`` and set all its children's read-only
        statuses to match. For each :class:`dropbox.files.DeletedMetadata`, if
        your local state has something at the given path, remove it and all its
        children. If there's nothing at the given path, ignore this entry. Note:
        :class:`dropbox.auth.RateLimitError` may be returned if multiple
        :meth:`files_list_folder` or :meth:`files_list_folder_continue` calls
        with same parameters are made simultaneously by same API app for same
        user. If your app implements retry logic, please hold off the retry
        until the previous request finishes.

        Route attributes:
            scope: files.metadata.read

        :param str path: A unique identifier for the file.
        :param bool recursive: If true, the list folder operation will be
            applied recursively to all subfolders and the response will contain
            contents of all subfolders.
        :param bool include_media_info: If true, ``FileMetadata.media_info`` is
            set for photo and video. This parameter will no longer have an
            effect starting December 2, 2019.
        :param bool include_deleted: If true, the results will include entries
            for files and folders that used to exist but were deleted.
        :param bool include_has_explicit_shared_members: If true, the results
            will include a flag for each file indicating whether or not  that
            file has any explicit members.
        :param bool include_mounted_folders: If true, the results will include
            entries under mounted folders which includes app folder, shared
            folder and team folder.
        :param Nullable[int] limit: The maximum number of results to return per
            request. Note: This is an approximate number and there can be
            slightly more entries returned in some cases.
        :param Nullable[:class:`dropbox.files.SharedLink`] shared_link: A shared
            link to list the contents of. If the link is password-protected, the
            password must be provided. If this field is present,
            ``ListFolderArg.path`` will be relative to root of the shared link.
            Only non-recursive mode is supported for shared link.
        :param Nullable[:class:`dropbox.files.TemplateFilterBase`]
            include_property_groups: If set to a valid list of template IDs,
            ``FileMetadata.property_groups`` is set if there exists property
            data associated with the file and each of the listed templates.
        :param bool include_non_downloadable_files: If true, include files that
            are not downloadable, i.e. Google Docs.
        :rtype: :class:`dropbox.files.ListFolderResult`
        :raises: :class:`.exceptions.ApiError`

        If this raises, ApiError will contain:
            :class:`dropbox.files.ListFolderError`
        """
        arg = files.ListFolderArg(path,
                                  recursive,
                                  include_media_info,
                                  include_deleted,
                                  include_has_explicit_shared_members,
                                  include_mounted_folders,
                                  limit,
                                  shared_link,
                                  include_property_groups,
                                  include_non_downloadable_files)
        r = self.request(
            files.list_folder,
            'files',
            arg,
            None,
        )
        return r

    def files_list_folder_continue(self,
                                   cursor):
        """
        Once a cursor has been retrieved from :meth:`files_list_folder`, use
        this to paginate through all files and retrieve updates to the folder,
        following the same rules as documented for :meth:`files_list_folder`.

        Route attributes:
            scope: files.metadata.read

        :param str cursor: The cursor returned by your last call to
            :meth:`files_list_folder` or :meth:`files_list_folder_continue`.
        :rtype: :class:`dropbox.files.ListFolderResult`
        :raises: :class:`.exceptions.ApiError`

        If this raises, ApiError will contain:
            :class:`dropbox.files.ListFolderContinueError`
        """
        arg = files.ListFolderContinueArg(cursor)
        r = self.request(
            files.list_folder_continue,
            'files',
            arg,
            None,
        )
        return r

    def files_list_folder_get_latest_cursor(self,
                                            path,
                                            recursive=False,
                                            include_media_info=False,
                                            include_deleted=False,
                                            include_has_explicit_shared_members=False,
                                            include_mounted_folders=True,
                                            limit=None,
                                            shared_link=None,
                                            include_property_groups=None,
                                            include_non_downloadable_files=True):
        """
        A way to quickly get a cursor for the folder's state. Unlike
        :meth:`files_list_folder`, :meth:`files_list_folder_get_latest_cursor`
        doesn't return any entries. This endpoint is for app which only needs to
        know about new files and modifications and doesn't need to know about
        files that already exist in Dropbox.

        Route attributes:
            scope: files.metadata.read

        :param str path: A unique identifier for the file.
        :param bool recursive: If true, the list folder operation will be
            applied recursively to all subfolders and the response will contain
            contents of all subfolders.
        :param bool include_media_info: If true, ``FileMetadata.media_info`` is
            set for photo and video. This parameter will no longer have an
            effect starting December 2, 2019.
        :param bool include_deleted: If true, the results will include entries
            for files and folders that used to exist but were deleted.
        :param bool include_has_explicit_shared_members: If true, the results
            will include a flag for each file indicating whether or not  that
            file has any explicit members.
        :param bool include_mounted_folders: If true, the results will include
            entries under mounted folders which includes app folder, shared
            folder and team folder.
        :param Nullable[int] limit: The maximum number of results to return per
            request. Note: This is an approximate number and there can be
            slightly more entries returned in some cases.
        :param Nullable[:class:`dropbox.files.SharedLink`] shared_link: A shared
            link to list the contents of. If the link is password-protected, the
            password must be provided. If this field is present,
            ``ListFolderArg.path`` will be relative to root of the shared link.
            Only non-recursive mode is supported for shared link.
        :param Nullable[:class:`dropbox.files.TemplateFilterBase`]
            include_property_groups: If set to a valid list of template IDs,
            ``FileMetadata.property_groups`` is set if there exists property
            data associated with the file and each of the listed templates.
        :param bool include_non_downloadable_files: If true, include files that
            are not downloadable, i.e. Google Docs.
        :rtype: :class:`dropbox.files.ListFolderGetLatestCursorResult`
        :raises: :class:`.exceptions.ApiError`

        If this raises, ApiError will contain:
            :class:`dropbox.files.ListFolderError`
        """
        arg = files.ListFolderArg(path,
                                  recursive,
                                  include_media_info,
                                  include_deleted,
                                  include_has_explicit_shared_members,
                                  include_mounted_folders,
                                  limit,
                                  shared_link,
                                  include_property_groups,
                                  include_non_downloadable_files)
        r = self.request(
            files.list_folder_get_latest_cursor,
            'files',
            arg,
            None,
        )
        return r

    def files_list_folder_longpoll(self,
                                   cursor,
                                   timeout=30):
        """
        A longpoll endpoint to wait for changes on an account. In conjunction
        with :meth:`files_list_folder_continue`, this call gives you a
        low-latency way to monitor an account for file changes. The connection
        will block until there are changes available or a timeout occurs. This
        endpoint is useful mostly for client-side apps. If you're looking for
        server-side notifications, check out our `webhooks documentation
        <https://www.dropbox.com/developers/reference/webhooks>`_.

        Route attributes:
            scope: files.metadata.read

        :param str cursor: A cursor as returned by :meth:`files_list_folder` or
            :meth:`files_list_folder_continue`. Cursors retrieved by setting
            ``ListFolderArg.include_media_info`` to ``True`` are not supported.
        :param int timeout: A timeout in seconds. The request will block for at
            most this length of time, plus up to 90 seconds of random jitter
            added to avoid the thundering herd problem. Care should be taken
            when using this parameter, as some network infrastructure does not
            support long timeouts.
        :rtype: :class:`dropbox.files.ListFolderLongpollResult`
        :raises: :class:`.exceptions.ApiError`

        If this raises, ApiError will contain:
            :class:`dropbox.files.ListFolderLongpollError`
        """
        arg = files.ListFolderLongpollArg(cursor,
                                          timeout)
        r = self.request(
            files.list_folder_longpoll,
            'files',
            arg,
            None,
        )
        return r

    def files_list_revisions(self,
                             path,
                             mode=files.ListRevisionsMode.path,
                             limit=10):
        """
        Returns revisions for files based on a file path or a file id. The file
        path or file id is identified from the latest file entry at the given
        file path or id. This end point allows your app to query either by file
        path or file id by setting the mode parameter appropriately. In the
        ``ListRevisionsMode.path`` (default) mode, all revisions at the same
        file path as the latest file entry are returned. If revisions with the
        same file id are desired, then mode must be set to
        ``ListRevisionsMode.id``. The ``ListRevisionsMode.id`` mode is useful to
        retrieve revisions for a given file across moves or renames.

        Route attributes:
            scope: files.metadata.read

        :param str path: The path to the file you want to see the revisions of.
        :param mode: Determines the behavior of the API in listing the revisions
            for a given file path or id.
        :type mode: :class:`dropbox.files.ListRevisionsMode`
        :param int limit: The maximum number of revision entries returned.
        :rtype: :class:`dropbox.files.ListRevisionsResult`
        :raises: :class:`.exceptions.ApiError`

        If this raises, ApiError will contain:
            :class:`dropbox.files.ListRevisionsError`
        """
        arg = files.ListRevisionsArg(path,
                                     mode,
                                     limit)
        r = self.request(
            files.list_revisions,
            'files',
            arg,
            None,
        )
        return r

    def files_lock_file_batch(self,
                              entries):
        """
        Lock the files at the given paths. A locked file will be writable only
        by the lock holder. A successful response indicates that the file has
        been locked. Returns a list of the locked file paths and their metadata
        after this operation.

        Route attributes:
            scope: files.content.write

        :param List[:class:`dropbox.files.LockFileArg`] entries: List of
            'entries'. Each 'entry' contains a path of the file which will be
            locked or queried. Duplicate path arguments in the batch are
            considered only once.
        :rtype: :class:`dropbox.files.LockFileBatchResult`
        :raises: :class:`.exceptions.ApiError`

        If this raises, ApiError will contain:
            :class:`dropbox.files.LockFileError`
        """
        arg = files.LockFileBatchArg(entries)
        r = self.request(
            files.lock_file_batch,
            'files',
            arg,
            None,
        )
        return r

    def files_move_v2(self,
                      from_path,
                      to_path,
                      allow_shared_folder=False,
                      autorename=False,
                      allow_ownership_transfer=False):
        """
        Move a file or folder to a different location in the user's Dropbox. If
        the source path is a folder all its contents will be moved. Note that we
        do not currently support case-only renaming.

        Route attributes:
            scope: files.content.write

        :param bool allow_shared_folder: This flag has no effect.
        :param bool autorename: If there's a conflict, have the Dropbox server
            try to autorename the file to avoid the conflict.
        :param bool allow_ownership_transfer: Allow moves by owner even if it
            would result in an ownership transfer for the content being moved.
            This does not apply to copies.
        :rtype: :class:`dropbox.files.RelocationResult`
        :raises: :class:`.exceptions.ApiError`

        If this raises, ApiError will contain:
            :class:`dropbox.files.RelocationError`
        """
        arg = files.RelocationArg(from_path,
                                  to_path,
                                  allow_shared_folder,
                                  autorename,
                                  allow_ownership_transfer)
        r = self.request(
            files.move_v2,
            'files',
            arg,
            None,
        )
        return r

    def files_move(self,
                   from_path,
                   to_path,
                   allow_shared_folder=False,
                   autorename=False,
                   allow_ownership_transfer=False):
        """
        Move a file or folder to a different location in the user's Dropbox. If
        the source path is a folder all its contents will be moved.

        Route attributes:
            scope: files.content.write

        :param bool allow_shared_folder: This flag has no effect.
        :param bool autorename: If there's a conflict, have the Dropbox server
            try to autorename the file to avoid the conflict.
        :param bool allow_ownership_transfer: Allow moves by owner even if it
            would result in an ownership transfer for the content being moved.
            This does not apply to copies.
        :rtype: :class:`dropbox.files.Metadata`
        :raises: :class:`.exceptions.ApiError`

        If this raises, ApiError will contain:
            :class:`dropbox.files.RelocationError`
        """
        warnings.warn(
            'move is deprecated. Use move.',
            DeprecationWarning,
        )
        arg = files.RelocationArg(from_path,
                                  to_path,
                                  allow_shared_folder,
                                  autorename,
                                  allow_ownership_transfer)
        r = self.request(
            files.move,
            'files',
            arg,
            None,
        )
        return r

    def files_move_batch_v2(self,
                            entries,
                            autorename=False,
                            allow_ownership_transfer=False):
        """
        Move multiple files or folders to different locations at once in the
        user's Dropbox. Note that we do not currently support case-only
        renaming. This route will replace :meth:`files_move_batch`. The main
        difference is this route will return status for each entry, while
        :meth:`files_move_batch` raises failure if any entry fails. This route
        will either finish synchronously, or return a job ID and do the async
        move job in background. Please use :meth:`files_move_batch_check_v2` to
        check the job status.

        Route attributes:
            scope: files.content.write

        :param bool allow_ownership_transfer: Allow moves by owner even if it
            would result in an ownership transfer for the content being moved.
            This does not apply to copies.
        :rtype: :class:`dropbox.files.RelocationBatchV2Launch`
        """
        arg = files.MoveBatchArg(entries,
                                 autorename,
                                 allow_ownership_transfer)
        r = self.request(
            files.move_batch_v2,
            'files',
            arg,
            None,
        )
        return r

    def files_move_batch(self,
                         entries,
                         autorename=False,
                         allow_shared_folder=False,
                         allow_ownership_transfer=False):
        """
        Move multiple files or folders to different locations at once in the
        user's Dropbox. This route will return job ID immediately and do the
        async moving job in background. Please use
        :meth:`files_move_batch_check` to check the job status.

        Route attributes:
            scope: files.content.write

        :param bool allow_shared_folder: This flag has no effect.
        :param bool allow_ownership_transfer: Allow moves by owner even if it
            would result in an ownership transfer for the content being moved.
            This does not apply to copies.
        :rtype: :class:`dropbox.files.RelocationBatchLaunch`
        """
        warnings.warn(
            'move_batch is deprecated. Use move_batch.',
            DeprecationWarning,
        )
        arg = files.RelocationBatchArg(entries,
                                       autorename,
                                       allow_shared_folder,
                                       allow_ownership_transfer)
        r = self.request(
            files.move_batch,
            'files',
            arg,
            None,
        )
        return r

    def files_move_batch_check_v2(self,
                                  async_job_id):
        """
        Returns the status of an asynchronous job for
        :meth:`files_move_batch_v2`. It returns list of results for each entry.

        Route attributes:
            scope: files.content.write

        :param str async_job_id: Id of the asynchronous job. This is the value
            of a response returned from the method that launched the job.
        :rtype: :class:`dropbox.files.RelocationBatchV2JobStatus`
        :raises: :class:`.exceptions.ApiError`

        If this raises, ApiError will contain:
            :class:`dropbox.files.PollError`
        """
        arg = async_.PollArg(async_job_id)
        r = self.request(
            files.move_batch_check_v2,
            'files',
            arg,
            None,
        )
        return r

    def files_move_batch_check(self,
                               async_job_id):
        """
        Returns the status of an asynchronous job for :meth:`files_move_batch`.
        If success, it returns list of results for each entry.

        Route attributes:
            scope: files.content.write

        :param str async_job_id: Id of the asynchronous job. This is the value
            of a response returned from the method that launched the job.
        :rtype: :class:`dropbox.files.RelocationBatchJobStatus`
        :raises: :class:`.exceptions.ApiError`

        If this raises, ApiError will contain:
            :class:`dropbox.files.PollError`
        """
        warnings.warn(
            'move_batch/check is deprecated. Use move_batch/check.',
            DeprecationWarning,
        )
        arg = async_.PollArg(async_job_id)
        r = self.request(
            files.move_batch_check,
            'files',
            arg,
            None,
        )
        return r

    def files_paper_create(self,
                           f,
                           path,
                           import_format):
        """
        Creates a new Paper doc with the provided content.

        Route attributes:
            scope: files.content.write

        :param bytes f: Contents to upload.
        :param str path: The fully qualified path to the location in the user's
            Dropbox where the Paper Doc should be created. This should include
            the document's title and end with .paper.
        :param import_format: The format of the provided data.
        :type import_format: :class:`dropbox.files.ImportFormat`
        :rtype: :class:`dropbox.files.PaperCreateResult`
        :raises: :class:`.exceptions.ApiError`

        If this raises, ApiError will contain:
            :class:`dropbox.files.PaperCreateError`
        """
        arg = files.PaperCreateArg(path,
                                   import_format)
        r = self.request(
            files.paper_create,
            'files',
            arg,
            f,
        )
        return r

    def files_paper_update(self,
                           f,
                           path,
                           import_format,
                           doc_update_policy,
                           paper_revision=None):
        """
        Updates an existing Paper doc with the provided content.

        Route attributes:
            scope: files.content.write

        :param bytes f: Contents to upload.
        :param str path: Path in the user's Dropbox to update. The path must
            correspond to a Paper doc or an error will be returned.
        :param import_format: The format of the provided data.
        :type import_format: :class:`dropbox.files.ImportFormat`
        :param doc_update_policy: How the provided content should be applied to
            the doc.
        :type doc_update_policy: :class:`dropbox.files.PaperDocUpdatePolicy`
        :param Nullable[int] paper_revision: The latest doc revision. Required
            when doc_update_policy is update. This value must match the current
            revision of the doc or error revision_mismatch will be returned.
        :rtype: :class:`dropbox.files.PaperUpdateResult`
        :raises: :class:`.exceptions.ApiError`

        If this raises, ApiError will contain:
            :class:`dropbox.files.PaperUpdateError`
        """
        arg = files.PaperUpdateArg(path,
                                   import_format,
                                   doc_update_policy,
                                   paper_revision)
        r = self.request(
            files.paper_update,
            'files',
            arg,
            f,
        )
        return r

    def files_permanently_delete(self,
                                 path,
                                 parent_rev=None):
        """
        Permanently delete the file or folder at a given path (see
        https://www.dropbox.com/en/help/40). If the given file or folder is not
        yet deleted, this route will first delete it. It is possible for this
        route to successfully delete, then fail to permanently delete. Note:
        This endpoint is only available for Dropbox Business apps.

        Route attributes:
            scope: files.permanent_delete

        :param str path: Path in the user's Dropbox to delete.
        :param Nullable[str] parent_rev: Perform delete if given "rev" matches
            the existing file's latest "rev". This field does not support
            deleting a folder.
        :rtype: None
        :raises: :class:`.exceptions.ApiError`

        If this raises, ApiError will contain:
            :class:`dropbox.files.DeleteError`
        """
        arg = files.DeleteArg(path,
                              parent_rev)
        r = self.request(
            files.permanently_delete,
            'files',
            arg,
            None,
        )
        return None

    def files_properties_add(self,
                             path,
                             property_groups):
        """
        Route attributes:
            scope: files.metadata.write

        :param str path: A unique identifier for the file or folder.
        :param List[:class:`dropbox.files.PropertyGroup`] property_groups: The
            property groups which are to be added to a Dropbox file. No two
            groups in the input should  refer to the same template.
        :rtype: None
        :raises: :class:`.exceptions.ApiError`

        If this raises, ApiError will contain:
            :class:`dropbox.files.AddPropertiesError`
        """
        warnings.warn(
            'properties/add is deprecated.',
            DeprecationWarning,
        )
        arg = file_properties.AddPropertiesArg(path,
                                               property_groups)
        r = self.request(
            files.properties_add,
            'files',
            arg,
            None,
        )
        return None

    def files_properties_overwrite(self,
                                   path,
                                   property_groups):
        """
        Route attributes:
            scope: files.metadata.write

        :param str path: A unique identifier for the file or folder.
        :param List[:class:`dropbox.files.PropertyGroup`] property_groups: The
            property groups "snapshot" updates to force apply. No two groups in
            the input should  refer to the same template.
        :rtype: None
        :raises: :class:`.exceptions.ApiError`

        If this raises, ApiError will contain:
            :class:`dropbox.files.InvalidPropertyGroupError`
        """
        warnings.warn(
            'properties/overwrite is deprecated.',
            DeprecationWarning,
        )
        arg = file_properties.OverwritePropertyGroupArg(path,
                                                        property_groups)
        r = self.request(
            files.properties_overwrite,
            'files',
            arg,
            None,
        )
        return None

    def files_properties_remove(self,
                                path,
                                property_template_ids):
        """
        Route attributes:
            scope: files.metadata.write

        :param str path: A unique identifier for the file or folder.
        :param List[str] property_template_ids: A list of identifiers for a
            template created by :meth:`files_templates_add_for_user` or
            :meth:`files_templates_add_for_team`.
        :rtype: None
        :raises: :class:`.exceptions.ApiError`

        If this raises, ApiError will contain:
            :class:`dropbox.files.RemovePropertiesError`
        """
        warnings.warn(
            'properties/remove is deprecated.',
            DeprecationWarning,
        )
        arg = file_properties.RemovePropertiesArg(path,
                                                  property_template_ids)
        r = self.request(
            files.properties_remove,
            'files',
            arg,
            None,
        )
        return None

    def files_properties_template_get(self,
                                      template_id):
        """
        Route attributes:
            scope: files.metadata.read

        :param str template_id: An identifier for template added by route  See
            :meth:`files_templates_add_for_user` or
            :meth:`files_templates_add_for_team`.
        :rtype: :class:`dropbox.files.GetTemplateResult`
        :raises: :class:`.exceptions.ApiError`

        If this raises, ApiError will contain:
            :class:`dropbox.files.TemplateError`
        """
        warnings.warn(
            'properties/template/get is deprecated.',
            DeprecationWarning,
        )
        arg = file_properties.GetTemplateArg(template_id)
        r = self.request(
            files.properties_template_get,
            'files',
            arg,
            None,
        )
        return r

    def files_properties_template_list(self):
        """
        Route attributes:
            scope: files.metadata.read

        :rtype: :class:`dropbox.files.ListTemplateResult`
        :raises: :class:`.exceptions.ApiError`

        If this raises, ApiError will contain:
            :class:`dropbox.files.TemplateError`
        """
        warnings.warn(
            'properties/template/list is deprecated.',
            DeprecationWarning,
        )
        arg = None
        r = self.request(
            files.properties_template_list,
            'files',
            arg,
            None,
        )
        return r

    def files_properties_update(self,
                                path,
                                update_property_groups):
        """
        Route attributes:
            scope: files.metadata.write

        :param str path: A unique identifier for the file or folder.
        :param List[:class:`dropbox.files.PropertyGroupUpdate`]
            update_property_groups: The property groups "delta" updates to
            apply.
        :rtype: None
        :raises: :class:`.exceptions.ApiError`

        If this raises, ApiError will contain:
            :class:`dropbox.files.UpdatePropertiesError`
        """
        warnings.warn(
            'properties/update is deprecated.',
            DeprecationWarning,
        )
        arg = file_properties.UpdatePropertiesArg(path,
                                                  update_property_groups)
        r = self.request(
            files.properties_update,
            'files',
            arg,
            None,
        )
        return None

    def files_restore(self,
                      path,
                      rev):
        """
        Restore a specific revision of a file to the given path.

        Route attributes:
            scope: files.content.write

        :param str path: The path to save the restored file.
        :param str rev: The revision to restore.
        :rtype: :class:`dropbox.files.FileMetadata`
        :raises: :class:`.exceptions.ApiError`

        If this raises, ApiError will contain:
            :class:`dropbox.files.RestoreError`
        """
        arg = files.RestoreArg(path,
                               rev)
        r = self.request(
            files.restore,
            'files',
            arg,
            None,
        )
        return r

    def files_save_url(self,
                       path,
                       url):
        """
        Save the data from a specified URL into a file in user's Dropbox. Note
        that the transfer from the URL must complete within 5 minutes, or the
        operation will time out and the job will fail. If the given path already
        exists, the file will be renamed to avoid the conflict (e.g. myfile
        (1).txt).

        Route attributes:
            scope: files.content.write

        :param str path: The path in Dropbox where the URL will be saved to.
        :param str url: The URL to be saved.
        :rtype: :class:`dropbox.files.SaveUrlResult`
        :raises: :class:`.exceptions.ApiError`

        If this raises, ApiError will contain:
            :class:`dropbox.files.SaveUrlError`
        """
        arg = files.SaveUrlArg(path,
                               url)
        r = self.request(
            files.save_url,
            'files',
            arg,
            None,
        )
        return r

    def files_save_url_check_job_status(self,
                                        async_job_id):
        """
        Check the status of a :meth:`files_save_url` job.

        Route attributes:
            scope: files.content.write

        :param str async_job_id: Id of the asynchronous job. This is the value
            of a response returned from the method that launched the job.
        :rtype: :class:`dropbox.files.SaveUrlJobStatus`
        :raises: :class:`.exceptions.ApiError`

        If this raises, ApiError will contain:
            :class:`dropbox.files.PollError`
        """
        arg = async_.PollArg(async_job_id)
        r = self.request(
            files.save_url_check_job_status,
            'files',
            arg,
            None,
        )
        return r

    def files_search(self,
                     path,
                     query,
                     start=0,
                     max_results=100,
                     mode=files.SearchMode.filename):
        """
        Searches for files and folders. Note: Recent changes will be reflected
        in search results within a few seconds and older revisions of existing
        files may still match your query for up to a few days.

        Route attributes:
            scope: files.metadata.read

        :param str path: The path in the user's Dropbox to search. Should
            probably be a folder.
        :param str query: The string to search for. Query string may be
            rewritten to improve relevance of results. The string is split on
            spaces into multiple tokens. For file name searching, the last token
            is used for prefix matching (i.e. "bat c" matches "bat cave" but not
            "batman car").
        :param int start: The starting index within the search results (used for
            paging).
        :param int max_results: The maximum number of search results to return.
        :param mode: The search mode (filename, filename_and_content, or
            deleted_filename). Note that searching file content is only
            available for Dropbox Business accounts.
        :type mode: :class:`dropbox.files.SearchMode`
        :rtype: :class:`dropbox.files.SearchResult`
        :raises: :class:`.exceptions.ApiError`

        If this raises, ApiError will contain:
            :class:`dropbox.files.SearchError`
        """
        warnings.warn(
            'search is deprecated. Use search.',
            DeprecationWarning,
        )
        arg = files.SearchArg(path,
                              query,
                              start,
                              max_results,
                              mode)
        r = self.request(
            files.search,
            'files',
            arg,
            None,
        )
        return r

    def files_search_v2(self,
                        query,
                        options=None,
                        match_field_options=None,
                        include_highlights=None):
        """
        Searches for files and folders. Note: :meth:`files_search_v2` along with
        :meth:`files_search_continue_v2` can only be used to retrieve a maximum
        of 10,000 matches. Recent changes may not immediately be reflected in
        search results due to a short delay in indexing. Duplicate results may
        be returned across pages. Some results may not be returned.

        Route attributes:
            scope: files.metadata.read

        :param str query: The string to search for. May match across multiple
            fields based on the request arguments.
        :param Nullable[:class:`dropbox.files.SearchOptions`] options: Options
            for more targeted search results.
        :param Nullable[:class:`dropbox.files.SearchMatchFieldOptions`]
            match_field_options: Options for search results match fields.
        :param Nullable[bool] include_highlights: Deprecated and moved this
            option to SearchMatchFieldOptions.
        :rtype: :class:`dropbox.files.SearchV2Result`
        :raises: :class:`.exceptions.ApiError`

        If this raises, ApiError will contain:
            :class:`dropbox.files.SearchError`
        """
        arg = files.SearchV2Arg(query,
                                options,
                                match_field_options,
                                include_highlights)
        r = self.request(
            files.search_v2,
            'files',
            arg,
            None,
        )
        return r

    def files_search_continue_v2(self,
                                 cursor):
        """
        Fetches the next page of search results returned from
        :meth:`files_search_v2`. Note: :meth:`files_search_v2` along with
        :meth:`files_search_continue_v2` can only be used to retrieve a maximum
        of 10,000 matches. Recent changes may not immediately be reflected in
        search results due to a short delay in indexing. Duplicate results may
        be returned across pages. Some results may not be returned.

        Route attributes:
            scope: files.metadata.read

        :param str cursor: The cursor returned by your last call to
            :meth:`files_search_v2`. Used to fetch the next page of results.
        :rtype: :class:`dropbox.files.SearchV2Result`
        :raises: :class:`.exceptions.ApiError`

        If this raises, ApiError will contain:
            :class:`dropbox.files.SearchError`
        """
        arg = files.SearchV2ContinueArg(cursor)
        r = self.request(
            files.search_continue_v2,
            'files',
            arg,
            None,
        )
        return r

    def files_tags_add(self,
                       path,
                       tag_text):
        """
        Add a tag to an item. A tag is a string. The strings are automatically
        converted to lowercase letters. No more than 20 tags can be added to a
        given item.

        Route attributes:
            scope: files.metadata.write

        :param str path: Path to the item to be tagged.
        :param str tag_text: The value of the tag to add. Will be automatically
            converted to lowercase letters.
        :rtype: None
        :raises: :class:`.exceptions.ApiError`

        If this raises, ApiError will contain:
            :class:`dropbox.files.AddTagError`
        """
        arg = files.AddTagArg(path,
                              tag_text)
        r = self.request(
            files.tags_add,
            'files',
            arg,
            None,
        )
        return None

    def files_tags_get(self,
                       paths):
        """
        Get list of tags assigned to items.

        Route attributes:
            scope: files.metadata.read

        :param List[str] paths: Path to the items.
        :rtype: :class:`dropbox.files.GetTagsResult`
        :raises: :class:`.exceptions.ApiError`

        If this raises, ApiError will contain:
            :class:`dropbox.files.BaseTagError`
        """
        arg = files.GetTagsArg(paths)
        r = self.request(
            files.tags_get,
            'files',
            arg,
            None,
        )
        return r

    def files_tags_remove(self,
                          path,
                          tag_text):
        """
        Remove a tag from an item.

        Route attributes:
            scope: files.metadata.write

        :param str path: Path to the item to tag.
        :param str tag_text: The tag to remove. Will be automatically converted
            to lowercase letters.
        :rtype: None
        :raises: :class:`.exceptions.ApiError`

        If this raises, ApiError will contain:
            :class:`dropbox.files.RemoveTagError`
        """
        arg = files.RemoveTagArg(path,
                                 tag_text)
        r = self.request(
            files.tags_remove,
            'files',
            arg,
            None,
        )
        return None

    def files_unlock_file_batch(self,
                                entries):
        """
        Unlock the files at the given paths. A locked file can only be unlocked
        by the lock holder or, if a business account, a team admin. A successful
        response indicates that the file has been unlocked. Returns a list of
        the unlocked file paths and their metadata after this operation.

        Route attributes:
            scope: files.content.write

        :param List[:class:`dropbox.files.UnlockFileArg`] entries: List of
            'entries'. Each 'entry' contains a path of the file which will be
            unlocked. Duplicate path arguments in the batch are considered only
            once.
        :rtype: :class:`dropbox.files.LockFileBatchResult`
        :raises: :class:`.exceptions.ApiError`

        If this raises, ApiError will contain:
            :class:`dropbox.files.LockFileError`
        """
        arg = files.UnlockFileBatchArg(entries)
        r = self.request(
            files.unlock_file_batch,
            'files',
            arg,
            None,
        )
        return r

    def files_upload(self,
                     f,
                     path,
                     mode=files.WriteMode.add,
                     autorename=False,
                     client_modified=None,
                     mute=False,
                     property_groups=None,
                     strict_conflict=False,
                     content_hash=None):
        """
        Create a new file with the contents provided in the request. Do not use
        this to upload a file larger than 150 MB. Instead, create an upload
        session with :meth:`files_upload_session_start`. Calls to this endpoint
        will count as data transport calls for any Dropbox Business teams with a
        limit on the number of data transport calls allowed per month. For more
        information, see the `Data transport limit page
        <https://www.dropbox.com/developers/reference/data-transport-limit>`_.

        Route attributes:
            scope: files.content.write

        :param bytes f: Contents to upload.
        :param Nullable[str] content_hash: A hash of the file content uploaded
            in this call. If provided and the uploaded content does not match
            this hash, an error will be returned. For more information see our
            `Content hash
            <https://www.dropbox.com/developers/reference/content-hash>`_ page.
        :rtype: :class:`dropbox.files.FileMetadata`
        :raises: :class:`.exceptions.ApiError`

        If this raises, ApiError will contain:
            :class:`dropbox.files.UploadError`
        """
        arg = files.UploadArg(path,
                              mode,
                              autorename,
                              client_modified,
                              mute,
                              property_groups,
                              strict_conflict,
                              content_hash)
        r = self.request(
            files.upload,
            'files',
            arg,
            f,
        )
        return r

    def files_upload_session_append_v2(self,
                                       f,
                                       cursor,
                                       close=False,
                                       content_hash=None):
        """
        Append more data to an upload session. When the parameter close is set,
        this call will close the session. A single request should not upload
        more than 150 MB. The maximum size of a file one can upload to an upload
        session is 350 GB. Calls to this endpoint will count as data transport
        calls for any Dropbox Business teams with a limit on the number of data
        transport calls allowed per month. For more information, see the `Data
        transport limit page
        <https://www.dropbox.com/developers/reference/data-transport-limit>`_.

        Route attributes:
            scope: files.content.write

        :param bytes f: Contents to upload.
        :param cursor: Contains the upload session ID and the offset.
        :type cursor: :class:`dropbox.files.UploadSessionCursor`
        :param bool close: If true, the current session will be closed, at which
            point you won't be able to call
            :meth:`files_upload_session_append_v2` anymore with the current
            session.
        :param Nullable[str] content_hash: A hash of the file content uploaded
            in this call. If provided and the uploaded content does not match
            this hash, an error will be returned. For more information see our
            `Content hash
            <https://www.dropbox.com/developers/reference/content-hash>`_ page.
        :rtype: None
        :raises: :class:`.exceptions.ApiError`

        If this raises, ApiError will contain:
            :class:`dropbox.files.UploadSessionAppendError`
        """
        arg = files.UploadSessionAppendArg(cursor,
                                           close,
                                           content_hash)
        r = self.request(
            files.upload_session_append_v2,
            'files',
            arg,
            f,
        )
        return None

    def files_upload_session_append(self,
                                    f,
                                    session_id,
                                    offset):
        """
        Append more data to an upload session. A single request should not
        upload more than 150 MB. The maximum size of a file one can upload to an
        upload session is 350 GB. Calls to this endpoint will count as data
        transport calls for any Dropbox Business teams with a limit on the
        number of data transport calls allowed per month. For more information,
        see the `Data transport limit page
        <https://www.dropbox.com/developers/reference/data-transport-limit>`_.

        Route attributes:
            scope: files.content.write

        :param bytes f: Contents to upload.
        :param str session_id: The upload session ID (returned by
            :meth:`files_upload_session_start`).
        :param int offset: Offset in bytes at which data should be appended. We
            use this to make sure upload data isn't lost or duplicated in the
            event of a network error.
        :rtype: None
        :raises: :class:`.exceptions.ApiError`

        If this raises, ApiError will contain:
            :class:`dropbox.files.UploadSessionAppendError`
        """
        warnings.warn(
            'upload_session/append is deprecated. Use upload_session/append.',
            DeprecationWarning,
        )
        arg = files.UploadSessionCursor(session_id,
                                        offset)
        r = self.request(
            files.upload_session_append,
            'files',
            arg,
            f,
        )
        return None

    def files_upload_session_finish(self,
                                    f,
                                    cursor,
                                    commit,
                                    content_hash=None):
        """
        Finish an upload session and save the uploaded data to the given file
        path. A single request should not upload more than 150 MB. The maximum
        size of a file one can upload to an upload session is 350 GB. Calls to
        this endpoint will count as data transport calls for any Dropbox
        Business teams with a limit on the number of data transport calls
        allowed per month. For more information, see the `Data transport limit
        page
        <https://www.dropbox.com/developers/reference/data-transport-limit>`_.

        Route attributes:
            scope: files.content.write

        :param bytes f: Contents to upload.
        :param cursor: Contains the upload session ID and the offset.
        :type cursor: :class:`dropbox.files.UploadSessionCursor`
        :param commit: Contains the path and other optional modifiers for the
            commit.
        :type commit: :class:`dropbox.files.CommitInfo`
        :param Nullable[str] content_hash: A hash of the file content uploaded
            in this call. If provided and the uploaded content does not match
            this hash, an error will be returned. For more information see our
            `Content hash
            <https://www.dropbox.com/developers/reference/content-hash>`_ page.
        :rtype: :class:`dropbox.files.FileMetadata`
        :raises: :class:`.exceptions.ApiError`

        If this raises, ApiError will contain:
            :class:`dropbox.files.UploadSessionFinishError`
        """
        arg = files.UploadSessionFinishArg(cursor,
                                           commit,
                                           content_hash)
        r = self.request(
            files.upload_session_finish,
            'files',
            arg,
            f,
        )
        return r

    def files_upload_session_finish_batch(self,
                                          entries):
        """
        This route helps you commit many files at once into a user's Dropbox.
        Use :meth:`files_upload_session_start` and
        :meth:`files_upload_session_append_v2` to upload file contents. We
        recommend uploading many files in parallel to increase throughput. Once
        the file contents have been uploaded, rather than calling
        :meth:`files_upload_session_finish`, use this route to finish all your
        upload sessions in a single request. ``UploadSessionStartArg.close`` or
        ``UploadSessionAppendArg.close`` needs to be true for the last
        :meth:`files_upload_session_start` or
        :meth:`files_upload_session_append_v2` call. The maximum size of a file
        one can upload to an upload session is 350 GB. This route will return a
        job_id immediately and do the async commit job in background. Use
        :meth:`files_upload_session_finish_batch_check` to check the job status.
        For the same account, this route should be executed serially. That means
        you should not start the next job before current job finishes. We allow
        up to 1000 entries in a single request. Calls to this endpoint will
        count as data transport calls for any Dropbox Business teams with a
        limit on the number of data transport calls allowed per month. For more
        information, see the `Data transport limit page
        <https://www.dropbox.com/developers/reference/data-transport-limit>`_.

        Route attributes:
            scope: files.content.write

        :param List[:class:`dropbox.files.UploadSessionFinishArg`] entries:
            Commit information for each file in the batch.
        :rtype: :class:`dropbox.files.UploadSessionFinishBatchLaunch`
        """
        warnings.warn(
            'upload_session/finish_batch is deprecated. Use upload_session/finish_batch.',
            DeprecationWarning,
        )
        arg = files.UploadSessionFinishBatchArg(entries)
        r = self.request(
            files.upload_session_finish_batch,
            'files',
            arg,
            None,
        )
        return r

    def files_upload_session_finish_batch_v2(self,
                                             entries):
        """
        This route helps you commit many files at once into a user's Dropbox.
        Use :meth:`files_upload_session_start` and
        :meth:`files_upload_session_append_v2` to upload file contents. We
        recommend uploading many files in parallel to increase throughput. Once
        the file contents have been uploaded, rather than calling
        :meth:`files_upload_session_finish`, use this route to finish all your
        upload sessions in a single request. ``UploadSessionStartArg.close`` or
        ``UploadSessionAppendArg.close`` needs to be true for the last
        :meth:`files_upload_session_start` or
        :meth:`files_upload_session_append_v2` call of each upload session. The
        maximum size of a file one can upload to an upload session is 350 GB. We
        allow up to 1000 entries in a single request. Calls to this endpoint
        will count as data transport calls for any Dropbox Business teams with a
        limit on the number of data transport calls allowed per month. For more
        information, see the `Data transport limit page
        <https://www.dropbox.com/developers/reference/data-transport-limit>`_.

        Route attributes:
            scope: files.content.write

        :param List[:class:`dropbox.files.UploadSessionFinishArg`] entries:
            Commit information for each file in the batch.
        :rtype: :class:`dropbox.files.UploadSessionFinishBatchResult`
        """
        arg = files.UploadSessionFinishBatchArg(entries)
        r = self.request(
            files.upload_session_finish_batch_v2,
            'files',
            arg,
            None,
        )
        return r

    def files_upload_session_finish_batch_check(self,
                                                async_job_id):
        """
        Returns the status of an asynchronous job for
        :meth:`files_upload_session_finish_batch`. If success, it returns list
        of result for each entry.

        Route attributes:
            scope: files.content.write

        :param str async_job_id: Id of the asynchronous job. This is the value
            of a response returned from the method that launched the job.
        :rtype: :class:`dropbox.files.UploadSessionFinishBatchJobStatus`
        :raises: :class:`.exceptions.ApiError`

        If this raises, ApiError will contain:
            :class:`dropbox.files.PollError`
        """
        arg = async_.PollArg(async_job_id)
        r = self.request(
            files.upload_session_finish_batch_check,
            'files',
            arg,
            None,
        )
        return r

    def files_upload_session_start(self,
                                   f,
                                   close=False,
                                   session_type=None,
                                   content_hash=None):
        """
        Upload sessions allow you to upload a single file in one or more
        requests, for example where the size of the file is greater than 150 MB.
        This call starts a new upload session with the given data. You can then
        use :meth:`files_upload_session_append_v2` to add more data and
        :meth:`files_upload_session_finish` to save all the data to a file in
        Dropbox. A single request should not upload more than 150 MB. The
        maximum size of a file one can upload to an upload session is 350 GB. An
        upload session can be used for a maximum of 7 days. Attempting to use an
        ``UploadSessionStartResult.session_id`` with
        :meth:`files_upload_session_append_v2` or
        :meth:`files_upload_session_finish` more than 7 days after its creation
        will return a ``UploadSessionLookupError.not_found``. Calls to this
        endpoint will count as data transport calls for any Dropbox Business
        teams with a limit on the number of data transport calls allowed per
        month. For more information, see the `Data transport limit page
        <https://www.dropbox.com/developers/reference/data-transport-limit>`_.
        By default, upload sessions require you to send content of the file in
        sequential order via consecutive :meth:`files_upload_session_start`,
        :meth:`files_upload_session_append_v2`,
        :meth:`files_upload_session_finish` calls. For better performance, you
        can instead optionally use a ``UploadSessionType.concurrent`` upload
        session. To start a new concurrent session, set
        ``UploadSessionStartArg.session_type`` to
        ``UploadSessionType.concurrent``. After that, you can send file data in
        concurrent :meth:`files_upload_session_append_v2` requests. Finally
        finish the session with :meth:`files_upload_session_finish`. There are
        couple of constraints with concurrent sessions to make them work. You
        can not send data with :meth:`files_upload_session_start` or
        :meth:`files_upload_session_finish` call, only with
        :meth:`files_upload_session_append_v2` call. Also data uploaded in
        :meth:`files_upload_session_append_v2` call must be multiple of 4194304
        bytes (except for last :meth:`files_upload_session_append_v2` with
        ``UploadSessionStartArg.close`` to ``True``, that may contain any
        remaining data).

        Route attributes:
            scope: files.content.write

        :param bytes f: Contents to upload.
        :param bool close: If true, the current session will be closed, at which
            point you won't be able to call
            :meth:`files_upload_session_append_v2` anymore with the current
            session.
        :param Nullable[:class:`dropbox.files.UploadSessionType`] session_type:
            Type of upload session you want to start. If not specified, default
            is ``UploadSessionType.sequential``.
        :param Nullable[str] content_hash: A hash of the file content uploaded
            in this call. If provided and the uploaded content does not match
            this hash, an error will be returned. For more information see our
            `Content hash
            <https://www.dropbox.com/developers/reference/content-hash>`_ page.
        :rtype: :class:`dropbox.files.UploadSessionStartResult`
        :raises: :class:`.exceptions.ApiError`

        If this raises, ApiError will contain:
            :class:`dropbox.files.UploadSessionStartError`
        """
        arg = files.UploadSessionStartArg(close,
                                          session_type,
                                          content_hash)
        r = self.request(
            files.upload_session_start,
            'files',
            arg,
            f,
        )
        return r

    def files_upload_session_start_batch(self,
                                         num_sessions,
                                         session_type=None):
        """
        This route starts batch of upload_sessions. Please refer to
        `upload_session/start` usage. Calls to this endpoint will count as data
        transport calls for any Dropbox Business teams with a limit on the
        number of data transport calls allowed per month. For more information,
        see the `Data transport limit page
        <https://www.dropbox.com/developers/reference/data-transport-limit>`_.

        Route attributes:
            scope: files.content.write

        :param Nullable[:class:`dropbox.files.UploadSessionType`] session_type:
            Type of upload session you want to start. If not specified, default
            is ``UploadSessionType.sequential``.
        :param int num_sessions: The number of upload sessions to start.
        :rtype: :class:`dropbox.files.UploadSessionStartBatchResult`
        """
        arg = files.UploadSessionStartBatchArg(num_sessions,
                                               session_type)
        r = self.request(
            files.upload_session_start_batch,
            'files',
            arg,
            None,
        )
        return r

    # ------------------------------------------
    # Routes in openid namespace

    def openid_userinfo(self):
        """
        This route is used for refreshing the info that is found in the id_token
        during the OIDC flow. This route doesn't require any arguments and will
        use the scopes approved for the given access token.

        Route attributes:
            scope: openid

        :rtype: :class:`dropbox.openid.UserInfoResult`
        :raises: :class:`.exceptions.ApiError`

        If this raises, ApiError will contain:
            :class:`dropbox.openid.UserInfoError`
        """
        arg = openid.UserInfoArgs()
        r = self.request(
            openid.userinfo,
            'openid',
            arg,
            None,
        )
        return r

    # ------------------------------------------
    # Routes in paper namespace

    def paper_docs_archive(self,
                           doc_id):
        """
        Marks the given Paper doc as archived. This action can be performed or
        undone by anyone with edit permissions to the doc. Note that this
        endpoint will continue to work for content created by users on the older
        version of Paper. To check which version of Paper a user is on, use
        /users/features/get_values. If the paper_as_files feature is enabled,
        then the user is running the new version of Paper. This endpoint will be
        retired in September 2020. Refer to the `Paper Migration Guide
        <https://www.dropbox.com/lp/developers/reference/paper-migration-guide>`_
        for more information.

        Route attributes:
            scope: files.content.write

        :param str doc_id: The Paper doc ID.
        :rtype: None
        :raises: :class:`.exceptions.ApiError`

        If this raises, ApiError will contain:
            :class:`dropbox.paper.DocLookupError`
        """
        warnings.warn(
            'docs/archive is deprecated.',
            DeprecationWarning,
        )
        arg = paper.RefPaperDoc(doc_id)
        r = self.request(
            paper.docs_archive,
            'paper',
            arg,
            None,
        )
        return None

    def paper_docs_create(self,
                          f,
                          import_format,
                          parent_folder_id=None):
        """
        Creates a new Paper doc with the provided content. Note that this
        endpoint will continue to work for content created by users on the older
        version of Paper. To check which version of Paper a user is on, use
        /users/features/get_values. If the paper_as_files feature is enabled,
        then the user is running the new version of Paper. This endpoint will be
        retired in September 2020. Refer to the `Paper Migration Guide
        <https://www.dropbox.com/lp/developers/reference/paper-migration-guide>`_
        for more information.

        Route attributes:
            scope: files.content.write

        :param bytes f: Contents to upload.
        :param Nullable[str] parent_folder_id: The Paper folder ID where the
            Paper document should be created. The API user has to have write
            access to this folder or error is thrown.
        :param import_format: The format of provided data.
        :type import_format: :class:`dropbox.paper.ImportFormat`
        :rtype: :class:`dropbox.paper.PaperDocCreateUpdateResult`
        :raises: :class:`.exceptions.ApiError`

        If this raises, ApiError will contain:
            :class:`dropbox.paper.PaperDocCreateError`
        """
        warnings.warn(
            'docs/create is deprecated.',
            DeprecationWarning,
        )
        arg = paper.PaperDocCreateArgs(import_format,
                                       parent_folder_id)
        r = self.request(
            paper.docs_create,
            'paper',
            arg,
            f,
        )
        return r

    def paper_docs_download(self,
                            doc_id,
                            export_format):
        """
        Exports and downloads Paper doc either as HTML or markdown. Note that
        this endpoint will continue to work for content created by users on the
        older version of Paper. To check which version of Paper a user is on,
        use /users/features/get_values. If the paper_as_files feature is
        enabled, then the user is running the new version of Paper. Refer to the
        `Paper Migration Guide
        <https://www.dropbox.com/lp/developers/reference/paper-migration-guide>`_
        for migration information.

        Route attributes:
            scope: files.content.read

        :type export_format: :class:`dropbox.paper.ExportFormat`
        :rtype: (:class:`dropbox.paper.PaperDocExportResult`,
                 :class:`requests.models.Response`)
        :raises: :class:`.exceptions.ApiError`

        If this raises, ApiError will contain:
            :class:`dropbox.paper.DocLookupError`

        If you do not consume the entire response body, then you must call close
        on the response object, otherwise you will max out your available
        connections. We recommend using the `contextlib.closing
        <https://docs.python.org/2/library/contextlib.html#contextlib.closing>`_
        context manager to ensure this.
        """
        warnings.warn(
            'docs/download is deprecated.',
            DeprecationWarning,
        )
        arg = paper.PaperDocExport(doc_id,
                                   export_format)
        r = self.request(
            paper.docs_download,
            'paper',
            arg,
            None,
        )
        return r

    def paper_docs_download_to_file(self,
                                    download_path,
                                    doc_id,
                                    export_format):
        """
        Exports and downloads Paper doc either as HTML or markdown. Note that
        this endpoint will continue to work for content created by users on the
        older version of Paper. To check which version of Paper a user is on,
        use /users/features/get_values. If the paper_as_files feature is
        enabled, then the user is running the new version of Paper. Refer to the
        `Paper Migration Guide
        <https://www.dropbox.com/lp/developers/reference/paper-migration-guide>`_
        for migration information.

        Route attributes:
            scope: files.content.read

        :param str download_path: Path on local machine to save file.
        :type export_format: :class:`dropbox.paper.ExportFormat`
        :rtype: :class:`dropbox.paper.PaperDocExportResult`
        :raises: :class:`.exceptions.ApiError`

        If this raises, ApiError will contain:
            :class:`dropbox.paper.DocLookupError`
        """
        warnings.warn(
            'docs/download is deprecated.',
            DeprecationWarning,
        )
        arg = paper.PaperDocExport(doc_id,
                                   export_format)
        r = self.request(
            paper.docs_download,
            'paper',
            arg,
            None,
        )
        self._save_body_to_file(download_path, r[1])
        return r[0]

    def paper_docs_folder_users_list(self,
                                     doc_id,
                                     limit=1000):
        """
        Lists the users who are explicitly invited to the Paper folder in which
        the Paper doc is contained. For private folders all users (including
        owner) shared on the folder are listed and for team folders all non-team
        users shared on the folder are returned. Note that this endpoint will
        continue to work for content created by users on the older version of
        Paper. To check which version of Paper a user is on, use
        /users/features/get_values. If the paper_as_files feature is enabled,
        then the user is running the new version of Paper. Refer to the `Paper
        Migration Guide
        <https://www.dropbox.com/lp/developers/reference/paper-migration-guide>`_
        for migration information.

        Route attributes:
            scope: sharing.read

        :param int limit: Size limit per batch. The maximum number of users that
            can be retrieved per batch is 1000. Higher value results in invalid
            arguments error.
        :rtype: :class:`dropbox.paper.ListUsersOnFolderResponse`
        :raises: :class:`.exceptions.ApiError`

        If this raises, ApiError will contain:
            :class:`dropbox.paper.DocLookupError`
        """
        warnings.warn(
            'docs/folder_users/list is deprecated.',
            DeprecationWarning,
        )
        arg = paper.ListUsersOnFolderArgs(doc_id,
                                          limit)
        r = self.request(
            paper.docs_folder_users_list,
            'paper',
            arg,
            None,
        )
        return r

    def paper_docs_folder_users_list_continue(self,
                                              doc_id,
                                              cursor):
        """
        Once a cursor has been retrieved from
        :meth:`paper_docs_folder_users_list`, use this to paginate through all
        users on the Paper folder. Note that this endpoint will continue to work
        for content created by users on the older version of Paper. To check
        which version of Paper a user is on, use /users/features/get_values. If
        the paper_as_files feature is enabled, then the user is running the new
        version of Paper. Refer to the `Paper Migration Guide
        <https://www.dropbox.com/lp/developers/reference/paper-migration-guide>`_
        for migration information.

        Route attributes:
            scope: sharing.read

        :param str cursor: The cursor obtained from
            :meth:`paper_docs_folder_users_list` or
            :meth:`paper_docs_folder_users_list_continue`. Allows for
            pagination.
        :rtype: :class:`dropbox.paper.ListUsersOnFolderResponse`
        :raises: :class:`.exceptions.ApiError`

        If this raises, ApiError will contain:
            :class:`dropbox.paper.ListUsersCursorError`
        """
        warnings.warn(
            'docs/folder_users/list/continue is deprecated.',
            DeprecationWarning,
        )
        arg = paper.ListUsersOnFolderContinueArgs(doc_id,
                                                  cursor)
        r = self.request(
            paper.docs_folder_users_list_continue,
            'paper',
            arg,
            None,
        )
        return r

    def paper_docs_get_folder_info(self,
                                   doc_id):
        """
        Retrieves folder information for the given Paper doc. This includes:   -
        folder sharing policy; permissions for subfolders are set by the
        top-level folder.   - full 'filepath', i.e. the list of folders (both
        folderId and folderName) from     the root folder to the folder directly
        containing the Paper doc.  If the Paper doc is not in any folder (aka
        unfiled) the response will be empty. Note that this endpoint will
        continue to work for content created by users on the older version of
        Paper. To check which version of Paper a user is on, use
        /users/features/get_values. If the paper_as_files feature is enabled,
        then the user is running the new version of Paper. Refer to the `Paper
        Migration Guide
        <https://www.dropbox.com/lp/developers/reference/paper-migration-guide>`_
        for migration information.

        Route attributes:
            scope: sharing.read

        :param str doc_id: The Paper doc ID.
        :rtype: :class:`dropbox.paper.FoldersContainingPaperDoc`
        :raises: :class:`.exceptions.ApiError`

        If this raises, ApiError will contain:
            :class:`dropbox.paper.DocLookupError`
        """
        warnings.warn(
            'docs/get_folder_info is deprecated.',
            DeprecationWarning,
        )
        arg = paper.RefPaperDoc(doc_id)
        r = self.request(
            paper.docs_get_folder_info,
            'paper',
            arg,
            None,
        )
        return r

    def paper_docs_list(self,
                        filter_by=paper.ListPaperDocsFilterBy.docs_accessed,
                        sort_by=paper.ListPaperDocsSortBy.accessed,
                        sort_order=paper.ListPaperDocsSortOrder.ascending,
                        limit=1000):
        """
        Return the list of all Paper docs according to the argument
        specifications. To iterate over through the full pagination, pass the
        cursor to :meth:`paper_docs_list_continue`. Note that this endpoint will
        continue to work for content created by users on the older version of
        Paper. To check which version of Paper a user is on, use
        /users/features/get_values. If the paper_as_files feature is enabled,
        then the user is running the new version of Paper. Refer to the `Paper
        Migration Guide
        <https://www.dropbox.com/lp/developers/reference/paper-migration-guide>`_
        for migration information.

        Route attributes:
            scope: files.metadata.read

        :param filter_by: Allows user to specify how the Paper docs should be
            filtered.
        :type filter_by: :class:`dropbox.paper.ListPaperDocsFilterBy`
        :param sort_by: Allows user to specify how the Paper docs should be
            sorted.
        :type sort_by: :class:`dropbox.paper.ListPaperDocsSortBy`
        :param sort_order: Allows user to specify the sort order of the result.
        :type sort_order: :class:`dropbox.paper.ListPaperDocsSortOrder`
        :param int limit: Size limit per batch. The maximum number of docs that
            can be retrieved per batch is 1000. Higher value results in invalid
            arguments error.
        :rtype: :class:`dropbox.paper.ListPaperDocsResponse`
        """
        warnings.warn(
            'docs/list is deprecated.',
            DeprecationWarning,
        )
        arg = paper.ListPaperDocsArgs(filter_by,
                                      sort_by,
                                      sort_order,
                                      limit)
        r = self.request(
            paper.docs_list,
            'paper',
            arg,
            None,
        )
        return r

    def paper_docs_list_continue(self,
                                 cursor):
        """
        Once a cursor has been retrieved from :meth:`paper_docs_list`, use this
        to paginate through all Paper doc. Note that this endpoint will continue
        to work for content created by users on the older version of Paper. To
        check which version of Paper a user is on, use
        /users/features/get_values. If the paper_as_files feature is enabled,
        then the user is running the new version of Paper. Refer to the `Paper
        Migration Guide
        <https://www.dropbox.com/lp/developers/reference/paper-migration-guide>`_
        for migration information.

        Route attributes:
            scope: files.metadata.read

        :param str cursor: The cursor obtained from :meth:`paper_docs_list` or
            :meth:`paper_docs_list_continue`. Allows for pagination.
        :rtype: :class:`dropbox.paper.ListPaperDocsResponse`
        :raises: :class:`.exceptions.ApiError`

        If this raises, ApiError will contain:
            :class:`dropbox.paper.ListDocsCursorError`
        """
        warnings.warn(
            'docs/list/continue is deprecated.',
            DeprecationWarning,
        )
        arg = paper.ListPaperDocsContinueArgs(cursor)
        r = self.request(
            paper.docs_list_continue,
            'paper',
            arg,
            None,
        )
        return r

    def paper_docs_permanently_delete(self,
                                      doc_id):
        """
        Permanently deletes the given Paper doc. This operation is final as the
        doc cannot be recovered. This action can be performed only by the doc
        owner. Note that this endpoint will continue to work for content created
        by users on the older version of Paper. To check which version of Paper
        a user is on, use /users/features/get_values. If the paper_as_files
        feature is enabled, then the user is running the new version of Paper.
        Refer to the `Paper Migration Guide
        <https://www.dropbox.com/lp/developers/reference/paper-migration-guide>`_
        for migration information.

        Route attributes:
            scope: files.permanent_delete

        :param str doc_id: The Paper doc ID.
        :rtype: None
        :raises: :class:`.exceptions.ApiError`

        If this raises, ApiError will contain:
            :class:`dropbox.paper.DocLookupError`
        """
        warnings.warn(
            'docs/permanently_delete is deprecated.',
            DeprecationWarning,
        )
        arg = paper.RefPaperDoc(doc_id)
        r = self.request(
            paper.docs_permanently_delete,
            'paper',
            arg,
            None,
        )
        return None

    def paper_docs_sharing_policy_get(self,
                                      doc_id):
        """
        Gets the default sharing policy for the given Paper doc. Note that this
        endpoint will continue to work for content created by users on the older
        version of Paper. To check which version of Paper a user is on, use
        /users/features/get_values. If the paper_as_files feature is enabled,
        then the user is running the new version of Paper. Refer to the `Paper
        Migration Guide
        <https://www.dropbox.com/lp/developers/reference/paper-migration-guide>`_
        for migration information.

        Route attributes:
            scope: sharing.read

        :param str doc_id: The Paper doc ID.
        :rtype: :class:`dropbox.paper.SharingPolicy`
        :raises: :class:`.exceptions.ApiError`

        If this raises, ApiError will contain:
            :class:`dropbox.paper.DocLookupError`
        """
        warnings.warn(
            'docs/sharing_policy/get is deprecated.',
            DeprecationWarning,
        )
        arg = paper.RefPaperDoc(doc_id)
        r = self.request(
            paper.docs_sharing_policy_get,
            'paper',
            arg,
            None,
        )
        return r

    def paper_docs_sharing_policy_set(self,
                                      doc_id,
                                      sharing_policy):
        """
        Sets the default sharing policy for the given Paper doc. The default
        'team_sharing_policy' can be changed only by teams, omit this field for
        personal accounts. The 'public_sharing_policy' policy can't be set to
        the value 'disabled' because this setting can be changed only via the
        team admin console. Note that this endpoint will continue to work for
        content created by users on the older version of Paper. To check which
        version of Paper a user is on, use /users/features/get_values. If the
        paper_as_files feature is enabled, then the user is running the new
        version of Paper. Refer to the `Paper Migration Guide
        <https://www.dropbox.com/lp/developers/reference/paper-migration-guide>`_
        for migration information.

        Route attributes:
            scope: sharing.write

        :param sharing_policy: The default sharing policy to be set for the
            Paper doc.
        :type sharing_policy: :class:`dropbox.paper.SharingPolicy`
        :rtype: None
        :raises: :class:`.exceptions.ApiError`

        If this raises, ApiError will contain:
            :class:`dropbox.paper.DocLookupError`
        """
        warnings.warn(
            'docs/sharing_policy/set is deprecated.',
            DeprecationWarning,
        )
        arg = paper.PaperDocSharingPolicy(doc_id,
                                          sharing_policy)
        r = self.request(
            paper.docs_sharing_policy_set,
            'paper',
            arg,
            None,
        )
        return None

    def paper_docs_update(self,
                          f,
                          doc_id,
                          doc_update_policy,
                          revision,
                          import_format):
        """
        Updates an existing Paper doc with the provided content. Note that this
        endpoint will continue to work for content created by users on the older
        version of Paper. To check which version of Paper a user is on, use
        /users/features/get_values. If the paper_as_files feature is enabled,
        then the user is running the new version of Paper. This endpoint will be
        retired in September 2020. Refer to the `Paper Migration Guide
        <https://www.dropbox.com/lp/developers/reference/paper-migration-guide>`_
        for more information.

        Route attributes:
            scope: files.content.write

        :param bytes f: Contents to upload.
        :param doc_update_policy: The policy used for the current update call.
        :type doc_update_policy: :class:`dropbox.paper.PaperDocUpdatePolicy`
        :param int revision: The latest doc revision. This value must match the
            head revision or an error code will be returned. This is to prevent
            colliding writes.
        :param import_format: The format of provided data.
        :type import_format: :class:`dropbox.paper.ImportFormat`
        :rtype: :class:`dropbox.paper.PaperDocCreateUpdateResult`
        :raises: :class:`.exceptions.ApiError`

        If this raises, ApiError will contain:
            :class:`dropbox.paper.PaperDocUpdateError`
        """
        warnings.warn(
            'docs/update is deprecated.',
            DeprecationWarning,
        )
        arg = paper.PaperDocUpdateArgs(doc_id,
                                       doc_update_policy,
                                       revision,
                                       import_format)
        r = self.request(
            paper.docs_update,
            'paper',
            arg,
            f,
        )
        return r

    def paper_docs_users_add(self,
                             doc_id,
                             members,
                             custom_message=None,
                             quiet=False):
        """
        Allows an owner or editor to add users to a Paper doc or change their
        permissions using their email address or Dropbox account ID. The doc
        owner's permissions cannot be changed. Note that this endpoint will
        continue to work for content created by users on the older version of
        Paper. To check which version of Paper a user is on, use
        /users/features/get_values. If the paper_as_files feature is enabled,
        then the user is running the new version of Paper. Refer to the `Paper
        Migration Guide
        <https://www.dropbox.com/lp/developers/reference/paper-migration-guide>`_
        for migration information.

        Route attributes:
            scope: sharing.write

        :param List[:class:`dropbox.paper.AddMember`] members: User which should
            be added to the Paper doc. Specify only email address or Dropbox
            account ID.
        :param Nullable[str] custom_message: A personal message that will be
            emailed to each successfully added member.
        :param bool quiet: Clients should set this to true if no email message
            shall be sent to added users.
        :rtype: List[:class:`dropbox.paper.AddPaperDocUserMemberResult`]
        :raises: :class:`.exceptions.ApiError`

        If this raises, ApiError will contain:
            :class:`dropbox.paper.DocLookupError`
        """
        warnings.warn(
            'docs/users/add is deprecated.',
            DeprecationWarning,
        )
        arg = paper.AddPaperDocUser(doc_id,
                                    members,
                                    custom_message,
                                    quiet)
        r = self.request(
            paper.docs_users_add,
            'paper',
            arg,
            None,
        )
        return r

    def paper_docs_users_list(self,
                              doc_id,
                              limit=1000,
                              filter_by=paper.UserOnPaperDocFilter.shared):
        """
        Lists all users who visited the Paper doc or users with explicit access.
        This call excludes users who have been removed. The list is sorted by
        the date of the visit or the share date. The list will include both
        users, the explicitly shared ones as well as those who came in using the
        Paper url link. Note that this endpoint will continue to work for
        content created by users on the older version of Paper. To check which
        version of Paper a user is on, use /users/features/get_values. If the
        paper_as_files feature is enabled, then the user is running the new
        version of Paper. Refer to the `Paper Migration Guide
        <https://www.dropbox.com/lp/developers/reference/paper-migration-guide>`_
        for migration information.

        Route attributes:
            scope: sharing.read

        :param int limit: Size limit per batch. The maximum number of users that
            can be retrieved per batch is 1000. Higher value results in invalid
            arguments error.
        :param filter_by: Specify this attribute if you want to obtain users
            that have already accessed the Paper doc.
        :type filter_by: :class:`dropbox.paper.UserOnPaperDocFilter`
        :rtype: :class:`dropbox.paper.ListUsersOnPaperDocResponse`
        :raises: :class:`.exceptions.ApiError`

        If this raises, ApiError will contain:
            :class:`dropbox.paper.DocLookupError`
        """
        warnings.warn(
            'docs/users/list is deprecated.',
            DeprecationWarning,
        )
        arg = paper.ListUsersOnPaperDocArgs(doc_id,
                                            limit,
                                            filter_by)
        r = self.request(
            paper.docs_users_list,
            'paper',
            arg,
            None,
        )
        return r

    def paper_docs_users_list_continue(self,
                                       doc_id,
                                       cursor):
        """
        Once a cursor has been retrieved from :meth:`paper_docs_users_list`, use
        this to paginate through all users on the Paper doc. Note that this
        endpoint will continue to work for content created by users on the older
        version of Paper. To check which version of Paper a user is on, use
        /users/features/get_values. If the paper_as_files feature is enabled,
        then the user is running the new version of Paper. Refer to the `Paper
        Migration Guide
        <https://www.dropbox.com/lp/developers/reference/paper-migration-guide>`_
        for migration information.

        Route attributes:
            scope: sharing.read

        :param str cursor: The cursor obtained from
            :meth:`paper_docs_users_list` or
            :meth:`paper_docs_users_list_continue`. Allows for pagination.
        :rtype: :class:`dropbox.paper.ListUsersOnPaperDocResponse`
        :raises: :class:`.exceptions.ApiError`

        If this raises, ApiError will contain:
            :class:`dropbox.paper.ListUsersCursorError`
        """
        warnings.warn(
            'docs/users/list/continue is deprecated.',
            DeprecationWarning,
        )
        arg = paper.ListUsersOnPaperDocContinueArgs(doc_id,
                                                    cursor)
        r = self.request(
            paper.docs_users_list_continue,
            'paper',
            arg,
            None,
        )
        return r

    def paper_docs_users_remove(self,
                                doc_id,
                                member):
        """
        Allows an owner or editor to remove users from a Paper doc using their
        email address or Dropbox account ID. The doc owner cannot be removed.
        Note that this endpoint will continue to work for content created by
        users on the older version of Paper. To check which version of Paper a
        user is on, use /users/features/get_values. If the paper_as_files
        feature is enabled, then the user is running the new version of Paper.
        Refer to the `Paper Migration Guide
        <https://www.dropbox.com/lp/developers/reference/paper-migration-guide>`_
        for migration information.

        Route attributes:
            scope: sharing.write

        :param member: User which should be removed from the Paper doc. Specify
            only email address or Dropbox account ID.
        :type member: :class:`dropbox.paper.MemberSelector`
        :rtype: None
        :raises: :class:`.exceptions.ApiError`

        If this raises, ApiError will contain:
            :class:`dropbox.paper.DocLookupError`
        """
        warnings.warn(
            'docs/users/remove is deprecated.',
            DeprecationWarning,
        )
        arg = paper.RemovePaperDocUser(doc_id,
                                       member)
        r = self.request(
            paper.docs_users_remove,
            'paper',
            arg,
            None,
        )
        return None

    def paper_folders_create(self,
                             name,
                             parent_folder_id=None,
                             is_team_folder=None):
        """
        Create a new Paper folder with the provided info. Note that this
        endpoint will continue to work for content created by users on the older
        version of Paper. To check which version of Paper a user is on, use
        /users/features/get_values. If the paper_as_files feature is enabled,
        then the user is running the new version of Paper. Refer to the `Paper
        Migration Guide
        <https://www.dropbox.com/lp/developers/reference/paper-migration-guide>`_
        for migration information.

        Route attributes:
            scope: files.content.write

        :param str name: The name of the new Paper folder.
        :param Nullable[str] parent_folder_id: The encrypted Paper folder Id
            where the new Paper folder should be created. The API user has to
            have write access to this folder or error is thrown. If not
            supplied, the new folder will be created at top level.
        :param Nullable[bool] is_team_folder: Whether the folder to be created
            should be a team folder. This value will be ignored if
            parent_folder_id is supplied, as the new folder will inherit the
            type (private or team folder) from its parent. We will by default
            create a top-level private folder if both parent_folder_id and
            is_team_folder are not supplied.
        :rtype: :class:`dropbox.paper.PaperFolderCreateResult`
        :raises: :class:`.exceptions.ApiError`

        If this raises, ApiError will contain:
            :class:`dropbox.paper.PaperFolderCreateError`
        """
        warnings.warn(
            'folders/create is deprecated.',
            DeprecationWarning,
        )
        arg = paper.PaperFolderCreateArg(name,
                                         parent_folder_id,
                                         is_team_folder)
        r = self.request(
            paper.folders_create,
            'paper',
            arg,
            None,
        )
        return r

    # ------------------------------------------
    # Routes in sharing namespace

    def sharing_add_file_member(self,
                                file,
                                members,
                                custom_message=None,
                                quiet=False,
                                access_level=sharing.AccessLevel.viewer,
                                add_message_as_comment=False):
        """
        Adds specified members to a file.

        Route attributes:
            scope: sharing.write

        :param str file: File to which to add members.
        :param List[:class:`dropbox.sharing.MemberSelector`] members: Members to
            add. Note that even an email address is given, this may result in a
            user being directly added to the membership if that email is the
            user's main account email.
        :param Nullable[str] custom_message: Message to send to added members in
            their invitation.
        :param bool quiet: Whether added members should be notified via email
            and device notifications of their invitation.
        :param access_level: AccessLevel union object, describing what access
            level we want to give new members.
        :type access_level: :class:`dropbox.sharing.AccessLevel`
        :param bool add_message_as_comment: If the custom message should be
            added as a comment on the file.
        :rtype: List[:class:`dropbox.sharing.FileMemberActionResult`]
        :raises: :class:`.exceptions.ApiError`

        If this raises, ApiError will contain:
            :class:`dropbox.sharing.AddFileMemberError`
        """
        arg = sharing.AddFileMemberArgs(file,
                                        members,
                                        custom_message,
                                        quiet,
                                        access_level,
                                        add_message_as_comment)
        r = self.request(
            sharing.add_file_member,
            'sharing',
            arg,
            None,
        )
        return r

    def sharing_add_folder_member(self,
                                  shared_folder_id,
                                  members,
                                  quiet=False,
                                  custom_message=None):
        """
        Allows an owner or editor (if the ACL update policy allows) of a shared
        folder to add another member. For the new member to get access to all
        the functionality for this folder, you will need to call
        :meth:`sharing_mount_folder` on their behalf.

        Route attributes:
            scope: sharing.write

        :param str shared_folder_id: The ID for the shared folder.
        :param List[:class:`dropbox.sharing.AddMember`] members: The intended
            list of members to add.  Added members will receive invites to join
            the shared folder.
        :param bool quiet: Whether added members should be notified via email
            and device notifications of their invite.
        :param Nullable[str] custom_message: Optional message to display to
            added members in their invitation.
        :rtype: None
        :raises: :class:`.exceptions.ApiError`

        If this raises, ApiError will contain:
            :class:`dropbox.sharing.AddFolderMemberError`
        """
        arg = sharing.AddFolderMemberArg(shared_folder_id,
                                         members,
                                         quiet,
                                         custom_message)
        r = self.request(
            sharing.add_folder_member,
            'sharing',
            arg,
            None,
        )
        return None

    def sharing_check_job_status(self,
                                 async_job_id):
        """
        Returns the status of an asynchronous job.

        Route attributes:
            scope: sharing.write

        :param str async_job_id: Id of the asynchronous job. This is the value
            of a response returned from the method that launched the job.
        :rtype: :class:`dropbox.sharing.JobStatus`
        :raises: :class:`.exceptions.ApiError`

        If this raises, ApiError will contain:
            :class:`dropbox.sharing.PollError`
        """
        arg = async_.PollArg(async_job_id)
        r = self.request(
            sharing.check_job_status,
            'sharing',
            arg,
            None,
        )
        return r

    def sharing_check_remove_member_job_status(self,
                                               async_job_id):
        """
        Returns the status of an asynchronous job for sharing a folder.

        Route attributes:
            scope: sharing.write

        :param str async_job_id: Id of the asynchronous job. This is the value
            of a response returned from the method that launched the job.
        :rtype: :class:`dropbox.sharing.RemoveMemberJobStatus`
        :raises: :class:`.exceptions.ApiError`

        If this raises, ApiError will contain:
            :class:`dropbox.sharing.PollError`
        """
        arg = async_.PollArg(async_job_id)
        r = self.request(
            sharing.check_remove_member_job_status,
            'sharing',
            arg,
            None,
        )
        return r

    def sharing_check_share_job_status(self,
                                       async_job_id):
        """
        Returns the status of an asynchronous job for sharing a folder.

        Route attributes:
            scope: sharing.write

        :param str async_job_id: Id of the asynchronous job. This is the value
            of a response returned from the method that launched the job.
        :rtype: :class:`dropbox.sharing.ShareFolderJobStatus`
        :raises: :class:`.exceptions.ApiError`

        If this raises, ApiError will contain:
            :class:`dropbox.sharing.PollError`
        """
        arg = async_.PollArg(async_job_id)
        r = self.request(
            sharing.check_share_job_status,
            'sharing',
            arg,
            None,
        )
        return r

    def sharing_create_shared_link(self,
                                   path,
                                   short_url=False,
                                   pending_upload=None):
        """
        Create a shared link. If a shared link already exists for the given
        path, that link is returned. Previously, it was technically possible to
        break a shared link by moving or renaming the corresponding file or
        folder. In the future, this will no longer be the case, so your app
        shouldn't rely on this behavior. Instead, if your app needs to revoke a
        shared link, use :meth:`sharing_revoke_shared_link`.

        Route attributes:
            scope: sharing.write

        :param str path: The path to share.
        :type short_url: bool
        :param Nullable[:class:`dropbox.sharing.PendingUploadMode`]
            pending_upload: If it's okay to share a path that does not yet
            exist, set this to either ``PendingUploadMode.file`` or
            ``PendingUploadMode.folder`` to indicate whether to assume it's a
            file or folder.
        :rtype: :class:`dropbox.sharing.PathLinkMetadata`
        :raises: :class:`.exceptions.ApiError`

        If this raises, ApiError will contain:
            :class:`dropbox.sharing.CreateSharedLinkError`
        """
        warnings.warn(
            'create_shared_link is deprecated. Use create_shared_link_with_settings.',
            DeprecationWarning,
        )
        arg = sharing.CreateSharedLinkArg(path,
                                          short_url,
                                          pending_upload)
        r = self.request(
            sharing.create_shared_link,
            'sharing',
            arg,
            None,
        )
        return r

    def sharing_create_shared_link_with_settings(self,
                                                 path,
                                                 settings=None):
        """
        Create a shared link with custom settings. If no settings are given then
        the default visibility is ``RequestedVisibility.public`` (The resolved
        visibility, though, may depend on other aspects such as team and shared
        folder settings).

        Route attributes:
            scope: sharing.write

        :param str path: The path to be shared by the shared link.
        :param Nullable[:class:`dropbox.sharing.SharedLinkSettings`] settings:
            The requested settings for the newly created shared link.
        :rtype: :class:`dropbox.sharing.SharedLinkMetadata`
        :raises: :class:`.exceptions.ApiError`

        If this raises, ApiError will contain:
            :class:`dropbox.sharing.CreateSharedLinkWithSettingsError`
        """
        arg = sharing.CreateSharedLinkWithSettingsArg(path,
                                                      settings)
        r = self.request(
            sharing.create_shared_link_with_settings,
            'sharing',
            arg,
            None,
        )
        return r

    def sharing_get_file_metadata(self,
                                  file,
                                  actions=None):
        """
        Returns shared file metadata.

        Route attributes:
            scope: sharing.read

        :param str file: The file to query.
        :param Nullable[List[:class:`dropbox.sharing.FileAction`]] actions: A
            list of `FileAction`s corresponding to `FilePermission`s that should
            appear in the  response's ``SharedFileMetadata.permissions`` field
            describing the actions the  authenticated user can perform on the
            file.
        :rtype: :class:`dropbox.sharing.SharedFileMetadata`
        :raises: :class:`.exceptions.ApiError`

        If this raises, ApiError will contain:
            :class:`dropbox.sharing.GetFileMetadataError`
        """
        arg = sharing.GetFileMetadataArg(file,
                                         actions)
        r = self.request(
            sharing.get_file_metadata,
            'sharing',
            arg,
            None,
        )
        return r

    def sharing_get_file_metadata_batch(self,
                                        files,
                                        actions=None):
        """
        Returns shared file metadata.

        Route attributes:
            scope: sharing.read

        :param List[str] files: The files to query.
        :param Nullable[List[:class:`dropbox.sharing.FileAction`]] actions: A
            list of `FileAction`s corresponding to `FilePermission`s that should
            appear in the  response's ``SharedFileMetadata.permissions`` field
            describing the actions the  authenticated user can perform on the
            file.
        :rtype: List[:class:`dropbox.sharing.GetFileMetadataBatchResult`]
        :raises: :class:`.exceptions.ApiError`

        If this raises, ApiError will contain:
            :class:`dropbox.sharing.SharingUserError`
        """
        arg = sharing.GetFileMetadataBatchArg(files,
                                              actions)
        r = self.request(
            sharing.get_file_metadata_batch,
            'sharing',
            arg,
            None,
        )
        return r

    def sharing_get_folder_metadata(self,
                                    shared_folder_id,
                                    actions=None):
        """
        Returns shared folder metadata by its folder ID.

        Route attributes:
            scope: sharing.read

        :param str shared_folder_id: The ID for the shared folder.
        :param Nullable[List[:class:`dropbox.sharing.FolderAction`]] actions: A
            list of `FolderAction`s corresponding to `FolderPermission`s that
            should appear in the  response's
            ``SharedFolderMetadata.permissions`` field describing the actions
            the  authenticated user can perform on the folder.
        :rtype: :class:`dropbox.sharing.SharedFolderMetadata`
        :raises: :class:`.exceptions.ApiError`

        If this raises, ApiError will contain:
            :class:`dropbox.sharing.SharedFolderAccessError`
        """
        arg = sharing.GetMetadataArgs(shared_folder_id,
                                      actions)
        r = self.request(
            sharing.get_folder_metadata,
            'sharing',
            arg,
            None,
        )
        return r

    def sharing_get_shared_link_file(self,
                                     url,
                                     path=None,
                                     link_password=None):
        """
        Download the shared link's file from a user's Dropbox.

        Route attributes:
            scope: sharing.read

        :param str url: URL of the shared link.
        :param Nullable[str] path: If the shared link is to a folder, this
            parameter can be used to retrieve the metadata for a specific file
            or sub-folder in this folder. A relative path should be used.
        :param Nullable[str] link_password: If the shared link has a password,
            this parameter can be used.
        :rtype: (:class:`dropbox.sharing.SharedLinkMetadata`,
                 :class:`requests.models.Response`)
        :raises: :class:`.exceptions.ApiError`

        If this raises, ApiError will contain:
            :class:`dropbox.sharing.GetSharedLinkFileError`

        If you do not consume the entire response body, then you must call close
        on the response object, otherwise you will max out your available
        connections. We recommend using the `contextlib.closing
        <https://docs.python.org/2/library/contextlib.html#contextlib.closing>`_
        context manager to ensure this.
        """
        arg = sharing.GetSharedLinkMetadataArg(url,
                                               path,
                                               link_password)
        r = self.request(
            sharing.get_shared_link_file,
            'sharing',
            arg,
            None,
        )
        return r

    def sharing_get_shared_link_file_to_file(self,
                                             download_path,
                                             url,
                                             path=None,
                                             link_password=None):
        """
        Download the shared link's file from a user's Dropbox.

        Route attributes:
            scope: sharing.read

        :param str download_path: Path on local machine to save file.
        :param str url: URL of the shared link.
        :param Nullable[str] path: If the shared link is to a folder, this
            parameter can be used to retrieve the metadata for a specific file
            or sub-folder in this folder. A relative path should be used.
        :param Nullable[str] link_password: If the shared link has a password,
            this parameter can be used.
        :rtype: :class:`dropbox.sharing.SharedLinkMetadata`
        :raises: :class:`.exceptions.ApiError`

        If this raises, ApiError will contain:
            :class:`dropbox.sharing.GetSharedLinkFileError`
        """
        arg = sharing.GetSharedLinkMetadataArg(url,
                                               path,
                                               link_password)
        r = self.request(
            sharing.get_shared_link_file,
            'sharing',
            arg,
            None,
        )
        self._save_body_to_file(download_path, r[1])
        return r[0]

    def sharing_get_shared_link_metadata(self,
                                         url,
                                         path=None,
                                         link_password=None):
        """
        Get the shared link's metadata.

        Route attributes:
            scope: sharing.read

        :param str url: URL of the shared link.
        :param Nullable[str] path: If the shared link is to a folder, this
            parameter can be used to retrieve the metadata for a specific file
            or sub-folder in this folder. A relative path should be used.
        :param Nullable[str] link_password: If the shared link has a password,
            this parameter can be used.
        :rtype: :class:`dropbox.sharing.SharedLinkMetadata`
        :raises: :class:`.exceptions.ApiError`

        If this raises, ApiError will contain:
            :class:`dropbox.sharing.SharedLinkError`
        """
        arg = sharing.GetSharedLinkMetadataArg(url,
                                               path,
                                               link_password)
        r = self.request(
            sharing.get_shared_link_metadata,
            'sharing',
            arg,
            None,
        )
        return r

    def sharing_get_shared_links(self,
                                 path=None):
        """
        Returns a list of :class:`dropbox.sharing.LinkMetadata` objects for this
        user, including collection links. If no path is given, returns a list of
        all shared links for the current user, including collection links, up to
        a maximum of 1000 links. If a non-empty path is given, returns a list of
        all shared links that allow access to the given path.  Collection links
        are never returned in this case.

        Route attributes:
            scope: sharing.read

        :param Nullable[str] path: See :meth:`sharing_get_shared_links`
            description.
        :rtype: :class:`dropbox.sharing.GetSharedLinksResult`
        :raises: :class:`.exceptions.ApiError`

        If this raises, ApiError will contain:
            :class:`dropbox.sharing.GetSharedLinksError`
        """
        warnings.warn(
            'get_shared_links is deprecated. Use list_shared_links.',
            DeprecationWarning,
        )
        arg = sharing.GetSharedLinksArg(path)
        r = self.request(
            sharing.get_shared_links,
            'sharing',
            arg,
            None,
        )
        return r

    def sharing_list_file_members(self,
                                  file,
                                  actions=None,
                                  include_inherited=True,
                                  limit=100):
        """
        Use to obtain the members who have been invited to a file, both
        inherited and uninherited members.

        Route attributes:
            scope: sharing.read

        :param str file: The file for which you want to see members.
        :param Nullable[List[:class:`dropbox.sharing.MemberAction`]] actions:
            The actions for which to return permissions on a member.
        :param bool include_inherited: Whether to include members who only have
            access from a parent shared folder.
        :param int limit: Number of members to return max per query. Defaults to
            100 if no limit is specified.
        :rtype: :class:`dropbox.sharing.SharedFileMembers`
        :raises: :class:`.exceptions.ApiError`

        If this raises, ApiError will contain:
            :class:`dropbox.sharing.ListFileMembersError`
        """
        arg = sharing.ListFileMembersArg(file,
                                         actions,
                                         include_inherited,
                                         limit)
        r = self.request(
            sharing.list_file_members,
            'sharing',
            arg,
            None,
        )
        return r

    def sharing_list_file_members_batch(self,
                                        files,
                                        limit=10):
        """
        Get members of multiple files at once. The arguments to this route are
        more limited, and the limit on query result size per file is more
        strict. To customize the results more, use the individual file endpoint.
        Inherited users and groups are not included in the result, and
        permissions are not returned for this endpoint.

        Route attributes:
            scope: sharing.read

        :param List[str] files: Files for which to return members.
        :param int limit: Number of members to return max per query. Defaults to
            10 if no limit is specified.
        :rtype: List[:class:`dropbox.sharing.ListFileMembersBatchResult`]
        :raises: :class:`.exceptions.ApiError`

        If this raises, ApiError will contain:
            :class:`dropbox.sharing.SharingUserError`
        """
        arg = sharing.ListFileMembersBatchArg(files,
                                              limit)
        r = self.request(
            sharing.list_file_members_batch,
            'sharing',
            arg,
            None,
        )
        return r

    def sharing_list_file_members_continue(self,
                                           cursor):
        """
        Once a cursor has been retrieved from :meth:`sharing_list_file_members`
        or :meth:`sharing_list_file_members_batch`, use this to paginate through
        all shared file members.

        Route attributes:
            scope: sharing.read

        :param str cursor: The cursor returned by your last call to
            :meth:`sharing_list_file_members`,
            :meth:`sharing_list_file_members_continue`, or
            :meth:`sharing_list_file_members_batch`.
        :rtype: :class:`dropbox.sharing.SharedFileMembers`
        :raises: :class:`.exceptions.ApiError`

        If this raises, ApiError will contain:
            :class:`dropbox.sharing.ListFileMembersContinueError`
        """
        arg = sharing.ListFileMembersContinueArg(cursor)
        r = self.request(
            sharing.list_file_members_continue,
            'sharing',
            arg,
            None,
        )
        return r

    def sharing_list_folder_members(self,
                                    shared_folder_id,
                                    actions=None,
                                    limit=1000):
        """
        Returns shared folder membership by its folder ID.

        Route attributes:
            scope: sharing.read

        :param str shared_folder_id: The ID for the shared folder.
        :rtype: :class:`dropbox.sharing.SharedFolderMembers`
        :raises: :class:`.exceptions.ApiError`

        If this raises, ApiError will contain:
            :class:`dropbox.sharing.SharedFolderAccessError`
        """
        arg = sharing.ListFolderMembersArgs(shared_folder_id,
                                            actions,
                                            limit)
        r = self.request(
            sharing.list_folder_members,
            'sharing',
            arg,
            None,
        )
        return r

    def sharing_list_folder_members_continue(self,
                                             cursor):
        """
        Once a cursor has been retrieved from
        :meth:`sharing_list_folder_members`, use this to paginate through all
        shared folder members.

        Route attributes:
            scope: sharing.read

        :param str cursor: The cursor returned by your last call to
            :meth:`sharing_list_folder_members` or
            :meth:`sharing_list_folder_members_continue`.
        :rtype: :class:`dropbox.sharing.SharedFolderMembers`
        :raises: :class:`.exceptions.ApiError`

        If this raises, ApiError will contain:
            :class:`dropbox.sharing.ListFolderMembersContinueError`
        """
        arg = sharing.ListFolderMembersContinueArg(cursor)
        r = self.request(
            sharing.list_folder_members_continue,
            'sharing',
            arg,
            None,
        )
        return r

    def sharing_list_folders(self,
                             limit=1000,
                             actions=None):
        """
        Return the list of all shared folders the current user has access to.

        Route attributes:
            scope: sharing.read

        :param int limit: The maximum number of results to return per request.
        :param Nullable[List[:class:`dropbox.sharing.FolderAction`]] actions: A
            list of `FolderAction`s corresponding to `FolderPermission`s that
            should appear in the  response's
            ``SharedFolderMetadata.permissions`` field describing the actions
            the  authenticated user can perform on the folder.
        :rtype: :class:`dropbox.sharing.ListFoldersResult`
        """
        arg = sharing.ListFoldersArgs(limit,
                                      actions)
        r = self.request(
            sharing.list_folders,
            'sharing',
            arg,
            None,
        )
        return r

    def sharing_list_folders_continue(self,
                                      cursor):
        """
        Once a cursor has been retrieved from :meth:`sharing_list_folders`, use
        this to paginate through all shared folders. The cursor must come from a
        previous call to :meth:`sharing_list_folders` or
        :meth:`sharing_list_folders_continue`.

        Route attributes:
            scope: sharing.read

        :param str cursor: The cursor returned by the previous API call
            specified in the endpoint description.
        :rtype: :class:`dropbox.sharing.ListFoldersResult`
        :raises: :class:`.exceptions.ApiError`

        If this raises, ApiError will contain:
            :class:`dropbox.sharing.ListFoldersContinueError`
        """
        arg = sharing.ListFoldersContinueArg(cursor)
        r = self.request(
            sharing.list_folders_continue,
            'sharing',
            arg,
            None,
        )
        return r

    def sharing_list_mountable_folders(self,
                                       limit=1000,
                                       actions=None):
        """
        Return the list of all shared folders the current user can mount or
        unmount.

        Route attributes:
            scope: sharing.read

        :param int limit: The maximum number of results to return per request.
        :param Nullable[List[:class:`dropbox.sharing.FolderAction`]] actions: A
            list of `FolderAction`s corresponding to `FolderPermission`s that
            should appear in the  response's
            ``SharedFolderMetadata.permissions`` field describing the actions
            the  authenticated user can perform on the folder.
        :rtype: :class:`dropbox.sharing.ListFoldersResult`
        """
        arg = sharing.ListFoldersArgs(limit,
                                      actions)
        r = self.request(
            sharing.list_mountable_folders,
            'sharing',
            arg,
            None,
        )
        return r

    def sharing_list_mountable_folders_continue(self,
                                                cursor):
        """
        Once a cursor has been retrieved from
        :meth:`sharing_list_mountable_folders`, use this to paginate through all
        mountable shared folders. The cursor must come from a previous call to
        :meth:`sharing_list_mountable_folders` or
        :meth:`sharing_list_mountable_folders_continue`.

        Route attributes:
            scope: sharing.read

        :param str cursor: The cursor returned by the previous API call
            specified in the endpoint description.
        :rtype: :class:`dropbox.sharing.ListFoldersResult`
        :raises: :class:`.exceptions.ApiError`

        If this raises, ApiError will contain:
            :class:`dropbox.sharing.ListFoldersContinueError`
        """
        arg = sharing.ListFoldersContinueArg(cursor)
        r = self.request(
            sharing.list_mountable_folders_continue,
            'sharing',
            arg,
            None,
        )
        return r

    def sharing_list_received_files(self,
                                    limit=100,
                                    actions=None):
        """
        Returns a list of all files shared with current user.  Does not include
        files the user has received via shared folders, and does  not include
        unclaimed invitations.

        Route attributes:
            scope: sharing.read

        :param int limit: Number of files to return max per query. Defaults to
            100 if no limit is specified.
        :param Nullable[List[:class:`dropbox.sharing.FileAction`]] actions: A
            list of `FileAction`s corresponding to `FilePermission`s that should
            appear in the  response's ``SharedFileMetadata.permissions`` field
            describing the actions the  authenticated user can perform on the
            file.
        :rtype: :class:`dropbox.sharing.ListFilesResult`
        :raises: :class:`.exceptions.ApiError`

        If this raises, ApiError will contain:
            :class:`dropbox.sharing.SharingUserError`
        """
        arg = sharing.ListFilesArg(limit,
                                   actions)
        r = self.request(
            sharing.list_received_files,
            'sharing',
            arg,
            None,
        )
        return r

    def sharing_list_received_files_continue(self,
                                             cursor):
        """
        Get more results with a cursor from :meth:`sharing_list_received_files`.

        Route attributes:
            scope: sharing.read

        :param str cursor: Cursor in ``ListFilesResult.cursor``.
        :rtype: :class:`dropbox.sharing.ListFilesResult`
        :raises: :class:`.exceptions.ApiError`

        If this raises, ApiError will contain:
            :class:`dropbox.sharing.ListFilesContinueError`
        """
        arg = sharing.ListFilesContinueArg(cursor)
        r = self.request(
            sharing.list_received_files_continue,
            'sharing',
            arg,
            None,
        )
        return r

    def sharing_list_shared_links(self,
                                  path=None,
                                  cursor=None,
                                  direct_only=None):
        """
        List shared links of this user. If no path is given, returns a list of
        all shared links for the current user. For members of business teams
        using team space and member folders, returns all shared links in the
        team member's home folder unless the team space ID is specified in the
        request header. For more information, refer to the `Namespace Guide
        <https://www.dropbox.com/developers/reference/namespace-guide>`_. If a
        non-empty path is given, returns a list of all shared links that allow
        access to the given path - direct links to the given path and links to
        parent folders of the given path. Links to parent folders can be
        suppressed by setting direct_only to true.

        Route attributes:
            scope: sharing.read

        :param Nullable[str] path: See :meth:`sharing_list_shared_links`
            description.
        :param Nullable[str] cursor: The cursor returned by your last call to
            :meth:`sharing_list_shared_links`.
        :param Nullable[bool] direct_only: See :meth:`sharing_list_shared_links`
            description.
        :rtype: :class:`dropbox.sharing.ListSharedLinksResult`
        :raises: :class:`.exceptions.ApiError`

        If this raises, ApiError will contain:
            :class:`dropbox.sharing.ListSharedLinksError`
        """
        arg = sharing.ListSharedLinksArg(path,
                                         cursor,
                                         direct_only)
        r = self.request(
            sharing.list_shared_links,
            'sharing',
            arg,
            None,
        )
        return r

    def sharing_modify_shared_link_settings(self,
                                            url,
                                            settings,
                                            remove_expiration=False):
        """
        Modify the shared link's settings. If the requested visibility conflict
        with the shared links policy of the team or the shared folder (in case
        the linked file is part of a shared folder) then the
        ``LinkPermissions.resolved_visibility`` of the returned
        :class:`dropbox.sharing.SharedLinkMetadata` will reflect the actual
        visibility of the shared link and the
        ``LinkPermissions.requested_visibility`` will reflect the requested
        visibility.

        Route attributes:
            scope: sharing.write

        :param str url: URL of the shared link to change its settings.
        :param settings: Set of settings for the shared link.
        :type settings: :class:`dropbox.sharing.SharedLinkSettings`
        :param bool remove_expiration: If set to true, removes the expiration of
            the shared link.
        :rtype: :class:`dropbox.sharing.SharedLinkMetadata`
        :raises: :class:`.exceptions.ApiError`

        If this raises, ApiError will contain:
            :class:`dropbox.sharing.ModifySharedLinkSettingsError`
        """
        arg = sharing.ModifySharedLinkSettingsArgs(url,
                                                   settings,
                                                   remove_expiration)
        r = self.request(
            sharing.modify_shared_link_settings,
            'sharing',
            arg,
            None,
        )
        return r

    def sharing_mount_folder(self,
                             shared_folder_id):
        """
        The current user mounts the designated folder. Mount a shared folder for
        a user after they have been added as a member. Once mounted, the shared
        folder will appear in their Dropbox.

        Route attributes:
            scope: sharing.write

        :param str shared_folder_id: The ID of the shared folder to mount.
        :rtype: :class:`dropbox.sharing.SharedFolderMetadata`
        :raises: :class:`.exceptions.ApiError`

        If this raises, ApiError will contain:
            :class:`dropbox.sharing.MountFolderError`
        """
        arg = sharing.MountFolderArg(shared_folder_id)
        r = self.request(
            sharing.mount_folder,
            'sharing',
            arg,
            None,
        )
        return r

    def sharing_relinquish_file_membership(self,
                                           file):
        """
        The current user relinquishes their membership in the designated file.
        Note that the current user may still have inherited access to this file
        through the parent folder.

        Route attributes:
            scope: sharing.write

        :param str file: The path or id for the file.
        :rtype: None
        :raises: :class:`.exceptions.ApiError`

        If this raises, ApiError will contain:
            :class:`dropbox.sharing.RelinquishFileMembershipError`
        """
        arg = sharing.RelinquishFileMembershipArg(file)
        r = self.request(
            sharing.relinquish_file_membership,
            'sharing',
            arg,
            None,
        )
        return None

    def sharing_relinquish_folder_membership(self,
                                             shared_folder_id,
                                             leave_a_copy=False):
        """
        The current user relinquishes their membership in the designated shared
        folder and will no longer have access to the folder.  A folder owner
        cannot relinquish membership in their own folder. This will run
        synchronously if leave_a_copy is false, and asynchronously if
        leave_a_copy is true.

        Route attributes:
            scope: sharing.write

        :param str shared_folder_id: The ID for the shared folder.
        :param bool leave_a_copy: Keep a copy of the folder's contents upon
            relinquishing membership. This must be set to false when the folder
            is within a team folder or another shared folder.
        :rtype: :class:`dropbox.sharing.LaunchEmptyResult`
        :raises: :class:`.exceptions.ApiError`

        If this raises, ApiError will contain:
            :class:`dropbox.sharing.RelinquishFolderMembershipError`
        """
        arg = sharing.RelinquishFolderMembershipArg(shared_folder_id,
                                                    leave_a_copy)
        r = self.request(
            sharing.relinquish_folder_membership,
            'sharing',
            arg,
            None,
        )
        return r

    def sharing_remove_file_member(self,
                                   file,
                                   member):
        """
        Identical to remove_file_member_2 but with less information returned.

        Route attributes:
            scope: sharing.write

        :param str file: File from which to remove members.
        :param member: Member to remove from this file. Note that even if an
            email is specified, it may result in the removal of a user (not an
            invitee) if the user's main account corresponds to that email
            address.
        :type member: :class:`dropbox.sharing.MemberSelector`
        :rtype: :class:`dropbox.sharing.FileMemberActionIndividualResult`
        :raises: :class:`.exceptions.ApiError`

        If this raises, ApiError will contain:
            :class:`dropbox.sharing.RemoveFileMemberError`
        """
        warnings.warn(
            'remove_file_member is deprecated. Use remove_file_member_2.',
            DeprecationWarning,
        )
        arg = sharing.RemoveFileMemberArg(file,
                                          member)
        r = self.request(
            sharing.remove_file_member,
            'sharing',
            arg,
            None,
        )
        return r

    def sharing_remove_file_member_2(self,
                                     file,
                                     member):
        """
        Removes a specified member from the file.

        Route attributes:
            scope: sharing.write

        :param str file: File from which to remove members.
        :param member: Member to remove from this file. Note that even if an
            email is specified, it may result in the removal of a user (not an
            invitee) if the user's main account corresponds to that email
            address.
        :type member: :class:`dropbox.sharing.MemberSelector`
        :rtype: :class:`dropbox.sharing.FileMemberRemoveActionResult`
        :raises: :class:`.exceptions.ApiError`

        If this raises, ApiError will contain:
            :class:`dropbox.sharing.RemoveFileMemberError`
        """
        arg = sharing.RemoveFileMemberArg(file,
                                          member)
        r = self.request(
            sharing.remove_file_member_2,
            'sharing',
            arg,
            None,
        )
        return r

    def sharing_remove_folder_member(self,
                                     shared_folder_id,
                                     member,
                                     leave_a_copy):
        """
        Allows an owner or editor (if the ACL update policy allows) of a shared
        folder to remove another member.

        Route attributes:
            scope: sharing.write

        :param str shared_folder_id: The ID for the shared folder.
        :param member: The member to remove from the folder.
        :type member: :class:`dropbox.sharing.MemberSelector`
        :param bool leave_a_copy: If true, the removed user will keep their copy
            of the folder after it's unshared, assuming it was mounted.
            Otherwise, it will be removed from their Dropbox. This must be set
            to false when removing a group, or when the folder is within a team
            folder or another shared folder.
        :rtype: :class:`dropbox.sharing.LaunchResultBase`
        :raises: :class:`.exceptions.ApiError`

        If this raises, ApiError will contain:
            :class:`dropbox.sharing.RemoveFolderMemberError`
        """
        arg = sharing.RemoveFolderMemberArg(shared_folder_id,
                                            member,
                                            leave_a_copy)
        r = self.request(
            sharing.remove_folder_member,
            'sharing',
            arg,
            None,
        )
        return r

    def sharing_revoke_shared_link(self,
                                   url):
        """
        Revoke a shared link. Note that even after revoking a shared link to a
        file, the file may be accessible if there are shared links leading to
        any of the file parent folders. To list all shared links that enable
        access to a specific file, you can use the
        :meth:`sharing_list_shared_links` with the file as the
        ``ListSharedLinksArg.path`` argument.

        Route attributes:
            scope: sharing.write

        :param str url: URL of the shared link.
        :rtype: None
        :raises: :class:`.exceptions.ApiError`

        If this raises, ApiError will contain:
            :class:`dropbox.sharing.RevokeSharedLinkError`
        """
        arg = sharing.RevokeSharedLinkArg(url)
        r = self.request(
            sharing.revoke_shared_link,
            'sharing',
            arg,
            None,
        )
        return None

    def sharing_set_access_inheritance(self,
                                       shared_folder_id,
                                       access_inheritance=sharing.AccessInheritance.inherit):
        """
        Change the inheritance policy of an existing Shared Folder. Only
        permitted for shared folders in a shared team root. If a
        ``ShareFolderLaunch.async_job_id`` is returned, you'll need to call
        :meth:`sharing_check_share_job_status` until the action completes to get
        the metadata for the folder.

        Route attributes:
            scope: sharing.write

        :param access_inheritance: The access inheritance settings for the
            folder.
        :type access_inheritance: :class:`dropbox.sharing.AccessInheritance`
        :param str shared_folder_id: The ID for the shared folder.
        :rtype: :class:`dropbox.sharing.ShareFolderLaunch`
        :raises: :class:`.exceptions.ApiError`

        If this raises, ApiError will contain:
            :class:`dropbox.sharing.SetAccessInheritanceError`
        """
        arg = sharing.SetAccessInheritanceArg(shared_folder_id,
                                              access_inheritance)
        r = self.request(
            sharing.set_access_inheritance,
            'sharing',
            arg,
            None,
        )
        return r

    def sharing_share_folder(self,
                             path,
                             acl_update_policy=None,
                             force_async=False,
                             member_policy=None,
                             shared_link_policy=None,
                             viewer_info_policy=None,
                             access_inheritance=sharing.AccessInheritance.inherit,
                             actions=None,
                             link_settings=None):
        """
        Share a folder with collaborators. Most sharing will be completed
        synchronously. Large folders will be completed asynchronously. To make
        testing the async case repeatable, set `ShareFolderArg.force_async`. If
        a ``ShareFolderLaunch.async_job_id`` is returned, you'll need to call
        :meth:`sharing_check_share_job_status` until the action completes to get
        the metadata for the folder.

        Route attributes:
            scope: sharing.write

        :param Nullable[List[:class:`dropbox.sharing.FolderAction`]] actions: A
            list of `FolderAction`s corresponding to `FolderPermission`s that
            should appear in the  response's
            ``SharedFolderMetadata.permissions`` field describing the actions
            the  authenticated user can perform on the folder.
        :param Nullable[:class:`dropbox.sharing.LinkSettings`] link_settings:
            Settings on the link for this folder.
        :rtype: :class:`dropbox.sharing.ShareFolderLaunch`
        :raises: :class:`.exceptions.ApiError`

        If this raises, ApiError will contain:
            :class:`dropbox.sharing.ShareFolderError`
        """
        arg = sharing.ShareFolderArg(path,
                                     acl_update_policy,
                                     force_async,
                                     member_policy,
                                     shared_link_policy,
                                     viewer_info_policy,
                                     access_inheritance,
                                     actions,
                                     link_settings)
        r = self.request(
            sharing.share_folder,
            'sharing',
            arg,
            None,
        )
        return r

    def sharing_transfer_folder(self,
                                shared_folder_id,
                                to_dropbox_id):
        """
        Transfer ownership of a shared folder to a member of the shared folder.
        User must have ``AccessLevel.owner`` access to the shared folder to
        perform a transfer.

        Route attributes:
            scope: sharing.write

        :param str shared_folder_id: The ID for the shared folder.
        :param str to_dropbox_id: A account or team member ID to transfer
            ownership to.
        :rtype: None
        :raises: :class:`.exceptions.ApiError`

        If this raises, ApiError will contain:
            :class:`dropbox.sharing.TransferFolderError`
        """
        arg = sharing.TransferFolderArg(shared_folder_id,
                                        to_dropbox_id)
        r = self.request(
            sharing.transfer_folder,
            'sharing',
            arg,
            None,
        )
        return None

    def sharing_unmount_folder(self,
                               shared_folder_id):
        """
        The current user unmounts the designated folder. They can re-mount the
        folder at a later time using :meth:`sharing_mount_folder`.

        Route attributes:
            scope: sharing.write

        :param str shared_folder_id: The ID for the shared folder.
        :rtype: None
        :raises: :class:`.exceptions.ApiError`

        If this raises, ApiError will contain:
            :class:`dropbox.sharing.UnmountFolderError`
        """
        arg = sharing.UnmountFolderArg(shared_folder_id)
        r = self.request(
            sharing.unmount_folder,
            'sharing',
            arg,
            None,
        )
        return None

    def sharing_unshare_file(self,
                             file):
        """
        Remove all members from this file. Does not remove inherited members.

        Route attributes:
            scope: sharing.write

        :param str file: The file to unshare.
        :rtype: None
        :raises: :class:`.exceptions.ApiError`

        If this raises, ApiError will contain:
            :class:`dropbox.sharing.UnshareFileError`
        """
        arg = sharing.UnshareFileArg(file)
        r = self.request(
            sharing.unshare_file,
            'sharing',
            arg,
            None,
        )
        return None

    def sharing_unshare_folder(self,
                               shared_folder_id,
                               leave_a_copy=False):
        """
        Allows a shared folder owner to unshare the folder. You'll need to call
        :meth:`sharing_check_job_status` to determine if the action has
        completed successfully.

        Route attributes:
            scope: sharing.write

        :param str shared_folder_id: The ID for the shared folder.
        :param bool leave_a_copy: If true, members of this shared folder will
            get a copy of this folder after it's unshared. Otherwise, it will be
            removed from their Dropbox. The current user, who is an owner, will
            always retain their copy.
        :rtype: :class:`dropbox.sharing.LaunchEmptyResult`
        :raises: :class:`.exceptions.ApiError`

        If this raises, ApiError will contain:
            :class:`dropbox.sharing.UnshareFolderError`
        """
        arg = sharing.UnshareFolderArg(shared_folder_id,
                                       leave_a_copy)
        r = self.request(
            sharing.unshare_folder,
            'sharing',
            arg,
            None,
        )
        return r

    def sharing_update_file_member(self,
                                   file,
                                   member,
                                   access_level):
        """
        Changes a member's access on a shared file.

        Route attributes:
            scope: sharing.write

        :param str file: File for which we are changing a member's access.
        :param member: The member whose access we are changing.
        :type member: :class:`dropbox.sharing.MemberSelector`
        :param access_level: The new access level for the member.
        :type access_level: :class:`dropbox.sharing.AccessLevel`
        :rtype: :class:`dropbox.sharing.MemberAccessLevelResult`
        :raises: :class:`.exceptions.ApiError`

        If this raises, ApiError will contain:
            :class:`dropbox.sharing.FileMemberActionError`
        """
        arg = sharing.UpdateFileMemberArgs(file,
                                           member,
                                           access_level)
        r = self.request(
            sharing.update_file_member,
            'sharing',
            arg,
            None,
        )
        return r

    def sharing_update_folder_member(self,
                                     shared_folder_id,
                                     member,
                                     access_level):
        """
        Allows an owner or editor of a shared folder to update another member's
        permissions.

        Route attributes:
            scope: sharing.write

        :param str shared_folder_id: The ID for the shared folder.
        :param member: The member of the shared folder to update.  Only the
            ``MemberSelector.dropbox_id`` may be set at this time.
        :type member: :class:`dropbox.sharing.MemberSelector`
        :param access_level: The new access level for ``member``.
            ``AccessLevel.owner`` is disallowed.
        :type access_level: :class:`dropbox.sharing.AccessLevel`
        :rtype: :class:`dropbox.sharing.MemberAccessLevelResult`
        :raises: :class:`.exceptions.ApiError`

        If this raises, ApiError will contain:
            :class:`dropbox.sharing.UpdateFolderMemberError`
        """
        arg = sharing.UpdateFolderMemberArg(shared_folder_id,
                                            member,
                                            access_level)
        r = self.request(
            sharing.update_folder_member,
            'sharing',
            arg,
            None,
        )
        return r

    def sharing_update_folder_policy(self,
                                     shared_folder_id,
                                     member_policy=None,
                                     acl_update_policy=None,
                                     viewer_info_policy=None,
                                     shared_link_policy=None,
                                     link_settings=None,
                                     actions=None):
        """
        Update the sharing policies for a shared folder. User must have
        ``AccessLevel.owner`` access to the shared folder to update its
        policies.

        Route attributes:
            scope: sharing.write

        :param str shared_folder_id: The ID for the shared folder.
        :param Nullable[:class:`dropbox.sharing.MemberPolicy`] member_policy:
            Who can be a member of this shared folder. Only applicable if the
            current user is on a team.
        :param Nullable[:class:`dropbox.sharing.AclUpdatePolicy`]
            acl_update_policy: Who can add and remove members of this shared
            folder.
        :param Nullable[:class:`dropbox.sharing.ViewerInfoPolicy`]
            viewer_info_policy: Who can enable/disable viewer info for this
            shared folder.
        :param Nullable[:class:`dropbox.sharing.SharedLinkPolicy`]
            shared_link_policy: The policy to apply to shared links created for
            content inside this shared folder. The current user must be on a
            team to set this policy to ``SharedLinkPolicy.members``.
        :param Nullable[:class:`dropbox.sharing.LinkSettings`] link_settings:
            Settings on the link for this folder.
        :param Nullable[List[:class:`dropbox.sharing.FolderAction`]] actions: A
            list of `FolderAction`s corresponding to `FolderPermission`s that
            should appear in the  response's
            ``SharedFolderMetadata.permissions`` field describing the actions
            the  authenticated user can perform on the folder.
        :rtype: :class:`dropbox.sharing.SharedFolderMetadata`
        :raises: :class:`.exceptions.ApiError`

        If this raises, ApiError will contain:
            :class:`dropbox.sharing.UpdateFolderPolicyError`
        """
        arg = sharing.UpdateFolderPolicyArg(shared_folder_id,
                                            member_policy,
                                            acl_update_policy,
                                            viewer_info_policy,
                                            shared_link_policy,
                                            link_settings,
                                            actions)
        r = self.request(
            sharing.update_folder_policy,
            'sharing',
            arg,
            None,
        )
        return r

    # ------------------------------------------
    # Routes in team namespace

    # ------------------------------------------
    # Routes in team_log namespace

    # ------------------------------------------
    # Routes in users namespace

    def users_features_get_values(self,
                                  features):
        """
        Get a list of feature values that may be configured for the current
        account.

        Route attributes:
            scope: account_info.read

        :param List[:class:`dropbox.users.UserFeature`] features: A list of
            features in :class:`dropbox.users.UserFeature`. If the list is
            empty, this route will return
            :class:`dropbox.users.UserFeaturesGetValuesBatchError`.
        :rtype: :class:`dropbox.users.UserFeaturesGetValuesBatchResult`
        :raises: :class:`.exceptions.ApiError`

        If this raises, ApiError will contain:
            :class:`dropbox.users.UserFeaturesGetValuesBatchError`
        """
        arg = users.UserFeaturesGetValuesBatchArg(features)
        r = self.request(
            users.features_get_values,
            'users',
            arg,
            None,
        )
        return r

    def users_get_account(self,
                          account_id):
        """
        Get information about a user's account.

        Route attributes:
            scope: sharing.read

        :param str account_id: A user's account identifier.
        :rtype: :class:`dropbox.users.BasicAccount`
        :raises: :class:`.exceptions.ApiError`

        If this raises, ApiError will contain:
            :class:`dropbox.users.GetAccountError`
        """
        arg = users.GetAccountArg(account_id)
        r = self.request(
            users.get_account,
            'users',
            arg,
            None,
        )
        return r

    def users_get_account_batch(self,
                                account_ids):
        """
        Get information about multiple user accounts.  At most 300 accounts may
        be queried per request.

        Route attributes:
            scope: sharing.read

        :param List[str] account_ids: List of user account identifiers.  Should
            not contain any duplicate account IDs.
        :rtype: List[:class:`dropbox.users.BasicAccount`]
        :raises: :class:`.exceptions.ApiError`

        If this raises, ApiError will contain:
            :class:`dropbox.users.GetAccountBatchError`
        """
        arg = users.GetAccountBatchArg(account_ids)
        r = self.request(
            users.get_account_batch,
            'users',
            arg,
            None,
        )
        return r

    def users_get_current_account(self):
        """
        Get information about the current user's account.

        Route attributes:
            scope: account_info.read

        :rtype: :class:`dropbox.users.FullAccount`
        """
        arg = None
        r = self.request(
            users.get_current_account,
            'users',
            arg,
            None,
        )
        return r

    def users_get_space_usage(self):
        """
        Get the space usage information for the current user's account.

        Route attributes:
            scope: account_info.read

        :rtype: :class:`dropbox.users.SpaceUsage`
        """
        arg = None
        r = self.request(
            users.get_space_usage,
            'users',
            arg,
            None,
        )
        return r

