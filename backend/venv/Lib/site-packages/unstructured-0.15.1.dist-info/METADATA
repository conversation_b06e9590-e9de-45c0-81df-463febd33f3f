Metadata-Version: 2.1
Name: unstructured
Version: 0.15.1
Summary: A library that prepares raw documents for downstream ML tasks.
Home-page: https://github.com/Unstructured-IO/unstructured
Author: Unstructured Technologies
Author-email: <EMAIL>
License: Apache-2.0
Keywords: NLP PDF HTML CV XML parsing preprocessing
Platform: UNKNOWN
Classifier: Development Status :: 4 - Beta
Classifier: Intended Audience :: Developers
Classifier: Intended Audience :: Education
Classifier: Intended Audience :: Science/Research
Classifier: License :: OSI Approved :: Apache Software License
Classifier: Operating System :: OS Independent
Classifier: Programming Language :: Python :: 3
Classifier: Programming Language :: Python :: 3.9
Classifier: Programming Language :: Python :: 3.10
Classifier: Programming Language :: Python :: 3.11
Classifier: Programming Language :: Python :: 3.12
Classifier: Topic :: Scientific/Engineering :: Artificial Intelligence
Requires-Python: >=3.9.0,<3.13
Description-Content-Type: text/markdown
Requires-Dist: chardet
Requires-Dist: filetype
Requires-Dist: python-magic
Requires-Dist: lxml
Requires-Dist: nltk
Requires-Dist: tabulate
Requires-Dist: requests
Requires-Dist: beautifulsoup4
Requires-Dist: emoji
Requires-Dist: dataclasses-json
Requires-Dist: python-iso639
Requires-Dist: langdetect
Requires-Dist: numpy (<2)
Requires-Dist: rapidfuzz
Requires-Dist: backoff
Requires-Dist: typing-extensions
Requires-Dist: unstructured-client
Requires-Dist: wrapt
Requires-Dist: tqdm
Requires-Dist: psutil
Provides-Extra: airtable
Requires-Dist: pyairtable ; extra == 'airtable'
Provides-Extra: all-docs
Requires-Dist: python-pptx (<=0.6.23) ; extra == 'all-docs'
Requires-Dist: xlrd ; extra == 'all-docs'
Requires-Dist: python-docx (>=1.1.2) ; extra == 'all-docs'
Requires-Dist: pypdf ; extra == 'all-docs'
Requires-Dist: pikepdf ; extra == 'all-docs'
Requires-Dist: pypandoc ; extra == 'all-docs'
Requires-Dist: pandas ; extra == 'all-docs'
Requires-Dist: unstructured-inference (==0.7.36) ; extra == 'all-docs'
Requires-Dist: onnx ; extra == 'all-docs'
Requires-Dist: pdf2image ; extra == 'all-docs'
Requires-Dist: unstructured.pytesseract (>=0.3.12) ; extra == 'all-docs'
Requires-Dist: google-cloud-vision ; extra == 'all-docs'
Requires-Dist: python-oxmsg ; extra == 'all-docs'
Requires-Dist: pdfminer.six ; extra == 'all-docs'
Requires-Dist: pytesseract ; extra == 'all-docs'
Requires-Dist: pillow-heif ; extra == 'all-docs'
Requires-Dist: networkx ; extra == 'all-docs'
Requires-Dist: openpyxl ; extra == 'all-docs'
Requires-Dist: markdown ; extra == 'all-docs'
Requires-Dist: effdet ; extra == 'all-docs'
Provides-Extra: astra
Requires-Dist: astrapy ; extra == 'astra'
Provides-Extra: azure
Requires-Dist: adlfs ; extra == 'azure'
Requires-Dist: fsspec ; extra == 'azure'
Provides-Extra: azure-cognitive-search
Requires-Dist: azure-search-documents ; extra == 'azure-cognitive-search'
Provides-Extra: bedrock
Requires-Dist: boto3 ; extra == 'bedrock'
Requires-Dist: langchain-community ; extra == 'bedrock'
Provides-Extra: biomed
Requires-Dist: bs4 ; extra == 'biomed'
Provides-Extra: box
Requires-Dist: boxfs ; extra == 'box'
Requires-Dist: fsspec ; extra == 'box'
Provides-Extra: chroma
Requires-Dist: chromadb ; extra == 'chroma'
Requires-Dist: importlib-metadata (>=7.1.0) ; extra == 'chroma'
Requires-Dist: typer (<=0.9.0) ; extra == 'chroma'
Provides-Extra: clarifai
Requires-Dist: clarifai ; extra == 'clarifai'
Provides-Extra: confluence
Requires-Dist: atlassian-python-api ; extra == 'confluence'
Provides-Extra: csv
Requires-Dist: pandas ; extra == 'csv'
Provides-Extra: databricks-volumes
Requires-Dist: databricks-sdk ; extra == 'databricks-volumes'
Provides-Extra: delta-table
Requires-Dist: deltalake ; extra == 'delta-table'
Requires-Dist: fsspec ; extra == 'delta-table'
Provides-Extra: discord
Requires-Dist: discord-py ; extra == 'discord'
Provides-Extra: doc
Requires-Dist: python-docx (>=1.1.2) ; extra == 'doc'
Provides-Extra: docx
Requires-Dist: python-docx (>=1.1.2) ; extra == 'docx'
Provides-Extra: dropbox
Requires-Dist: dropboxdrivefs ; extra == 'dropbox'
Requires-Dist: fsspec ; extra == 'dropbox'
Provides-Extra: elasticsearch
Requires-Dist: elasticsearch[async] ; extra == 'elasticsearch'
Provides-Extra: embed-huggingface
Requires-Dist: langchain-huggingface ; extra == 'embed-huggingface'
Provides-Extra: embed-octoai
Requires-Dist: openai ; extra == 'embed-octoai'
Requires-Dist: tiktoken ; extra == 'embed-octoai'
Provides-Extra: embed-vertexai
Requires-Dist: langchain ; extra == 'embed-vertexai'
Requires-Dist: langchain-community ; extra == 'embed-vertexai'
Requires-Dist: langchain-google-vertexai ; extra == 'embed-vertexai'
Provides-Extra: embed-voyageai
Requires-Dist: langchain ; extra == 'embed-voyageai'
Requires-Dist: langchain-voyageai ; extra == 'embed-voyageai'
Provides-Extra: epub
Requires-Dist: pypandoc ; extra == 'epub'
Provides-Extra: gcs
Requires-Dist: gcsfs ; extra == 'gcs'
Requires-Dist: fsspec ; extra == 'gcs'
Requires-Dist: bs4 ; extra == 'gcs'
Provides-Extra: github
Requires-Dist: pygithub (>1.58.0) ; extra == 'github'
Provides-Extra: gitlab
Requires-Dist: python-gitlab ; extra == 'gitlab'
Provides-Extra: google-drive
Requires-Dist: google-api-python-client ; extra == 'google-drive'
Provides-Extra: hubspot
Requires-Dist: hubspot-api-client ; extra == 'hubspot'
Requires-Dist: urllib3 ; extra == 'hubspot'
Provides-Extra: huggingface
Requires-Dist: langdetect ; extra == 'huggingface'
Requires-Dist: sacremoses ; extra == 'huggingface'
Requires-Dist: sentencepiece ; extra == 'huggingface'
Requires-Dist: torch ; extra == 'huggingface'
Requires-Dist: transformers ; extra == 'huggingface'
Provides-Extra: image
Requires-Dist: onnx ; extra == 'image'
Requires-Dist: pdf2image ; extra == 'image'
Requires-Dist: pdfminer.six ; extra == 'image'
Requires-Dist: pikepdf ; extra == 'image'
Requires-Dist: pillow-heif ; extra == 'image'
Requires-Dist: pypdf ; extra == 'image'
Requires-Dist: pytesseract ; extra == 'image'
Requires-Dist: google-cloud-vision ; extra == 'image'
Requires-Dist: effdet ; extra == 'image'
Requires-Dist: unstructured-inference (==0.7.36) ; extra == 'image'
Requires-Dist: unstructured.pytesseract (>=0.3.12) ; extra == 'image'
Provides-Extra: jira
Requires-Dist: atlassian-python-api ; extra == 'jira'
Provides-Extra: kafka
Requires-Dist: confluent-kafka ; extra == 'kafka'
Provides-Extra: local-inference
Requires-Dist: python-pptx (<=0.6.23) ; extra == 'local-inference'
Requires-Dist: xlrd ; extra == 'local-inference'
Requires-Dist: python-docx (>=1.1.2) ; extra == 'local-inference'
Requires-Dist: pypdf ; extra == 'local-inference'
Requires-Dist: pikepdf ; extra == 'local-inference'
Requires-Dist: pypandoc ; extra == 'local-inference'
Requires-Dist: pandas ; extra == 'local-inference'
Requires-Dist: unstructured-inference (==0.7.36) ; extra == 'local-inference'
Requires-Dist: onnx ; extra == 'local-inference'
Requires-Dist: pdf2image ; extra == 'local-inference'
Requires-Dist: unstructured.pytesseract (>=0.3.12) ; extra == 'local-inference'
Requires-Dist: google-cloud-vision ; extra == 'local-inference'
Requires-Dist: python-oxmsg ; extra == 'local-inference'
Requires-Dist: pdfminer.six ; extra == 'local-inference'
Requires-Dist: pytesseract ; extra == 'local-inference'
Requires-Dist: pillow-heif ; extra == 'local-inference'
Requires-Dist: networkx ; extra == 'local-inference'
Requires-Dist: openpyxl ; extra == 'local-inference'
Requires-Dist: markdown ; extra == 'local-inference'
Requires-Dist: effdet ; extra == 'local-inference'
Provides-Extra: md
Requires-Dist: markdown ; extra == 'md'
Provides-Extra: mongodb
Requires-Dist: pymongo ; extra == 'mongodb'
Provides-Extra: msg
Requires-Dist: python-oxmsg ; extra == 'msg'
Provides-Extra: notion
Requires-Dist: notion-client ; extra == 'notion'
Requires-Dist: htmlBuilder ; extra == 'notion'
Provides-Extra: odt
Requires-Dist: python-docx (>=1.1.2) ; extra == 'odt'
Requires-Dist: pypandoc ; extra == 'odt'
Provides-Extra: onedrive
Requires-Dist: msal ; extra == 'onedrive'
Requires-Dist: Office365-REST-Python-Client ; extra == 'onedrive'
Requires-Dist: bs4 ; extra == 'onedrive'
Provides-Extra: openai
Requires-Dist: langchain-openai ; extra == 'openai'
Provides-Extra: opensearch
Requires-Dist: opensearch-py ; extra == 'opensearch'
Provides-Extra: org
Requires-Dist: pypandoc ; extra == 'org'
Provides-Extra: outlook
Requires-Dist: msal ; extra == 'outlook'
Requires-Dist: Office365-REST-Python-Client ; extra == 'outlook'
Provides-Extra: paddleocr
Requires-Dist: unstructured.paddleocr (==2.8.0.1) ; extra == 'paddleocr'
Provides-Extra: pdf
Requires-Dist: onnx ; extra == 'pdf'
Requires-Dist: pdf2image ; extra == 'pdf'
Requires-Dist: pdfminer.six ; extra == 'pdf'
Requires-Dist: pikepdf ; extra == 'pdf'
Requires-Dist: pillow-heif ; extra == 'pdf'
Requires-Dist: pypdf ; extra == 'pdf'
Requires-Dist: pytesseract ; extra == 'pdf'
Requires-Dist: google-cloud-vision ; extra == 'pdf'
Requires-Dist: effdet ; extra == 'pdf'
Requires-Dist: unstructured-inference (==0.7.36) ; extra == 'pdf'
Requires-Dist: unstructured.pytesseract (>=0.3.12) ; extra == 'pdf'
Provides-Extra: pinecone
Requires-Dist: pinecone-client (>=3.7.1) ; extra == 'pinecone'
Provides-Extra: postgres
Requires-Dist: psycopg2-binary ; extra == 'postgres'
Provides-Extra: ppt
Requires-Dist: python-pptx (<=0.6.23) ; extra == 'ppt'
Provides-Extra: pptx
Requires-Dist: python-pptx (<=0.6.23) ; extra == 'pptx'
Provides-Extra: qdrant
Requires-Dist: qdrant-client ; extra == 'qdrant'
Provides-Extra: reddit
Requires-Dist: praw ; extra == 'reddit'
Provides-Extra: rst
Requires-Dist: pypandoc ; extra == 'rst'
Provides-Extra: rtf
Requires-Dist: pypandoc ; extra == 'rtf'
Provides-Extra: s3
Requires-Dist: s3fs ; extra == 's3'
Requires-Dist: fsspec ; extra == 's3'
Provides-Extra: salesforce
Requires-Dist: simple-salesforce ; extra == 'salesforce'
Provides-Extra: sftp
Requires-Dist: fsspec ; extra == 'sftp'
Requires-Dist: paramiko ; extra == 'sftp'
Provides-Extra: sharepoint
Requires-Dist: msal ; extra == 'sharepoint'
Requires-Dist: Office365-REST-Python-Client ; extra == 'sharepoint'
Provides-Extra: singlestore
Requires-Dist: singlestoredb ; extra == 'singlestore'
Provides-Extra: slack
Requires-Dist: slack-sdk ; extra == 'slack'
Provides-Extra: tsv
Requires-Dist: pandas ; extra == 'tsv'
Provides-Extra: weaviate
Requires-Dist: weaviate-client ; extra == 'weaviate'
Provides-Extra: wikipedia
Requires-Dist: wikipedia ; extra == 'wikipedia'
Provides-Extra: xlsx
Requires-Dist: openpyxl ; extra == 'xlsx'
Requires-Dist: pandas ; extra == 'xlsx'
Requires-Dist: xlrd ; extra == 'xlsx'
Requires-Dist: networkx ; extra == 'xlsx'

<h3 align="center">
  <img
    src="https://raw.githubusercontent.com/Unstructured-IO/unstructured/main/img/unstructured_logo.png"
    height="200"
  >
</h3>

<div align="center">

  <a href="https://github.com/Unstructured-IO/unstructured/blob/main/LICENSE.md">![https://pypi.python.org/pypi/unstructured/](https://img.shields.io/pypi/l/unstructured.svg)</a>
  <a href="https://pypi.python.org/pypi/unstructured/">![https://pypi.python.org/pypi/unstructured/](https://img.shields.io/pypi/pyversions/unstructured.svg)</a>
  <a href="https://GitHub.com/unstructured-io/unstructured/graphs/contributors">![https://GitHub.com/unstructured-io/unstructured.js/graphs/contributors](https://img.shields.io/github/contributors/unstructured-io/unstructured)</a>
  <a href="https://github.com/Unstructured-IO/unstructured/blob/main/CODE_OF_CONDUCT.md">![code_of_conduct.md](https://img.shields.io/badge/Contributor%20Covenant-2.1-4baaaa.svg) </a>
  <a href="https://GitHub.com/unstructured-io/unstructured/releases">![https://GitHub.com/unstructured-io/unstructured.js/releases](https://img.shields.io/github/release/unstructured-io/unstructured)</a>
  <a href="https://pypi.python.org/pypi/unstructured/">![https://github.com/Naereen/badges/](https://badgen.net/badge/Open%20Source%20%3F/Yes%21/blue?icon=github)</a>
  [![Downloads](https://static.pepy.tech/badge/unstructured)](https://pepy.tech/project/unstructured)
  [![Downloads](https://static.pepy.tech/badge/unstructured/month)](https://pepy.tech/project/unstructured)
  <a
   href="https://www.phorm.ai/query?projectId=34efc517-2201-4376-af43-40c4b9da3dc5">
	<img src="https://img.shields.io/badge/Phorm-Ask_AI-%23F2777A.svg?&logo=data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNSIgaGVpZ2h0PSI0IiBmaWxsPSJub25lIiB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciPgogIDxwYXRoIGQ9Ik00LjQzIDEuODgyYTEuNDQgMS40NCAwIDAgMS0uMDk4LjQyNmMtLjA1LjEyMy0uMTE1LjIzLS4xOTIuMzIyLS4wNzUuMDktLjE2LjE2NS0uMjU1LjIyNmExLjM1MyAxLjM1MyAwIDAgMS0uNTk1LjIxMmMtLjA5OS4wMTItLjE5Mi4wMTQtLjI3OS4wMDZsLTEuNTkzLS4xNHYtLjQwNmgxLjY1OGMuMDkuMDAxLjE3LS4xNjkuMjQ2LS4xOTFhLjYwMy42MDMgMCAwIDAgLjItLjEwNi41MjkuNTI5IDAgMCAwIC4xMzgtLjE3LjY1NC42NTQgMCAwIDAgLjA2NS0uMjRsLjAyOC0uMzJhLjkzLjkzIDAgMCAwLS4wMzYtLjI0OS41NjcuNTY3IDAgMCAwLS4xMDMtLjIuNTAyLjUwMiAwIDAgMC0uMTY4LS4xMzguNjA4LjYwOCAwIDAgMC0uMjQtLjA2N0wyLjQzNy43MjkgMS42MjUuNjcxYS4zMjIuMzIyIDAgMCAwLS4yMzIuMDU4LjM3NS4zNzUgMCAwIDAtLjExNi4yMzJsLS4xMTYgMS40NS0uMDU4LjY5Ny0uMDU4Ljc1NEwuNzA1IDRsLS4zNTctLjA3OUwuNjAyLjkwNkMuNjE3LjcyNi42NjMuNTc0LjczOS40NTRhLjk1OC45NTggMCAwIDEgLjI3NC0uMjg1Ljk3MS45NzEgMCAwIDEgLjMzNy0uMTRjLjExOS0uMDI2LjIyNy0uMDM0LjMyNS0uMDI2TDMuMjMyLjE2Yy4xNTkuMDE0LjMzNi4wMy40NTkuMDgyYTEuMTczIDEuMTczIDAgMCAxIC41NDUuNDQ3Yy4wNi4wOTQuMTA5LjE5Mi4xNDQuMjkzYTEuMzkyIDEuMzkyIDAgMCAxIC4wNzguNThsLS4wMjkuMzJaIiBmaWxsPSIjRjI3NzdBIi8+CiAgPHBhdGggZD0iTTQuMDgyIDIuMDA3YTEuNDU1IDEuNDU1IDAgMCAxLS4wOTguNDI3Yy0uMDUuMTI0LS4xMTQuMjMyLS4xOTIuMzI0YTEuMTMgMS4xMyAwIDAgMS0uMjU0LjIyNyAxLjM1MyAxLjM1MyAwIDAgMS0uNTk1LjIxNGMtLjEuMDEyLS4xOTMuMDE0LS4yOC4wMDZsLTEuNTYtLjEwOC4wMzQtLjQwNi4wMy0uMzQ4IDEuNTU5LjE1NGMuMDkgMCAuMTczLS4wMS4yNDgtLjAzM2EuNjAzLjYwMyAwIDAgMCAuMi0uMTA2LjUzMi41MzIgMCAwIDAgLjEzOS0uMTcyLjY2LjY2IDAgMCAwIC4wNjQtLjI0MWwuMDI5LS4zMjFhLjk0Ljk0IDAgMCAwLS4wMzYtLjI1LjU3LjU3IDAgMCAwLS4xMDMtLjIwMi41MDIuNTAyIDAgMCAwLS4xNjgtLjEzOC42MDUuNjA1IDAgMCAwLS4yNC0uMDY3TDEuMjczLjgyN2MtLjA5NC0uMDA4LS4xNjguMDEtLjIyMS4wNTUtLjA1My4wNDUtLjA4NC4xMTQtLjA5Mi4yMDZMLjcwNSA0IDAgMy45MzhsLjI1NS0yLjkxMUExLjAxIDEuMDEgMCAwIDEgLjM5My41NzIuOTYyLjk2MiAwIDAgMSAuNjY2LjI4NmEuOTcuOTcgMCAwIDEgLjMzOC0uMTRDMS4xMjIuMTIgMS4yMy4xMSAxLjMyOC4xMTlsMS41OTMuMTRjLjE2LjAxNC4zLjA0Ny40MjMuMWExLjE3IDEuMTcgMCAwIDEgLjU0NS40NDhjLjA2MS4wOTUuMTA5LjE5My4xNDQuMjk1YTEuNDA2IDEuNDA2IDAgMCAxIC4wNzcuNTgzbC0uMDI4LjMyMloiIGZpbGw9IndoaXRlIi8+CiAgPHBhdGggZD0iTTQuMDgyIDIuMDA3YTEuNDU1IDEuNDU1IDAgMCAxLS4wOTguNDI3Yy0uMDUuMTI0LS4xMTQuMjMyLS4xOTIuMzI0YTEuMTMgMS4xMyAwIDAgMS0uMjU0LjIyNyAxLjM1MyAxLjM1MyAwIDAgMS0uNTk1LjIxNGMtLjEuMDEyLS4xOTMuMDE0LS4yOC4wMDZsLTEuNTYtLjEwOC4wMzQtLjQwNi4wMy0uMzQ4IDEuNTU5LjE1NGMuMDkgMCAuMTczLS4wMS4yNDgtLjAzM2EuNjAzLjYwMyAwIDAgMCAuMi0uMTA2LjUzMi41MzIgMCAwIDAgLjEzOS0uMTcyLjY2LjY2IDAgMCAwIC4wNjQtLjI0MWwuMDI5LS4zMjFhLjk0Ljk0IDAgMCAwLS4wMzYtLjI1LjU3LjU3IDAgMCAwLS4xMDMtLjIwMi41MDIuNTAyIDAgMCAwLS4xNjgtLjEzOC42MDUuNjA1IDAgMCAwLS4yNC0uMDY3TDEuMjczLjgyN2MtLjA5NC0uMDA4LS4xNjguMDEtLjIyMS4wNTUtLjA1My4wNDUtLjA4NC4xMTQtLjA5Mi4yMDZMLjcwNSA0IDAgMy45MzhsLjI1NS0yLjkxMUExLjAxIDEuMDEgMCAwIDEgLjM5My41NzIuOTYyLjk2MiAwIDAgMSAuNjY2LjI4NmEuOTcuOTcgMCAwIDEgLjMzOC0uMTRDMS4xMjIuMTIgMS4yMy4xMSAxLjMyOC4xMTlsMS41OTMuMTRjLjE2LjAxNC4zLjA0Ny40MjMuMWExLjE3IDEuMTcgMCAwIDEgLjU0NS40NDhjLjA2MS4wOTUuMTA5LjE5My4xNDQuMjk1YTEuNDA2IDEuNDA2IDAgMCAxIC4wNzcuNTgzbC0uMDI4LjMyMloiIGZpbGw9IndoaXRlIi8+Cjwvc3ZnPgo=" />
   </a>

</div>

<div>
  <p align="center">
  <a
  href="https://short.unstructured.io/pzw05l7">
    <img src="https://img.shields.io/badge/JOIN US ON SLACK-4A154B?style=for-the-badge&logo=slack&logoColor=white" />
  </a>
  <a href="https://www.linkedin.com/company/unstructuredio/">
    <img src="https://img.shields.io/badge/LinkedIn-0077B5?style=for-the-badge&logo=linkedin&logoColor=white" />
  </a>
</div>

<h2 align="center">
  <p>Open-Source Pre-Processing Tools for Unstructured Data</p>
</h2>

The `unstructured` library provides open-source components for ingesting and pre-processing images and text documents, such as PDFs, HTML, Word docs, and [many more](https://docs.unstructured.io/open-source/core-functionality/partitioning). The use cases of `unstructured` revolve around streamlining and optimizing the data processing workflow for LLMs. `unstructured` modular functions and connectors form a cohesive system that simplifies data ingestion and pre-processing, making it adaptable to different platforms and efficient in transforming unstructured data into structured outputs.

## Try the Unstructured Serverless API!

Looking for better pre-processing performance and less setup?
Check out our new [Serverless API](https://unstructured.io/api-key-hosted)!
The Unstructured Serverless API is our most performant API yet, delivering a more responsive,
production-grade solution to better support your business and LLM needs.
Head to our [signup page](https://app.unstructured.io/) page to get started for
free.

## :eight_pointed_black_star: Quick Start

There are several ways to use the `unstructured` library:
* [Run the library in a container](https://github.com/Unstructured-IO/unstructured#run-the-library-in-a-container) or
* Install the library
    1. [Install from PyPI](https://github.com/Unstructured-IO/unstructured#installing-the-library)
    2. [Install for local development](https://github.com/Unstructured-IO/unstructured#installation-instructions-for-local-development)
* For installation with `conda` on Windows system, please refer to the [documentation](https://unstructured-io.github.io/unstructured/installing.html#installation-with-conda-on-windows)

### Run the library in a container

The following instructions are intended to help you get up and running using Docker to interact with `unstructured`.
See [here](https://docs.docker.com/get-docker/) if you don't already have docker installed on your machine.

NOTE: we build multi-platform images to support both x86_64 and Apple silicon hardware. `docker pull` should download the corresponding image for your architecture, but you can specify with `--platform` (e.g. `--platform linux/amd64`) if needed.

We build Docker images for all pushes to `main`. We tag each image with the corresponding short commit hash (e.g. `fbc7a69`) and the application version (e.g. `0.5.5-dev1`). We also tag the most recent image with `latest`. To leverage this, `docker pull` from our image repository.

```bash
docker pull downloads.unstructured.io/unstructured-io/unstructured:latest
```

Once pulled, you can create a container from this image and shell to it.

```bash
# create the container
docker run -dt --name unstructured downloads.unstructured.io/unstructured-io/unstructured:latest

# this will drop you into a bash shell where the Docker image is running
docker exec -it unstructured bash
```

You can also build your own Docker image. Note that the base image is `wolfi-base`, which is
updated regularly. If you are building the image locally, it is possible `docker-build` could
fail due to upstream changes in `wolfi-base`.

If you only plan on parsing one type of data you can speed up building the image by commenting out some
of the packages/requirements necessary for other data types. See Dockerfile to know which lines are necessary
for your use case.

```bash
make docker-build

# this will drop you into a bash shell where the Docker image is running
make docker-start-bash
```

Once in the running container, you can try things directly in Python interpreter's interactive mode.
```bash
# this will drop you into a python console so you can run the below partition functions
python3

>>> from unstructured.partition.pdf import partition_pdf
>>> elements = partition_pdf(filename="example-docs/layout-parser-paper-fast.pdf")

>>> from unstructured.partition.text import partition_text
>>> elements = partition_text(filename="example-docs/fake-text.txt")
```

### Installing the library
Use the following instructions to get up and running with `unstructured` and test your
installation.

- Install the Python SDK to support all document types with `pip install "unstructured[all-docs]"`
  - For plain text files, HTML, XML, JSON and Emails that do not require any extra dependencies, you can run `pip install unstructured`
  - To process other doc types, you can install the extras required for those documents, such as `pip install "unstructured[docx,pptx]"`
- Install the following system dependencies if they are not already available on your system.
  Depending on what document types you're parsing, you may not need all of these.
    - `libmagic-dev` (filetype detection)
    - `poppler-utils` (images and PDFs)
    - `tesseract-ocr` (images and PDFs, install `tesseract-lang` for additional language support)
    - `libreoffice` (MS Office docs)
    - `pandoc` (EPUBs, RTFs and Open Office docs). Please note that to handle RTF files, you need version `2.14.2` or newer. Running either `make install-pandoc` or `./scripts/install-pandoc.sh` will install the correct version for you.

- For suggestions on how to install on the Windows and to learn about dependencies for other features, see the
  installation documentation [here](https://unstructured-io.github.io/unstructured/installing.html).

At this point, you should be able to run the following code:

```python
from unstructured.partition.auto import partition

elements = partition(filename="example-docs/eml/fake-email.eml")
print("\n\n".join([str(el) for el in elements]))
```

### Installation Instructions for Local Development

The following instructions are intended to help you get up and running with `unstructured`
locally if you are planning to contribute to the project.

* Using `pyenv` to manage virtualenv's is recommended but not necessary
	* Mac install instructions. See [here](https://github.com/Unstructured-IO/community#mac--homebrew) for more detailed instructions.
		* `brew install pyenv-virtualenv`
	  * `pyenv install 3.10`
  * Linux instructions are available [here](https://github.com/Unstructured-IO/community#linux).

* Create a virtualenv to work in and activate it, e.g. for one named `unstructured`:

	`pyenv  virtualenv 3.10 unstructured` <br />
	`pyenv activate unstructured`

* Run `make install`

* Optional:
  * To install models and dependencies for processing images and PDFs locally, run `make install-local-inference`.
  * For processing image files, `tesseract` is required. See [here](https://tesseract-ocr.github.io/tessdoc/Installation.html) for installation instructions.
  * For processing PDF files, `tesseract` and `poppler` are required. The [pdf2image docs](https://pdf2image.readthedocs.io/en/latest/installation.html) have instructions on installing `poppler` across various platforms.

Additionally, if you're planning to contribute to `unstructured`, we provide you an optional `pre-commit` configuration
file to ensure your code matches the formatting and linting standards used in `unstructured`.
If you'd prefer not to have code changes auto-tidied before every commit, you can use  `make check` to see
whether any linting or formatting changes should be applied, and `make tidy` to apply them.

If using the optional `pre-commit`, you'll just need to install the hooks with `pre-commit install` since the
`pre-commit` package is installed as part of `make install` mentioned above. Finally, if you decided to use `pre-commit`
you can also uninstall the hooks with `pre-commit uninstall`.

In addition to develop in your local OS we also provide a helper to use docker providing a development environment:

```bash
make docker-start-dev
```

This starts a docker container with your local repo mounted to `/mnt/local_unstructured`. This docker image allows you to develop without worrying about your OS's compatibility with the repo and its dependencies.

## :clap: Quick Tour

### Documentation
For more comprehensive documentation, visit https://docs.unstructured.io . You can also learn
more about our other products on the documentation page, including our SaaS API.

Here are a few pages from the [Open Source documentation page](https://docs.unstructured.io/open-source/introduction/overview)
that are helpful for new users to review:

- [Quick Start](https://docs.unstructured.io/open-source/introduction/quick-start)
- [Using the `unstructured` open source package](https://docs.unstructured.io/open-source/core-functionality/overview)
- [Connectors](https://docs.unstructured.io/open-source/ingest/overview)
- [Concepts](https://docs.unstructured.io/open-source/concepts/document-elements)
- [Integrations](https://docs.unstructured.io/open-source/integrations)


### PDF Document Parsing Example
The following examples show how to get started with the `unstructured` library. The easiest way to parse a document in unstructured is to use the `partition` function. If you use `partition` function, `unstructured` will detect the file type and route it to the appropriate file-specific partitioning function. If you are using the `partition` function, you may need to install additional dependencies per doc type.
For example, to install docx dependencies you need to run `pip install "unstructured[docx]"`.
See our  [installation guide](https://docs.unstructured.io/open-source/installation/full-installation) for more details.

```python
from unstructured.partition.auto import partition

elements = partition("example-docs/layout-parser-paper.pdf")
```

Run `print("\n\n".join([str(el) for el in elements]))` to get a string representation of the
output, which looks like:

```

LayoutParser : A Uniﬁed Toolkit for Deep Learning Based Document Image Analysis

Zejiang Shen 1 ( (cid:0) ), Ruochen Zhang 2 , Melissa Dell 3 , Benjamin Charles Germain Lee 4 , Jacob Carlson 3 , and
Weining Li 5

Abstract. Recent advances in document image analysis (DIA) have been primarily driven by the application of neural
networks. Ideally, research outcomes could be easily deployed in production and extended for further investigation.
However, various factors like loosely organized codebases and sophisticated model conﬁgurations complicate the easy
reuse of important innovations by a wide audience. Though there have been ongoing eﬀorts to improve reusability and
simplify deep learning (DL) model development in disciplines like natural language processing and computer vision, none
of them are optimized for challenges in the domain of DIA. This represents a major gap in the existing toolkit, as DIA
is central to academic research across a wide range of disciplines in the social sciences and humanities. This paper
introduces LayoutParser, an open-source library for streamlining the usage of DL in DIA research and applications.
The core LayoutParser library comes with a set of simple and intuitive interfaces for applying and customizing DL models
for layout detection, character recognition, and many other document processing tasks. To promote extensibility,
LayoutParser also incorporates a community platform for sharing both pre-trained models and full document digitization
pipelines. We demonstrate that LayoutParser is helpful for both lightweight and large-scale digitization pipelines in
real-word use cases. The library is publicly available at https://layout-parser.github.io

Keywords: Document Image Analysis · Deep Learning · Layout Analysis · Character Recognition · Open Source library ·
Toolkit.

Introduction

Deep Learning(DL)-based approaches are the state-of-the-art for a wide range of document image analysis (DIA) tasks
including document image classiﬁcation [11,
```

See the [partitioning](https://docs.unstructured.io/open-source/core-functionality/partitioning)
section in our documentation for a full list of options and instructions on how to use
file-specific partitioning functions.

## :guardsman: Security Policy

See our [security policy](https://github.com/Unstructured-IO/unstructured/security/policy) for
information on how to report security vulnerabilities.

## :bug: Reporting Bugs

Encountered a bug? Please create a new [GitHub issue](https://github.com/Unstructured-IO/unstructured/issues/new/choose) and use our bug report template to describe the problem. To help us diagnose the issue, use the `python scripts/collect_env.py` command to gather your system's environment information and include it in your report. Your assistance helps us continuously improve our software - thank you!

## :books: Learn more

| Section | Description |
|-|-|
| [Company Website](https://unstructured.io) | Unstructured.io product and company info |
| [Documentation](https://docs.unstructured.io/) | Full API documentation |
| [Batch Processing](unstructured/ingest/README.md) | Ingesting batches of documents through Unstructured |

## :chart_with_upwards_trend: Analytics

We’ve partnered with Scarf (https://scarf.sh) to collect anonymized user statistics to understand which features our community is using and how to prioritize product decision-making in the future. To learn more about how we collect and use this data, please read our [Privacy Policy](https://unstructured.io/privacy-policy).
To opt out of this data collection, you can set the environment variable `SCARF_NO_ANALYTICS=true` before running any `unstructured` commands.


