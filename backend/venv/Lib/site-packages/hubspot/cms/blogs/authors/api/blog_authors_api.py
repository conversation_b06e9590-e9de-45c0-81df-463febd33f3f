# coding: utf-8

"""
    Blog Post endpoints

    Use these endpoints for interacting with Blog Posts, Blog Authors, and Blog Tags  # noqa: E501

    The version of the OpenAPI document: v3
    Generated by: https://openapi-generator.tech
"""


from __future__ import absolute_import

import re  # noqa: F401

# python 2 and python 3 compatibility library
import six

from hubspot.cms.blogs.authors.api_client import ApiClient
from hubspot.cms.blogs.authors.exceptions import ApiTypeError, ApiValueError  # noqa: F401


class BlogAuthorsApi(object):
    """NOTE: This class is auto generated by OpenAPI Generator
    Ref: https://openapi-generator.tech

    Do not edit the class manually.
    """

    def __init__(self, api_client=None):
        if api_client is None:
            api_client = ApiClient()
        self.api_client = api_client

    def archive(self, object_id, **kwargs):  # noqa: E501
        """Delete a Blog Author  # noqa: E501

        Delete the Blog Author object identified by the id in the path.  # noqa: E501
        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True

        >>> thread = api.archive(object_id, async_req=True)
        >>> result = thread.get()

        :param object_id: The Blog Author id. (required)
        :type object_id: str
        :param archived: Whether to return only results that have been archived.
        :type archived: bool
        :param async_req: Whether to execute the request asynchronously.
        :type async_req: bool, optional
        :param _preload_content: if False, the urllib3.HTTPResponse object will
                                 be returned without reading/decoding response
                                 data. Default is True.
        :type _preload_content: bool, optional
        :param _request_timeout: timeout setting for this request. If one
                                 number provided, it will be total request
                                 timeout. It can also be a pair (tuple) of
                                 (connection, read) timeouts.
        :return: Returns the result object.
                 If the method is called asynchronously,
                 returns the request thread.
        :rtype: None
        """
        kwargs["_return_http_data_only"] = True
        return self.archive_with_http_info(object_id, **kwargs)  # noqa: E501

    def archive_with_http_info(self, object_id, **kwargs):  # noqa: E501
        """Delete a Blog Author  # noqa: E501

        Delete the Blog Author object identified by the id in the path.  # noqa: E501
        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True

        >>> thread = api.archive_with_http_info(object_id, async_req=True)
        >>> result = thread.get()

        :param object_id: The Blog Author id. (required)
        :type object_id: str
        :param archived: Whether to return only results that have been archived.
        :type archived: bool
        :param async_req: Whether to execute the request asynchronously.
        :type async_req: bool, optional
        :param _return_http_data_only: response data without head status code
                                       and headers
        :type _return_http_data_only: bool, optional
        :param _preload_content: if False, the urllib3.HTTPResponse object will
                                 be returned without reading/decoding response
                                 data. Default is True.
        :type _preload_content: bool, optional
        :param _request_timeout: timeout setting for this request. If one
                                 number provided, it will be total request
                                 timeout. It can also be a pair (tuple) of
                                 (connection, read) timeouts.
        :param _request_auth: set to override the auth_settings for an a single
                              request; this effectively ignores the authentication
                              in the spec for a single request.
        :type _request_auth: dict, optional
        :type _content_type: string, optional: force content-type for the request
        :return: Returns the result object.
                 If the method is called asynchronously,
                 returns the request thread.
        :rtype: None
        """

        local_var_params = locals()

        all_params = ["object_id", "archived"]
        all_params.extend(["async_req", "_return_http_data_only", "_preload_content", "_request_timeout", "_request_auth", "_content_type", "_headers"])

        for key, val in six.iteritems(local_var_params["kwargs"]):
            if key not in all_params:
                raise ApiTypeError("Got an unexpected keyword argument '%s'" " to method archive" % key)
            local_var_params[key] = val
        del local_var_params["kwargs"]
        # verify the required parameter 'object_id' is set
        if self.api_client.client_side_validation and local_var_params.get("object_id") is None:  # noqa: E501
            raise ApiValueError("Missing the required parameter `object_id` when calling `archive`")  # noqa: E501

        collection_formats = {}

        path_params = {}
        if "object_id" in local_var_params:
            path_params["objectId"] = local_var_params["object_id"]  # noqa: E501

        query_params = []
        if local_var_params.get("archived") is not None:  # noqa: E501
            query_params.append(("archived", local_var_params["archived"]))  # noqa: E501

        header_params = dict(local_var_params.get("_headers", {}))

        form_params = []
        local_var_files = {}

        body_params = None
        # HTTP header `Accept`
        header_params["Accept"] = self.api_client.select_header_accept(["*/*"])  # noqa: E501

        # Authentication setting
        auth_settings = ["oauth2"]  # noqa: E501

        response_types_map = {}

        return self.api_client.call_api(
            "/cms/v3/blogs/authors/{objectId}",
            "DELETE",
            path_params,
            query_params,
            header_params,
            body=body_params,
            post_params=form_params,
            files=local_var_files,
            response_types_map=response_types_map,
            auth_settings=auth_settings,
            async_req=local_var_params.get("async_req"),
            _return_http_data_only=local_var_params.get("_return_http_data_only"),  # noqa: E501
            _preload_content=local_var_params.get("_preload_content", True),
            _request_timeout=local_var_params.get("_request_timeout"),
            collection_formats=collection_formats,
            _request_auth=local_var_params.get("_request_auth"),
        )

    def archive_batch(self, batch_input_string, **kwargs):  # noqa: E501
        """Delete a batch of Blog Authors  # noqa: E501

        Delete the Blog Author objects identified in the request body.  # noqa: E501
        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True

        >>> thread = api.archive_batch(batch_input_string, async_req=True)
        >>> result = thread.get()

        :param batch_input_string: The JSON array of Blog Author ids. (required)
        :type batch_input_string: BatchInputString
        :param async_req: Whether to execute the request asynchronously.
        :type async_req: bool, optional
        :param _preload_content: if False, the urllib3.HTTPResponse object will
                                 be returned without reading/decoding response
                                 data. Default is True.
        :type _preload_content: bool, optional
        :param _request_timeout: timeout setting for this request. If one
                                 number provided, it will be total request
                                 timeout. It can also be a pair (tuple) of
                                 (connection, read) timeouts.
        :return: Returns the result object.
                 If the method is called asynchronously,
                 returns the request thread.
        :rtype: None
        """
        kwargs["_return_http_data_only"] = True
        return self.archive_batch_with_http_info(batch_input_string, **kwargs)  # noqa: E501

    def archive_batch_with_http_info(self, batch_input_string, **kwargs):  # noqa: E501
        """Delete a batch of Blog Authors  # noqa: E501

        Delete the Blog Author objects identified in the request body.  # noqa: E501
        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True

        >>> thread = api.archive_batch_with_http_info(batch_input_string, async_req=True)
        >>> result = thread.get()

        :param batch_input_string: The JSON array of Blog Author ids. (required)
        :type batch_input_string: BatchInputString
        :param async_req: Whether to execute the request asynchronously.
        :type async_req: bool, optional
        :param _return_http_data_only: response data without head status code
                                       and headers
        :type _return_http_data_only: bool, optional
        :param _preload_content: if False, the urllib3.HTTPResponse object will
                                 be returned without reading/decoding response
                                 data. Default is True.
        :type _preload_content: bool, optional
        :param _request_timeout: timeout setting for this request. If one
                                 number provided, it will be total request
                                 timeout. It can also be a pair (tuple) of
                                 (connection, read) timeouts.
        :param _request_auth: set to override the auth_settings for an a single
                              request; this effectively ignores the authentication
                              in the spec for a single request.
        :type _request_auth: dict, optional
        :type _content_type: string, optional: force content-type for the request
        :return: Returns the result object.
                 If the method is called asynchronously,
                 returns the request thread.
        :rtype: None
        """

        local_var_params = locals()

        all_params = ["batch_input_string"]
        all_params.extend(["async_req", "_return_http_data_only", "_preload_content", "_request_timeout", "_request_auth", "_content_type", "_headers"])

        for key, val in six.iteritems(local_var_params["kwargs"]):
            if key not in all_params:
                raise ApiTypeError("Got an unexpected keyword argument '%s'" " to method archive_batch" % key)
            local_var_params[key] = val
        del local_var_params["kwargs"]
        # verify the required parameter 'batch_input_string' is set
        if self.api_client.client_side_validation and local_var_params.get("batch_input_string") is None:  # noqa: E501
            raise ApiValueError("Missing the required parameter `batch_input_string` when calling `archive_batch`")  # noqa: E501

        collection_formats = {}

        path_params = {}

        query_params = []

        header_params = dict(local_var_params.get("_headers", {}))

        form_params = []
        local_var_files = {}

        body_params = None
        if "batch_input_string" in local_var_params:
            body_params = local_var_params["batch_input_string"]
        # HTTP header `Accept`
        header_params["Accept"] = self.api_client.select_header_accept(["*/*"])  # noqa: E501

        # HTTP header `Content-Type`
        content_types_list = local_var_params.get("_content_type", self.api_client.select_header_content_type(["application/json"], "POST", body_params))  # noqa: E501
        if content_types_list:
            header_params["Content-Type"] = content_types_list

        # Authentication setting
        auth_settings = ["oauth2"]  # noqa: E501

        response_types_map = {}

        return self.api_client.call_api(
            "/cms/v3/blogs/authors/batch/archive",
            "POST",
            path_params,
            query_params,
            header_params,
            body=body_params,
            post_params=form_params,
            files=local_var_files,
            response_types_map=response_types_map,
            auth_settings=auth_settings,
            async_req=local_var_params.get("async_req"),
            _return_http_data_only=local_var_params.get("_return_http_data_only"),  # noqa: E501
            _preload_content=local_var_params.get("_preload_content", True),
            _request_timeout=local_var_params.get("_request_timeout"),
            collection_formats=collection_formats,
            _request_auth=local_var_params.get("_request_auth"),
        )

    def attach_to_lang_group(self, attach_to_lang_primary_request_v_next, **kwargs):  # noqa: E501
        """Attach a Blog Author to a multi-language group  # noqa: E501

        Attach a Blog Author to a multi-language group.  # noqa: E501
        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True

        >>> thread = api.attach_to_lang_group(attach_to_lang_primary_request_v_next, async_req=True)
        >>> result = thread.get()

        :param attach_to_lang_primary_request_v_next: The JSON representation of the AttachToLangPrimaryRequest object. (required)
        :type attach_to_lang_primary_request_v_next: AttachToLangPrimaryRequestVNext
        :param async_req: Whether to execute the request asynchronously.
        :type async_req: bool, optional
        :param _preload_content: if False, the urllib3.HTTPResponse object will
                                 be returned without reading/decoding response
                                 data. Default is True.
        :type _preload_content: bool, optional
        :param _request_timeout: timeout setting for this request. If one
                                 number provided, it will be total request
                                 timeout. It can also be a pair (tuple) of
                                 (connection, read) timeouts.
        :return: Returns the result object.
                 If the method is called asynchronously,
                 returns the request thread.
        :rtype: Error
        """
        kwargs["_return_http_data_only"] = True
        return self.attach_to_lang_group_with_http_info(attach_to_lang_primary_request_v_next, **kwargs)  # noqa: E501

    def attach_to_lang_group_with_http_info(self, attach_to_lang_primary_request_v_next, **kwargs):  # noqa: E501
        """Attach a Blog Author to a multi-language group  # noqa: E501

        Attach a Blog Author to a multi-language group.  # noqa: E501
        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True

        >>> thread = api.attach_to_lang_group_with_http_info(attach_to_lang_primary_request_v_next, async_req=True)
        >>> result = thread.get()

        :param attach_to_lang_primary_request_v_next: The JSON representation of the AttachToLangPrimaryRequest object. (required)
        :type attach_to_lang_primary_request_v_next: AttachToLangPrimaryRequestVNext
        :param async_req: Whether to execute the request asynchronously.
        :type async_req: bool, optional
        :param _return_http_data_only: response data without head status code
                                       and headers
        :type _return_http_data_only: bool, optional
        :param _preload_content: if False, the urllib3.HTTPResponse object will
                                 be returned without reading/decoding response
                                 data. Default is True.
        :type _preload_content: bool, optional
        :param _request_timeout: timeout setting for this request. If one
                                 number provided, it will be total request
                                 timeout. It can also be a pair (tuple) of
                                 (connection, read) timeouts.
        :param _request_auth: set to override the auth_settings for an a single
                              request; this effectively ignores the authentication
                              in the spec for a single request.
        :type _request_auth: dict, optional
        :type _content_type: string, optional: force content-type for the request
        :return: Returns the result object.
                 If the method is called asynchronously,
                 returns the request thread.
        :rtype: tuple(Error, status_code(int), headers(HTTPHeaderDict))
        """

        local_var_params = locals()

        all_params = ["attach_to_lang_primary_request_v_next"]
        all_params.extend(["async_req", "_return_http_data_only", "_preload_content", "_request_timeout", "_request_auth", "_content_type", "_headers"])

        for key, val in six.iteritems(local_var_params["kwargs"]):
            if key not in all_params:
                raise ApiTypeError("Got an unexpected keyword argument '%s'" " to method attach_to_lang_group" % key)
            local_var_params[key] = val
        del local_var_params["kwargs"]
        # verify the required parameter 'attach_to_lang_primary_request_v_next' is set
        if self.api_client.client_side_validation and local_var_params.get("attach_to_lang_primary_request_v_next") is None:  # noqa: E501
            raise ApiValueError("Missing the required parameter `attach_to_lang_primary_request_v_next` when calling `attach_to_lang_group`")  # noqa: E501

        collection_formats = {}

        path_params = {}

        query_params = []

        header_params = dict(local_var_params.get("_headers", {}))

        form_params = []
        local_var_files = {}

        body_params = None
        if "attach_to_lang_primary_request_v_next" in local_var_params:
            body_params = local_var_params["attach_to_lang_primary_request_v_next"]
        # HTTP header `Accept`
        header_params["Accept"] = self.api_client.select_header_accept(["*/*"])  # noqa: E501

        # HTTP header `Content-Type`
        content_types_list = local_var_params.get("_content_type", self.api_client.select_header_content_type(["application/json"], "POST", body_params))  # noqa: E501
        if content_types_list:
            header_params["Content-Type"] = content_types_list

        # Authentication setting
        auth_settings = ["oauth2"]  # noqa: E501

        response_types_map = {}

        return self.api_client.call_api(
            "/cms/v3/blogs/authors/multi-language/attach-to-lang-group",
            "POST",
            path_params,
            query_params,
            header_params,
            body=body_params,
            post_params=form_params,
            files=local_var_files,
            response_types_map=response_types_map,
            auth_settings=auth_settings,
            async_req=local_var_params.get("async_req"),
            _return_http_data_only=local_var_params.get("_return_http_data_only"),  # noqa: E501
            _preload_content=local_var_params.get("_preload_content", True),
            _request_timeout=local_var_params.get("_request_timeout"),
            collection_formats=collection_formats,
            _request_auth=local_var_params.get("_request_auth"),
        )

    def create(self, blog_author, **kwargs):  # noqa: E501
        """Create a new Blog Author  # noqa: E501

        Create a new Blog Author.  # noqa: E501
        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True

        >>> thread = api.create(blog_author, async_req=True)
        >>> result = thread.get()

        :param blog_author: The JSON representation of a new Blog Author. (required)
        :type blog_author: BlogAuthor
        :param async_req: Whether to execute the request asynchronously.
        :type async_req: bool, optional
        :param _preload_content: if False, the urllib3.HTTPResponse object will
                                 be returned without reading/decoding response
                                 data. Default is True.
        :type _preload_content: bool, optional
        :param _request_timeout: timeout setting for this request. If one
                                 number provided, it will be total request
                                 timeout. It can also be a pair (tuple) of
                                 (connection, read) timeouts.
        :return: Returns the result object.
                 If the method is called asynchronously,
                 returns the request thread.
        :rtype: BlogAuthor
        """
        kwargs["_return_http_data_only"] = True
        return self.create_with_http_info(blog_author, **kwargs)  # noqa: E501

    def create_with_http_info(self, blog_author, **kwargs):  # noqa: E501
        """Create a new Blog Author  # noqa: E501

        Create a new Blog Author.  # noqa: E501
        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True

        >>> thread = api.create_with_http_info(blog_author, async_req=True)
        >>> result = thread.get()

        :param blog_author: The JSON representation of a new Blog Author. (required)
        :type blog_author: BlogAuthor
        :param async_req: Whether to execute the request asynchronously.
        :type async_req: bool, optional
        :param _return_http_data_only: response data without head status code
                                       and headers
        :type _return_http_data_only: bool, optional
        :param _preload_content: if False, the urllib3.HTTPResponse object will
                                 be returned without reading/decoding response
                                 data. Default is True.
        :type _preload_content: bool, optional
        :param _request_timeout: timeout setting for this request. If one
                                 number provided, it will be total request
                                 timeout. It can also be a pair (tuple) of
                                 (connection, read) timeouts.
        :param _request_auth: set to override the auth_settings for an a single
                              request; this effectively ignores the authentication
                              in the spec for a single request.
        :type _request_auth: dict, optional
        :type _content_type: string, optional: force content-type for the request
        :return: Returns the result object.
                 If the method is called asynchronously,
                 returns the request thread.
        :rtype: tuple(BlogAuthor, status_code(int), headers(HTTPHeaderDict))
        """

        local_var_params = locals()

        all_params = ["blog_author"]
        all_params.extend(["async_req", "_return_http_data_only", "_preload_content", "_request_timeout", "_request_auth", "_content_type", "_headers"])

        for key, val in six.iteritems(local_var_params["kwargs"]):
            if key not in all_params:
                raise ApiTypeError("Got an unexpected keyword argument '%s'" " to method create" % key)
            local_var_params[key] = val
        del local_var_params["kwargs"]
        # verify the required parameter 'blog_author' is set
        if self.api_client.client_side_validation and local_var_params.get("blog_author") is None:  # noqa: E501
            raise ApiValueError("Missing the required parameter `blog_author` when calling `create`")  # noqa: E501

        collection_formats = {}

        path_params = {}

        query_params = []

        header_params = dict(local_var_params.get("_headers", {}))

        form_params = []
        local_var_files = {}

        body_params = None
        if "blog_author" in local_var_params:
            body_params = local_var_params["blog_author"]
        # HTTP header `Accept`
        header_params["Accept"] = self.api_client.select_header_accept(["application/json", "*/*"])  # noqa: E501

        # HTTP header `Content-Type`
        content_types_list = local_var_params.get("_content_type", self.api_client.select_header_content_type(["application/json"], "POST", body_params))  # noqa: E501
        if content_types_list:
            header_params["Content-Type"] = content_types_list

        # Authentication setting
        auth_settings = ["oauth2"]  # noqa: E501

        response_types_map = {
            201: "BlogAuthor",
        }

        return self.api_client.call_api(
            "/cms/v3/blogs/authors",
            "POST",
            path_params,
            query_params,
            header_params,
            body=body_params,
            post_params=form_params,
            files=local_var_files,
            response_types_map=response_types_map,
            auth_settings=auth_settings,
            async_req=local_var_params.get("async_req"),
            _return_http_data_only=local_var_params.get("_return_http_data_only"),  # noqa: E501
            _preload_content=local_var_params.get("_preload_content", True),
            _request_timeout=local_var_params.get("_request_timeout"),
            collection_formats=collection_formats,
            _request_auth=local_var_params.get("_request_auth"),
        )

    def create_batch(self, batch_input_blog_author, **kwargs):  # noqa: E501
        """Create a batch of Blog Authors  # noqa: E501

        Create the Blog Author objects detailed in the request body.  # noqa: E501
        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True

        >>> thread = api.create_batch(batch_input_blog_author, async_req=True)
        >>> result = thread.get()

        :param batch_input_blog_author: The JSON array of new Blog Authors to create. (required)
        :type batch_input_blog_author: BatchInputBlogAuthor
        :param async_req: Whether to execute the request asynchronously.
        :type async_req: bool, optional
        :param _preload_content: if False, the urllib3.HTTPResponse object will
                                 be returned without reading/decoding response
                                 data. Default is True.
        :type _preload_content: bool, optional
        :param _request_timeout: timeout setting for this request. If one
                                 number provided, it will be total request
                                 timeout. It can also be a pair (tuple) of
                                 (connection, read) timeouts.
        :return: Returns the result object.
                 If the method is called asynchronously,
                 returns the request thread.
        :rtype: BatchResponseBlogAuthor
        """
        kwargs["_return_http_data_only"] = True
        return self.create_batch_with_http_info(batch_input_blog_author, **kwargs)  # noqa: E501

    def create_batch_with_http_info(self, batch_input_blog_author, **kwargs):  # noqa: E501
        """Create a batch of Blog Authors  # noqa: E501

        Create the Blog Author objects detailed in the request body.  # noqa: E501
        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True

        >>> thread = api.create_batch_with_http_info(batch_input_blog_author, async_req=True)
        >>> result = thread.get()

        :param batch_input_blog_author: The JSON array of new Blog Authors to create. (required)
        :type batch_input_blog_author: BatchInputBlogAuthor
        :param async_req: Whether to execute the request asynchronously.
        :type async_req: bool, optional
        :param _return_http_data_only: response data without head status code
                                       and headers
        :type _return_http_data_only: bool, optional
        :param _preload_content: if False, the urllib3.HTTPResponse object will
                                 be returned without reading/decoding response
                                 data. Default is True.
        :type _preload_content: bool, optional
        :param _request_timeout: timeout setting for this request. If one
                                 number provided, it will be total request
                                 timeout. It can also be a pair (tuple) of
                                 (connection, read) timeouts.
        :param _request_auth: set to override the auth_settings for an a single
                              request; this effectively ignores the authentication
                              in the spec for a single request.
        :type _request_auth: dict, optional
        :type _content_type: string, optional: force content-type for the request
        :return: Returns the result object.
                 If the method is called asynchronously,
                 returns the request thread.
        :rtype: tuple(BatchResponseBlogAuthor, status_code(int), headers(HTTPHeaderDict))
        """

        local_var_params = locals()

        all_params = ["batch_input_blog_author"]
        all_params.extend(["async_req", "_return_http_data_only", "_preload_content", "_request_timeout", "_request_auth", "_content_type", "_headers"])

        for key, val in six.iteritems(local_var_params["kwargs"]):
            if key not in all_params:
                raise ApiTypeError("Got an unexpected keyword argument '%s'" " to method create_batch" % key)
            local_var_params[key] = val
        del local_var_params["kwargs"]
        # verify the required parameter 'batch_input_blog_author' is set
        if self.api_client.client_side_validation and local_var_params.get("batch_input_blog_author") is None:  # noqa: E501
            raise ApiValueError("Missing the required parameter `batch_input_blog_author` when calling `create_batch`")  # noqa: E501

        collection_formats = {}

        path_params = {}

        query_params = []

        header_params = dict(local_var_params.get("_headers", {}))

        form_params = []
        local_var_files = {}

        body_params = None
        if "batch_input_blog_author" in local_var_params:
            body_params = local_var_params["batch_input_blog_author"]
        # HTTP header `Accept`
        header_params["Accept"] = self.api_client.select_header_accept(["application/json", "*/*"])  # noqa: E501

        # HTTP header `Content-Type`
        content_types_list = local_var_params.get("_content_type", self.api_client.select_header_content_type(["application/json"], "POST", body_params))  # noqa: E501
        if content_types_list:
            header_params["Content-Type"] = content_types_list

        # Authentication setting
        auth_settings = ["oauth2"]  # noqa: E501

        response_types_map = {
            201: "BatchResponseBlogAuthor",
            207: "BatchResponseBlogAuthorWithErrors",
        }

        return self.api_client.call_api(
            "/cms/v3/blogs/authors/batch/create",
            "POST",
            path_params,
            query_params,
            header_params,
            body=body_params,
            post_params=form_params,
            files=local_var_files,
            response_types_map=response_types_map,
            auth_settings=auth_settings,
            async_req=local_var_params.get("async_req"),
            _return_http_data_only=local_var_params.get("_return_http_data_only"),  # noqa: E501
            _preload_content=local_var_params.get("_preload_content", True),
            _request_timeout=local_var_params.get("_request_timeout"),
            collection_formats=collection_formats,
            _request_auth=local_var_params.get("_request_auth"),
        )

    def create_lang_variation(self, blog_author_clone_request_v_next, **kwargs):  # noqa: E501
        """Create a new language variation  # noqa: E501

        Create a new language variation from an existing Blog Author.  # noqa: E501
        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True

        >>> thread = api.create_lang_variation(blog_author_clone_request_v_next, async_req=True)
        >>> result = thread.get()

        :param blog_author_clone_request_v_next: The JSON representation of the ContentLanguageCloneRequest object. (required)
        :type blog_author_clone_request_v_next: BlogAuthorCloneRequestVNext
        :param async_req: Whether to execute the request asynchronously.
        :type async_req: bool, optional
        :param _preload_content: if False, the urllib3.HTTPResponse object will
                                 be returned without reading/decoding response
                                 data. Default is True.
        :type _preload_content: bool, optional
        :param _request_timeout: timeout setting for this request. If one
                                 number provided, it will be total request
                                 timeout. It can also be a pair (tuple) of
                                 (connection, read) timeouts.
        :return: Returns the result object.
                 If the method is called asynchronously,
                 returns the request thread.
        :rtype: BlogAuthor
        """
        kwargs["_return_http_data_only"] = True
        return self.create_lang_variation_with_http_info(blog_author_clone_request_v_next, **kwargs)  # noqa: E501

    def create_lang_variation_with_http_info(self, blog_author_clone_request_v_next, **kwargs):  # noqa: E501
        """Create a new language variation  # noqa: E501

        Create a new language variation from an existing Blog Author.  # noqa: E501
        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True

        >>> thread = api.create_lang_variation_with_http_info(blog_author_clone_request_v_next, async_req=True)
        >>> result = thread.get()

        :param blog_author_clone_request_v_next: The JSON representation of the ContentLanguageCloneRequest object. (required)
        :type blog_author_clone_request_v_next: BlogAuthorCloneRequestVNext
        :param async_req: Whether to execute the request asynchronously.
        :type async_req: bool, optional
        :param _return_http_data_only: response data without head status code
                                       and headers
        :type _return_http_data_only: bool, optional
        :param _preload_content: if False, the urllib3.HTTPResponse object will
                                 be returned without reading/decoding response
                                 data. Default is True.
        :type _preload_content: bool, optional
        :param _request_timeout: timeout setting for this request. If one
                                 number provided, it will be total request
                                 timeout. It can also be a pair (tuple) of
                                 (connection, read) timeouts.
        :param _request_auth: set to override the auth_settings for an a single
                              request; this effectively ignores the authentication
                              in the spec for a single request.
        :type _request_auth: dict, optional
        :type _content_type: string, optional: force content-type for the request
        :return: Returns the result object.
                 If the method is called asynchronously,
                 returns the request thread.
        :rtype: tuple(BlogAuthor, status_code(int), headers(HTTPHeaderDict))
        """

        local_var_params = locals()

        all_params = ["blog_author_clone_request_v_next"]
        all_params.extend(["async_req", "_return_http_data_only", "_preload_content", "_request_timeout", "_request_auth", "_content_type", "_headers"])

        for key, val in six.iteritems(local_var_params["kwargs"]):
            if key not in all_params:
                raise ApiTypeError("Got an unexpected keyword argument '%s'" " to method create_lang_variation" % key)
            local_var_params[key] = val
        del local_var_params["kwargs"]
        # verify the required parameter 'blog_author_clone_request_v_next' is set
        if self.api_client.client_side_validation and local_var_params.get("blog_author_clone_request_v_next") is None:  # noqa: E501
            raise ApiValueError("Missing the required parameter `blog_author_clone_request_v_next` when calling `create_lang_variation`")  # noqa: E501

        collection_formats = {}

        path_params = {}

        query_params = []

        header_params = dict(local_var_params.get("_headers", {}))

        form_params = []
        local_var_files = {}

        body_params = None
        if "blog_author_clone_request_v_next" in local_var_params:
            body_params = local_var_params["blog_author_clone_request_v_next"]
        # HTTP header `Accept`
        header_params["Accept"] = self.api_client.select_header_accept(["application/json", "*/*"])  # noqa: E501

        # HTTP header `Content-Type`
        content_types_list = local_var_params.get("_content_type", self.api_client.select_header_content_type(["application/json"], "POST", body_params))  # noqa: E501
        if content_types_list:
            header_params["Content-Type"] = content_types_list

        # Authentication setting
        auth_settings = ["oauth2"]  # noqa: E501

        response_types_map = {
            200: "BlogAuthor",
        }

        return self.api_client.call_api(
            "/cms/v3/blogs/authors/multi-language/create-language-variation",
            "POST",
            path_params,
            query_params,
            header_params,
            body=body_params,
            post_params=form_params,
            files=local_var_files,
            response_types_map=response_types_map,
            auth_settings=auth_settings,
            async_req=local_var_params.get("async_req"),
            _return_http_data_only=local_var_params.get("_return_http_data_only"),  # noqa: E501
            _preload_content=local_var_params.get("_preload_content", True),
            _request_timeout=local_var_params.get("_request_timeout"),
            collection_formats=collection_formats,
            _request_auth=local_var_params.get("_request_auth"),
        )

    def detach_from_lang_group(self, detach_from_lang_group_request_v_next, **kwargs):  # noqa: E501
        """Detach a Blog Author from a multi-language group  # noqa: E501

        Detach a Blog Author from a multi-language group.  # noqa: E501
        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True

        >>> thread = api.detach_from_lang_group(detach_from_lang_group_request_v_next, async_req=True)
        >>> result = thread.get()

        :param detach_from_lang_group_request_v_next: The JSON representation of the DetachFromLangGroupRequest object. (required)
        :type detach_from_lang_group_request_v_next: DetachFromLangGroupRequestVNext
        :param async_req: Whether to execute the request asynchronously.
        :type async_req: bool, optional
        :param _preload_content: if False, the urllib3.HTTPResponse object will
                                 be returned without reading/decoding response
                                 data. Default is True.
        :type _preload_content: bool, optional
        :param _request_timeout: timeout setting for this request. If one
                                 number provided, it will be total request
                                 timeout. It can also be a pair (tuple) of
                                 (connection, read) timeouts.
        :return: Returns the result object.
                 If the method is called asynchronously,
                 returns the request thread.
        :rtype: Error
        """
        kwargs["_return_http_data_only"] = True
        return self.detach_from_lang_group_with_http_info(detach_from_lang_group_request_v_next, **kwargs)  # noqa: E501

    def detach_from_lang_group_with_http_info(self, detach_from_lang_group_request_v_next, **kwargs):  # noqa: E501
        """Detach a Blog Author from a multi-language group  # noqa: E501

        Detach a Blog Author from a multi-language group.  # noqa: E501
        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True

        >>> thread = api.detach_from_lang_group_with_http_info(detach_from_lang_group_request_v_next, async_req=True)
        >>> result = thread.get()

        :param detach_from_lang_group_request_v_next: The JSON representation of the DetachFromLangGroupRequest object. (required)
        :type detach_from_lang_group_request_v_next: DetachFromLangGroupRequestVNext
        :param async_req: Whether to execute the request asynchronously.
        :type async_req: bool, optional
        :param _return_http_data_only: response data without head status code
                                       and headers
        :type _return_http_data_only: bool, optional
        :param _preload_content: if False, the urllib3.HTTPResponse object will
                                 be returned without reading/decoding response
                                 data. Default is True.
        :type _preload_content: bool, optional
        :param _request_timeout: timeout setting for this request. If one
                                 number provided, it will be total request
                                 timeout. It can also be a pair (tuple) of
                                 (connection, read) timeouts.
        :param _request_auth: set to override the auth_settings for an a single
                              request; this effectively ignores the authentication
                              in the spec for a single request.
        :type _request_auth: dict, optional
        :type _content_type: string, optional: force content-type for the request
        :return: Returns the result object.
                 If the method is called asynchronously,
                 returns the request thread.
        :rtype: tuple(Error, status_code(int), headers(HTTPHeaderDict))
        """

        local_var_params = locals()

        all_params = ["detach_from_lang_group_request_v_next"]
        all_params.extend(["async_req", "_return_http_data_only", "_preload_content", "_request_timeout", "_request_auth", "_content_type", "_headers"])

        for key, val in six.iteritems(local_var_params["kwargs"]):
            if key not in all_params:
                raise ApiTypeError("Got an unexpected keyword argument '%s'" " to method detach_from_lang_group" % key)
            local_var_params[key] = val
        del local_var_params["kwargs"]
        # verify the required parameter 'detach_from_lang_group_request_v_next' is set
        if self.api_client.client_side_validation and local_var_params.get("detach_from_lang_group_request_v_next") is None:  # noqa: E501
            raise ApiValueError("Missing the required parameter `detach_from_lang_group_request_v_next` when calling `detach_from_lang_group`")  # noqa: E501

        collection_formats = {}

        path_params = {}

        query_params = []

        header_params = dict(local_var_params.get("_headers", {}))

        form_params = []
        local_var_files = {}

        body_params = None
        if "detach_from_lang_group_request_v_next" in local_var_params:
            body_params = local_var_params["detach_from_lang_group_request_v_next"]
        # HTTP header `Accept`
        header_params["Accept"] = self.api_client.select_header_accept(["*/*"])  # noqa: E501

        # HTTP header `Content-Type`
        content_types_list = local_var_params.get("_content_type", self.api_client.select_header_content_type(["application/json"], "POST", body_params))  # noqa: E501
        if content_types_list:
            header_params["Content-Type"] = content_types_list

        # Authentication setting
        auth_settings = ["oauth2"]  # noqa: E501

        response_types_map = {}

        return self.api_client.call_api(
            "/cms/v3/blogs/authors/multi-language/detach-from-lang-group",
            "POST",
            path_params,
            query_params,
            header_params,
            body=body_params,
            post_params=form_params,
            files=local_var_files,
            response_types_map=response_types_map,
            auth_settings=auth_settings,
            async_req=local_var_params.get("async_req"),
            _return_http_data_only=local_var_params.get("_return_http_data_only"),  # noqa: E501
            _preload_content=local_var_params.get("_preload_content", True),
            _request_timeout=local_var_params.get("_request_timeout"),
            collection_formats=collection_formats,
            _request_auth=local_var_params.get("_request_auth"),
        )

    def get_by_id(self, object_id, **kwargs):  # noqa: E501
        """Retrieve a Blog Author  # noqa: E501

        Retrieve the Blog Author object identified by the id in the path.  # noqa: E501
        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True

        >>> thread = api.get_by_id(object_id, async_req=True)
        >>> result = thread.get()

        :param object_id: The Blog Author id. (required)
        :type object_id: str
        :param archived: Specifies whether to return deleted Blog Authors. Defaults to `false`.
        :type archived: bool
        :param async_req: Whether to execute the request asynchronously.
        :type async_req: bool, optional
        :param _preload_content: if False, the urllib3.HTTPResponse object will
                                 be returned without reading/decoding response
                                 data. Default is True.
        :type _preload_content: bool, optional
        :param _request_timeout: timeout setting for this request. If one
                                 number provided, it will be total request
                                 timeout. It can also be a pair (tuple) of
                                 (connection, read) timeouts.
        :return: Returns the result object.
                 If the method is called asynchronously,
                 returns the request thread.
        :rtype: BlogAuthor
        """
        kwargs["_return_http_data_only"] = True
        return self.get_by_id_with_http_info(object_id, **kwargs)  # noqa: E501

    def get_by_id_with_http_info(self, object_id, **kwargs):  # noqa: E501
        """Retrieve a Blog Author  # noqa: E501

        Retrieve the Blog Author object identified by the id in the path.  # noqa: E501
        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True

        >>> thread = api.get_by_id_with_http_info(object_id, async_req=True)
        >>> result = thread.get()

        :param object_id: The Blog Author id. (required)
        :type object_id: str
        :param archived: Specifies whether to return deleted Blog Authors. Defaults to `false`.
        :type archived: bool
        :param async_req: Whether to execute the request asynchronously.
        :type async_req: bool, optional
        :param _return_http_data_only: response data without head status code
                                       and headers
        :type _return_http_data_only: bool, optional
        :param _preload_content: if False, the urllib3.HTTPResponse object will
                                 be returned without reading/decoding response
                                 data. Default is True.
        :type _preload_content: bool, optional
        :param _request_timeout: timeout setting for this request. If one
                                 number provided, it will be total request
                                 timeout. It can also be a pair (tuple) of
                                 (connection, read) timeouts.
        :param _request_auth: set to override the auth_settings for an a single
                              request; this effectively ignores the authentication
                              in the spec for a single request.
        :type _request_auth: dict, optional
        :type _content_type: string, optional: force content-type for the request
        :return: Returns the result object.
                 If the method is called asynchronously,
                 returns the request thread.
        :rtype: tuple(BlogAuthor, status_code(int), headers(HTTPHeaderDict))
        """

        local_var_params = locals()

        all_params = ["object_id", "archived"]
        all_params.extend(["async_req", "_return_http_data_only", "_preload_content", "_request_timeout", "_request_auth", "_content_type", "_headers"])

        for key, val in six.iteritems(local_var_params["kwargs"]):
            if key not in all_params:
                raise ApiTypeError("Got an unexpected keyword argument '%s'" " to method get_by_id" % key)
            local_var_params[key] = val
        del local_var_params["kwargs"]
        # verify the required parameter 'object_id' is set
        if self.api_client.client_side_validation and local_var_params.get("object_id") is None:  # noqa: E501
            raise ApiValueError("Missing the required parameter `object_id` when calling `get_by_id`")  # noqa: E501

        collection_formats = {}

        path_params = {}
        if "object_id" in local_var_params:
            path_params["objectId"] = local_var_params["object_id"]  # noqa: E501

        query_params = []
        if local_var_params.get("archived") is not None:  # noqa: E501
            query_params.append(("archived", local_var_params["archived"]))  # noqa: E501

        header_params = dict(local_var_params.get("_headers", {}))

        form_params = []
        local_var_files = {}

        body_params = None
        # HTTP header `Accept`
        header_params["Accept"] = self.api_client.select_header_accept(["application/json", "*/*"])  # noqa: E501

        # Authentication setting
        auth_settings = ["oauth2"]  # noqa: E501

        response_types_map = {
            200: "BlogAuthor",
        }

        return self.api_client.call_api(
            "/cms/v3/blogs/authors/{objectId}",
            "GET",
            path_params,
            query_params,
            header_params,
            body=body_params,
            post_params=form_params,
            files=local_var_files,
            response_types_map=response_types_map,
            auth_settings=auth_settings,
            async_req=local_var_params.get("async_req"),
            _return_http_data_only=local_var_params.get("_return_http_data_only"),  # noqa: E501
            _preload_content=local_var_params.get("_preload_content", True),
            _request_timeout=local_var_params.get("_request_timeout"),
            collection_formats=collection_formats,
            _request_auth=local_var_params.get("_request_auth"),
        )

    def get_page(self, **kwargs):  # noqa: E501
        """Get all Blog Authors  # noqa: E501

        Get the list of blog authors. Supports paging and filtering. This method would be useful for an integration that examined these models and used an external service to suggest edits.   # noqa: E501
        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True

        >>> thread = api.get_page(async_req=True)
        >>> result = thread.get()

        :param created_at: Only return Blog Authors created at exactly the specified time.
        :type created_at: datetime
        :param created_after: Only return Blog Authors created after the specified time.
        :type created_after: datetime
        :param created_before: Only return Blog Authors created before the specified time.
        :type created_before: datetime
        :param updated_at: Only return Blog Authors last updated at exactly the specified time.
        :type updated_at: datetime
        :param updated_after: Only return Blog Authors last updated after the specified time.
        :type updated_after: datetime
        :param updated_before: Only return Blog Authors last updated before the specified time.
        :type updated_before: datetime
        :param sort: Specifies which fields to use for sorting results. Valid fields are `name`, `createdAt`, `updatedAt`, `createdBy`, `updatedBy`. `createdAt` will be used by default.
        :type sort: list[str]
        :param after: The cursor token value to get the next set of results. You can get this from the `paging.next.after` JSON property of a paged response containing more results.
        :type after: str
        :param limit: The maximum number of results to return. Default is 100.
        :type limit: int
        :param archived: Specifies whether to return deleted Blog Authors. Defaults to `false`.
        :type archived: bool
        :param async_req: Whether to execute the request asynchronously.
        :type async_req: bool, optional
        :param _preload_content: if False, the urllib3.HTTPResponse object will
                                 be returned without reading/decoding response
                                 data. Default is True.
        :type _preload_content: bool, optional
        :param _request_timeout: timeout setting for this request. If one
                                 number provided, it will be total request
                                 timeout. It can also be a pair (tuple) of
                                 (connection, read) timeouts.
        :return: Returns the result object.
                 If the method is called asynchronously,
                 returns the request thread.
        :rtype: CollectionResponseWithTotalBlogAuthorForwardPaging
        """
        kwargs["_return_http_data_only"] = True
        return self.get_page_with_http_info(**kwargs)  # noqa: E501

    def get_page_with_http_info(self, **kwargs):  # noqa: E501
        """Get all Blog Authors  # noqa: E501

        Get the list of blog authors. Supports paging and filtering. This method would be useful for an integration that examined these models and used an external service to suggest edits.   # noqa: E501
        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True

        >>> thread = api.get_page_with_http_info(async_req=True)
        >>> result = thread.get()

        :param created_at: Only return Blog Authors created at exactly the specified time.
        :type created_at: datetime
        :param created_after: Only return Blog Authors created after the specified time.
        :type created_after: datetime
        :param created_before: Only return Blog Authors created before the specified time.
        :type created_before: datetime
        :param updated_at: Only return Blog Authors last updated at exactly the specified time.
        :type updated_at: datetime
        :param updated_after: Only return Blog Authors last updated after the specified time.
        :type updated_after: datetime
        :param updated_before: Only return Blog Authors last updated before the specified time.
        :type updated_before: datetime
        :param sort: Specifies which fields to use for sorting results. Valid fields are `name`, `createdAt`, `updatedAt`, `createdBy`, `updatedBy`. `createdAt` will be used by default.
        :type sort: list[str]
        :param after: The cursor token value to get the next set of results. You can get this from the `paging.next.after` JSON property of a paged response containing more results.
        :type after: str
        :param limit: The maximum number of results to return. Default is 100.
        :type limit: int
        :param archived: Specifies whether to return deleted Blog Authors. Defaults to `false`.
        :type archived: bool
        :param async_req: Whether to execute the request asynchronously.
        :type async_req: bool, optional
        :param _return_http_data_only: response data without head status code
                                       and headers
        :type _return_http_data_only: bool, optional
        :param _preload_content: if False, the urllib3.HTTPResponse object will
                                 be returned without reading/decoding response
                                 data. Default is True.
        :type _preload_content: bool, optional
        :param _request_timeout: timeout setting for this request. If one
                                 number provided, it will be total request
                                 timeout. It can also be a pair (tuple) of
                                 (connection, read) timeouts.
        :param _request_auth: set to override the auth_settings for an a single
                              request; this effectively ignores the authentication
                              in the spec for a single request.
        :type _request_auth: dict, optional
        :type _content_type: string, optional: force content-type for the request
        :return: Returns the result object.
                 If the method is called asynchronously,
                 returns the request thread.
        :rtype: tuple(CollectionResponseWithTotalBlogAuthorForwardPaging, status_code(int), headers(HTTPHeaderDict))
        """

        local_var_params = locals()

        all_params = ["created_at", "created_after", "created_before", "updated_at", "updated_after", "updated_before", "sort", "after", "limit", "archived"]
        all_params.extend(["async_req", "_return_http_data_only", "_preload_content", "_request_timeout", "_request_auth", "_content_type", "_headers"])

        for key, val in six.iteritems(local_var_params["kwargs"]):
            if key not in all_params:
                raise ApiTypeError("Got an unexpected keyword argument '%s'" " to method get_page" % key)
            local_var_params[key] = val
        del local_var_params["kwargs"]

        collection_formats = {}

        path_params = {}

        query_params = []
        if local_var_params.get("created_at") is not None:  # noqa: E501
            query_params.append(("createdAt", local_var_params["created_at"]))  # noqa: E501
        if local_var_params.get("created_after") is not None:  # noqa: E501
            query_params.append(("createdAfter", local_var_params["created_after"]))  # noqa: E501
        if local_var_params.get("created_before") is not None:  # noqa: E501
            query_params.append(("createdBefore", local_var_params["created_before"]))  # noqa: E501
        if local_var_params.get("updated_at") is not None:  # noqa: E501
            query_params.append(("updatedAt", local_var_params["updated_at"]))  # noqa: E501
        if local_var_params.get("updated_after") is not None:  # noqa: E501
            query_params.append(("updatedAfter", local_var_params["updated_after"]))  # noqa: E501
        if local_var_params.get("updated_before") is not None:  # noqa: E501
            query_params.append(("updatedBefore", local_var_params["updated_before"]))  # noqa: E501
        if local_var_params.get("sort") is not None:  # noqa: E501
            query_params.append(("sort", local_var_params["sort"]))  # noqa: E501
            collection_formats["sort"] = "multi"  # noqa: E501
        if local_var_params.get("after") is not None:  # noqa: E501
            query_params.append(("after", local_var_params["after"]))  # noqa: E501
        if local_var_params.get("limit") is not None:  # noqa: E501
            query_params.append(("limit", local_var_params["limit"]))  # noqa: E501
        if local_var_params.get("archived") is not None:  # noqa: E501
            query_params.append(("archived", local_var_params["archived"]))  # noqa: E501

        header_params = dict(local_var_params.get("_headers", {}))

        form_params = []
        local_var_files = {}

        body_params = None
        # HTTP header `Accept`
        header_params["Accept"] = self.api_client.select_header_accept(["application/json", "*/*"])  # noqa: E501

        # Authentication setting
        auth_settings = ["oauth2"]  # noqa: E501

        response_types_map = {
            200: "CollectionResponseWithTotalBlogAuthorForwardPaging",
        }

        return self.api_client.call_api(
            "/cms/v3/blogs/authors",
            "GET",
            path_params,
            query_params,
            header_params,
            body=body_params,
            post_params=form_params,
            files=local_var_files,
            response_types_map=response_types_map,
            auth_settings=auth_settings,
            async_req=local_var_params.get("async_req"),
            _return_http_data_only=local_var_params.get("_return_http_data_only"),  # noqa: E501
            _preload_content=local_var_params.get("_preload_content", True),
            _request_timeout=local_var_params.get("_request_timeout"),
            collection_formats=collection_formats,
            _request_auth=local_var_params.get("_request_auth"),
        )

    def read_batch(self, batch_input_string, **kwargs):  # noqa: E501
        """Retrieve a batch of Blog Authors  # noqa: E501

        Retrieve the Blog Author objects identified in the request body.  # noqa: E501
        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True

        >>> thread = api.read_batch(batch_input_string, async_req=True)
        >>> result = thread.get()

        :param batch_input_string: The JSON array of Blog Author ids. (required)
        :type batch_input_string: BatchInputString
        :param archived: Specifies whether to return deleted Blog Authors. Defaults to `false`.
        :type archived: bool
        :param async_req: Whether to execute the request asynchronously.
        :type async_req: bool, optional
        :param _preload_content: if False, the urllib3.HTTPResponse object will
                                 be returned without reading/decoding response
                                 data. Default is True.
        :type _preload_content: bool, optional
        :param _request_timeout: timeout setting for this request. If one
                                 number provided, it will be total request
                                 timeout. It can also be a pair (tuple) of
                                 (connection, read) timeouts.
        :return: Returns the result object.
                 If the method is called asynchronously,
                 returns the request thread.
        :rtype: BatchResponseBlogAuthor
        """
        kwargs["_return_http_data_only"] = True
        return self.read_batch_with_http_info(batch_input_string, **kwargs)  # noqa: E501

    def read_batch_with_http_info(self, batch_input_string, **kwargs):  # noqa: E501
        """Retrieve a batch of Blog Authors  # noqa: E501

        Retrieve the Blog Author objects identified in the request body.  # noqa: E501
        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True

        >>> thread = api.read_batch_with_http_info(batch_input_string, async_req=True)
        >>> result = thread.get()

        :param batch_input_string: The JSON array of Blog Author ids. (required)
        :type batch_input_string: BatchInputString
        :param archived: Specifies whether to return deleted Blog Authors. Defaults to `false`.
        :type archived: bool
        :param async_req: Whether to execute the request asynchronously.
        :type async_req: bool, optional
        :param _return_http_data_only: response data without head status code
                                       and headers
        :type _return_http_data_only: bool, optional
        :param _preload_content: if False, the urllib3.HTTPResponse object will
                                 be returned without reading/decoding response
                                 data. Default is True.
        :type _preload_content: bool, optional
        :param _request_timeout: timeout setting for this request. If one
                                 number provided, it will be total request
                                 timeout. It can also be a pair (tuple) of
                                 (connection, read) timeouts.
        :param _request_auth: set to override the auth_settings for an a single
                              request; this effectively ignores the authentication
                              in the spec for a single request.
        :type _request_auth: dict, optional
        :type _content_type: string, optional: force content-type for the request
        :return: Returns the result object.
                 If the method is called asynchronously,
                 returns the request thread.
        :rtype: tuple(BatchResponseBlogAuthor, status_code(int), headers(HTTPHeaderDict))
        """

        local_var_params = locals()

        all_params = ["batch_input_string", "archived"]
        all_params.extend(["async_req", "_return_http_data_only", "_preload_content", "_request_timeout", "_request_auth", "_content_type", "_headers"])

        for key, val in six.iteritems(local_var_params["kwargs"]):
            if key not in all_params:
                raise ApiTypeError("Got an unexpected keyword argument '%s'" " to method read_batch" % key)
            local_var_params[key] = val
        del local_var_params["kwargs"]
        # verify the required parameter 'batch_input_string' is set
        if self.api_client.client_side_validation and local_var_params.get("batch_input_string") is None:  # noqa: E501
            raise ApiValueError("Missing the required parameter `batch_input_string` when calling `read_batch`")  # noqa: E501

        collection_formats = {}

        path_params = {}

        query_params = []
        if local_var_params.get("archived") is not None:  # noqa: E501
            query_params.append(("archived", local_var_params["archived"]))  # noqa: E501

        header_params = dict(local_var_params.get("_headers", {}))

        form_params = []
        local_var_files = {}

        body_params = None
        if "batch_input_string" in local_var_params:
            body_params = local_var_params["batch_input_string"]
        # HTTP header `Accept`
        header_params["Accept"] = self.api_client.select_header_accept(["application/json", "*/*"])  # noqa: E501

        # HTTP header `Content-Type`
        content_types_list = local_var_params.get("_content_type", self.api_client.select_header_content_type(["application/json"], "POST", body_params))  # noqa: E501
        if content_types_list:
            header_params["Content-Type"] = content_types_list

        # Authentication setting
        auth_settings = ["oauth2"]  # noqa: E501

        response_types_map = {
            200: "BatchResponseBlogAuthor",
            207: "BatchResponseBlogAuthorWithErrors",
        }

        return self.api_client.call_api(
            "/cms/v3/blogs/authors/batch/read",
            "POST",
            path_params,
            query_params,
            header_params,
            body=body_params,
            post_params=form_params,
            files=local_var_files,
            response_types_map=response_types_map,
            auth_settings=auth_settings,
            async_req=local_var_params.get("async_req"),
            _return_http_data_only=local_var_params.get("_return_http_data_only"),  # noqa: E501
            _preload_content=local_var_params.get("_preload_content", True),
            _request_timeout=local_var_params.get("_request_timeout"),
            collection_formats=collection_formats,
            _request_auth=local_var_params.get("_request_auth"),
        )

    def set_lang_primary(self, set_new_language_primary_request_v_next, **kwargs):  # noqa: E501
        """Set a new primary language  # noqa: E501

        Set a Blog Author as the primary language of a multi-language group.  # noqa: E501
        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True

        >>> thread = api.set_lang_primary(set_new_language_primary_request_v_next, async_req=True)
        >>> result = thread.get()

        :param set_new_language_primary_request_v_next: The JSON representation of the SetNewLanguagePrimaryRequest object. (required)
        :type set_new_language_primary_request_v_next: SetNewLanguagePrimaryRequestVNext
        :param async_req: Whether to execute the request asynchronously.
        :type async_req: bool, optional
        :param _preload_content: if False, the urllib3.HTTPResponse object will
                                 be returned without reading/decoding response
                                 data. Default is True.
        :type _preload_content: bool, optional
        :param _request_timeout: timeout setting for this request. If one
                                 number provided, it will be total request
                                 timeout. It can also be a pair (tuple) of
                                 (connection, read) timeouts.
        :return: Returns the result object.
                 If the method is called asynchronously,
                 returns the request thread.
        :rtype: None
        """
        kwargs["_return_http_data_only"] = True
        return self.set_lang_primary_with_http_info(set_new_language_primary_request_v_next, **kwargs)  # noqa: E501

    def set_lang_primary_with_http_info(self, set_new_language_primary_request_v_next, **kwargs):  # noqa: E501
        """Set a new primary language  # noqa: E501

        Set a Blog Author as the primary language of a multi-language group.  # noqa: E501
        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True

        >>> thread = api.set_lang_primary_with_http_info(set_new_language_primary_request_v_next, async_req=True)
        >>> result = thread.get()

        :param set_new_language_primary_request_v_next: The JSON representation of the SetNewLanguagePrimaryRequest object. (required)
        :type set_new_language_primary_request_v_next: SetNewLanguagePrimaryRequestVNext
        :param async_req: Whether to execute the request asynchronously.
        :type async_req: bool, optional
        :param _return_http_data_only: response data without head status code
                                       and headers
        :type _return_http_data_only: bool, optional
        :param _preload_content: if False, the urllib3.HTTPResponse object will
                                 be returned without reading/decoding response
                                 data. Default is True.
        :type _preload_content: bool, optional
        :param _request_timeout: timeout setting for this request. If one
                                 number provided, it will be total request
                                 timeout. It can also be a pair (tuple) of
                                 (connection, read) timeouts.
        :param _request_auth: set to override the auth_settings for an a single
                              request; this effectively ignores the authentication
                              in the spec for a single request.
        :type _request_auth: dict, optional
        :type _content_type: string, optional: force content-type for the request
        :return: Returns the result object.
                 If the method is called asynchronously,
                 returns the request thread.
        :rtype: None
        """

        local_var_params = locals()

        all_params = ["set_new_language_primary_request_v_next"]
        all_params.extend(["async_req", "_return_http_data_only", "_preload_content", "_request_timeout", "_request_auth", "_content_type", "_headers"])

        for key, val in six.iteritems(local_var_params["kwargs"]):
            if key not in all_params:
                raise ApiTypeError("Got an unexpected keyword argument '%s'" " to method set_lang_primary" % key)
            local_var_params[key] = val
        del local_var_params["kwargs"]
        # verify the required parameter 'set_new_language_primary_request_v_next' is set
        if self.api_client.client_side_validation and local_var_params.get("set_new_language_primary_request_v_next") is None:  # noqa: E501
            raise ApiValueError("Missing the required parameter `set_new_language_primary_request_v_next` when calling `set_lang_primary`")  # noqa: E501

        collection_formats = {}

        path_params = {}

        query_params = []

        header_params = dict(local_var_params.get("_headers", {}))

        form_params = []
        local_var_files = {}

        body_params = None
        if "set_new_language_primary_request_v_next" in local_var_params:
            body_params = local_var_params["set_new_language_primary_request_v_next"]
        # HTTP header `Accept`
        header_params["Accept"] = self.api_client.select_header_accept(["*/*"])  # noqa: E501

        # HTTP header `Content-Type`
        content_types_list = local_var_params.get("_content_type", self.api_client.select_header_content_type(["application/json"], "PUT", body_params))  # noqa: E501
        if content_types_list:
            header_params["Content-Type"] = content_types_list

        # Authentication setting
        auth_settings = ["oauth2"]  # noqa: E501

        response_types_map = {}

        return self.api_client.call_api(
            "/cms/v3/blogs/authors/multi-language/set-new-lang-primary",
            "PUT",
            path_params,
            query_params,
            header_params,
            body=body_params,
            post_params=form_params,
            files=local_var_files,
            response_types_map=response_types_map,
            auth_settings=auth_settings,
            async_req=local_var_params.get("async_req"),
            _return_http_data_only=local_var_params.get("_return_http_data_only"),  # noqa: E501
            _preload_content=local_var_params.get("_preload_content", True),
            _request_timeout=local_var_params.get("_request_timeout"),
            collection_formats=collection_formats,
            _request_auth=local_var_params.get("_request_auth"),
        )

    def update(self, object_id, blog_author, **kwargs):  # noqa: E501
        """Update a Blog Author  # noqa: E501

        Sparse updates a single Blog Author object identified by the id in the path. All the column values need not be specified. Only the that need to be modified can be specified.   # noqa: E501
        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True

        >>> thread = api.update(object_id, blog_author, async_req=True)
        >>> result = thread.get()

        :param object_id: The Blog Author id. (required)
        :type object_id: str
        :param blog_author: The JSON representation of the updated Blog Author. (required)
        :type blog_author: BlogAuthor
        :param archived: Specifies whether to update deleted Blog Authors. Defaults to `false`.
        :type archived: bool
        :param async_req: Whether to execute the request asynchronously.
        :type async_req: bool, optional
        :param _preload_content: if False, the urllib3.HTTPResponse object will
                                 be returned without reading/decoding response
                                 data. Default is True.
        :type _preload_content: bool, optional
        :param _request_timeout: timeout setting for this request. If one
                                 number provided, it will be total request
                                 timeout. It can also be a pair (tuple) of
                                 (connection, read) timeouts.
        :return: Returns the result object.
                 If the method is called asynchronously,
                 returns the request thread.
        :rtype: BlogAuthor
        """
        kwargs["_return_http_data_only"] = True
        return self.update_with_http_info(object_id, blog_author, **kwargs)  # noqa: E501

    def update_with_http_info(self, object_id, blog_author, **kwargs):  # noqa: E501
        """Update a Blog Author  # noqa: E501

        Sparse updates a single Blog Author object identified by the id in the path. All the column values need not be specified. Only the that need to be modified can be specified.   # noqa: E501
        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True

        >>> thread = api.update_with_http_info(object_id, blog_author, async_req=True)
        >>> result = thread.get()

        :param object_id: The Blog Author id. (required)
        :type object_id: str
        :param blog_author: The JSON representation of the updated Blog Author. (required)
        :type blog_author: BlogAuthor
        :param archived: Specifies whether to update deleted Blog Authors. Defaults to `false`.
        :type archived: bool
        :param async_req: Whether to execute the request asynchronously.
        :type async_req: bool, optional
        :param _return_http_data_only: response data without head status code
                                       and headers
        :type _return_http_data_only: bool, optional
        :param _preload_content: if False, the urllib3.HTTPResponse object will
                                 be returned without reading/decoding response
                                 data. Default is True.
        :type _preload_content: bool, optional
        :param _request_timeout: timeout setting for this request. If one
                                 number provided, it will be total request
                                 timeout. It can also be a pair (tuple) of
                                 (connection, read) timeouts.
        :param _request_auth: set to override the auth_settings for an a single
                              request; this effectively ignores the authentication
                              in the spec for a single request.
        :type _request_auth: dict, optional
        :type _content_type: string, optional: force content-type for the request
        :return: Returns the result object.
                 If the method is called asynchronously,
                 returns the request thread.
        :rtype: tuple(BlogAuthor, status_code(int), headers(HTTPHeaderDict))
        """

        local_var_params = locals()

        all_params = ["object_id", "blog_author", "archived"]
        all_params.extend(["async_req", "_return_http_data_only", "_preload_content", "_request_timeout", "_request_auth", "_content_type", "_headers"])

        for key, val in six.iteritems(local_var_params["kwargs"]):
            if key not in all_params:
                raise ApiTypeError("Got an unexpected keyword argument '%s'" " to method update" % key)
            local_var_params[key] = val
        del local_var_params["kwargs"]
        # verify the required parameter 'object_id' is set
        if self.api_client.client_side_validation and local_var_params.get("object_id") is None:  # noqa: E501
            raise ApiValueError("Missing the required parameter `object_id` when calling `update`")  # noqa: E501
        # verify the required parameter 'blog_author' is set
        if self.api_client.client_side_validation and local_var_params.get("blog_author") is None:  # noqa: E501
            raise ApiValueError("Missing the required parameter `blog_author` when calling `update`")  # noqa: E501

        collection_formats = {}

        path_params = {}
        if "object_id" in local_var_params:
            path_params["objectId"] = local_var_params["object_id"]  # noqa: E501

        query_params = []
        if local_var_params.get("archived") is not None:  # noqa: E501
            query_params.append(("archived", local_var_params["archived"]))  # noqa: E501

        header_params = dict(local_var_params.get("_headers", {}))

        form_params = []
        local_var_files = {}

        body_params = None
        if "blog_author" in local_var_params:
            body_params = local_var_params["blog_author"]
        # HTTP header `Accept`
        header_params["Accept"] = self.api_client.select_header_accept(["application/json", "*/*"])  # noqa: E501

        # HTTP header `Content-Type`
        content_types_list = local_var_params.get("_content_type", self.api_client.select_header_content_type(["application/json"], "PATCH", body_params))  # noqa: E501
        if content_types_list:
            header_params["Content-Type"] = content_types_list

        # Authentication setting
        auth_settings = ["oauth2"]  # noqa: E501

        response_types_map = {
            200: "BlogAuthor",
        }

        return self.api_client.call_api(
            "/cms/v3/blogs/authors/{objectId}",
            "PATCH",
            path_params,
            query_params,
            header_params,
            body=body_params,
            post_params=form_params,
            files=local_var_files,
            response_types_map=response_types_map,
            auth_settings=auth_settings,
            async_req=local_var_params.get("async_req"),
            _return_http_data_only=local_var_params.get("_return_http_data_only"),  # noqa: E501
            _preload_content=local_var_params.get("_preload_content", True),
            _request_timeout=local_var_params.get("_request_timeout"),
            collection_formats=collection_formats,
            _request_auth=local_var_params.get("_request_auth"),
        )

    def update_batch(self, batch_input_json_node, **kwargs):  # noqa: E501
        """Update a batch of Blog Authors  # noqa: E501

        Update the Blog Author objects identified in the request body.  # noqa: E501
        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True

        >>> thread = api.update_batch(batch_input_json_node, async_req=True)
        >>> result = thread.get()

        :param batch_input_json_node: A JSON array of the JSON representations of the updated Blog Authors. (required)
        :type batch_input_json_node: BatchInputJsonNode
        :param archived: Specifies whether to update deleted Blog Authors. Defaults to `false`.
        :type archived: bool
        :param async_req: Whether to execute the request asynchronously.
        :type async_req: bool, optional
        :param _preload_content: if False, the urllib3.HTTPResponse object will
                                 be returned without reading/decoding response
                                 data. Default is True.
        :type _preload_content: bool, optional
        :param _request_timeout: timeout setting for this request. If one
                                 number provided, it will be total request
                                 timeout. It can also be a pair (tuple) of
                                 (connection, read) timeouts.
        :return: Returns the result object.
                 If the method is called asynchronously,
                 returns the request thread.
        :rtype: BatchResponseBlogAuthor
        """
        kwargs["_return_http_data_only"] = True
        return self.update_batch_with_http_info(batch_input_json_node, **kwargs)  # noqa: E501

    def update_batch_with_http_info(self, batch_input_json_node, **kwargs):  # noqa: E501
        """Update a batch of Blog Authors  # noqa: E501

        Update the Blog Author objects identified in the request body.  # noqa: E501
        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True

        >>> thread = api.update_batch_with_http_info(batch_input_json_node, async_req=True)
        >>> result = thread.get()

        :param batch_input_json_node: A JSON array of the JSON representations of the updated Blog Authors. (required)
        :type batch_input_json_node: BatchInputJsonNode
        :param archived: Specifies whether to update deleted Blog Authors. Defaults to `false`.
        :type archived: bool
        :param async_req: Whether to execute the request asynchronously.
        :type async_req: bool, optional
        :param _return_http_data_only: response data without head status code
                                       and headers
        :type _return_http_data_only: bool, optional
        :param _preload_content: if False, the urllib3.HTTPResponse object will
                                 be returned without reading/decoding response
                                 data. Default is True.
        :type _preload_content: bool, optional
        :param _request_timeout: timeout setting for this request. If one
                                 number provided, it will be total request
                                 timeout. It can also be a pair (tuple) of
                                 (connection, read) timeouts.
        :param _request_auth: set to override the auth_settings for an a single
                              request; this effectively ignores the authentication
                              in the spec for a single request.
        :type _request_auth: dict, optional
        :type _content_type: string, optional: force content-type for the request
        :return: Returns the result object.
                 If the method is called asynchronously,
                 returns the request thread.
        :rtype: tuple(BatchResponseBlogAuthor, status_code(int), headers(HTTPHeaderDict))
        """

        local_var_params = locals()

        all_params = ["batch_input_json_node", "archived"]
        all_params.extend(["async_req", "_return_http_data_only", "_preload_content", "_request_timeout", "_request_auth", "_content_type", "_headers"])

        for key, val in six.iteritems(local_var_params["kwargs"]):
            if key not in all_params:
                raise ApiTypeError("Got an unexpected keyword argument '%s'" " to method update_batch" % key)
            local_var_params[key] = val
        del local_var_params["kwargs"]
        # verify the required parameter 'batch_input_json_node' is set
        if self.api_client.client_side_validation and local_var_params.get("batch_input_json_node") is None:  # noqa: E501
            raise ApiValueError("Missing the required parameter `batch_input_json_node` when calling `update_batch`")  # noqa: E501

        collection_formats = {}

        path_params = {}

        query_params = []
        if local_var_params.get("archived") is not None:  # noqa: E501
            query_params.append(("archived", local_var_params["archived"]))  # noqa: E501

        header_params = dict(local_var_params.get("_headers", {}))

        form_params = []
        local_var_files = {}

        body_params = None
        if "batch_input_json_node" in local_var_params:
            body_params = local_var_params["batch_input_json_node"]
        # HTTP header `Accept`
        header_params["Accept"] = self.api_client.select_header_accept(["application/json", "*/*"])  # noqa: E501

        # HTTP header `Content-Type`
        content_types_list = local_var_params.get("_content_type", self.api_client.select_header_content_type(["application/json"], "POST", body_params))  # noqa: E501
        if content_types_list:
            header_params["Content-Type"] = content_types_list

        # Authentication setting
        auth_settings = ["oauth2"]  # noqa: E501

        response_types_map = {
            200: "BatchResponseBlogAuthor",
            207: "BatchResponseBlogAuthorWithErrors",
        }

        return self.api_client.call_api(
            "/cms/v3/blogs/authors/batch/update",
            "POST",
            path_params,
            query_params,
            header_params,
            body=body_params,
            post_params=form_params,
            files=local_var_files,
            response_types_map=response_types_map,
            auth_settings=auth_settings,
            async_req=local_var_params.get("async_req"),
            _return_http_data_only=local_var_params.get("_return_http_data_only"),  # noqa: E501
            _preload_content=local_var_params.get("_preload_content", True),
            _request_timeout=local_var_params.get("_request_timeout"),
            collection_formats=collection_formats,
            _request_auth=local_var_params.get("_request_auth"),
        )

    def update_langs(self, update_languages_request_v_next, **kwargs):  # noqa: E501
        """Update languages of multi-language group  # noqa: E501

        Explicitly set new languages for each Blog Author in a multi-language group.  # noqa: E501
        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True

        >>> thread = api.update_langs(update_languages_request_v_next, async_req=True)
        >>> result = thread.get()

        :param update_languages_request_v_next: The JSON representation of the UpdateLanguagesRequest object. (required)
        :type update_languages_request_v_next: UpdateLanguagesRequestVNext
        :param async_req: Whether to execute the request asynchronously.
        :type async_req: bool, optional
        :param _preload_content: if False, the urllib3.HTTPResponse object will
                                 be returned without reading/decoding response
                                 data. Default is True.
        :type _preload_content: bool, optional
        :param _request_timeout: timeout setting for this request. If one
                                 number provided, it will be total request
                                 timeout. It can also be a pair (tuple) of
                                 (connection, read) timeouts.
        :return: Returns the result object.
                 If the method is called asynchronously,
                 returns the request thread.
        :rtype: Error
        """
        kwargs["_return_http_data_only"] = True
        return self.update_langs_with_http_info(update_languages_request_v_next, **kwargs)  # noqa: E501

    def update_langs_with_http_info(self, update_languages_request_v_next, **kwargs):  # noqa: E501
        """Update languages of multi-language group  # noqa: E501

        Explicitly set new languages for each Blog Author in a multi-language group.  # noqa: E501
        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True

        >>> thread = api.update_langs_with_http_info(update_languages_request_v_next, async_req=True)
        >>> result = thread.get()

        :param update_languages_request_v_next: The JSON representation of the UpdateLanguagesRequest object. (required)
        :type update_languages_request_v_next: UpdateLanguagesRequestVNext
        :param async_req: Whether to execute the request asynchronously.
        :type async_req: bool, optional
        :param _return_http_data_only: response data without head status code
                                       and headers
        :type _return_http_data_only: bool, optional
        :param _preload_content: if False, the urllib3.HTTPResponse object will
                                 be returned without reading/decoding response
                                 data. Default is True.
        :type _preload_content: bool, optional
        :param _request_timeout: timeout setting for this request. If one
                                 number provided, it will be total request
                                 timeout. It can also be a pair (tuple) of
                                 (connection, read) timeouts.
        :param _request_auth: set to override the auth_settings for an a single
                              request; this effectively ignores the authentication
                              in the spec for a single request.
        :type _request_auth: dict, optional
        :type _content_type: string, optional: force content-type for the request
        :return: Returns the result object.
                 If the method is called asynchronously,
                 returns the request thread.
        :rtype: tuple(Error, status_code(int), headers(HTTPHeaderDict))
        """

        local_var_params = locals()

        all_params = ["update_languages_request_v_next"]
        all_params.extend(["async_req", "_return_http_data_only", "_preload_content", "_request_timeout", "_request_auth", "_content_type", "_headers"])

        for key, val in six.iteritems(local_var_params["kwargs"]):
            if key not in all_params:
                raise ApiTypeError("Got an unexpected keyword argument '%s'" " to method update_langs" % key)
            local_var_params[key] = val
        del local_var_params["kwargs"]
        # verify the required parameter 'update_languages_request_v_next' is set
        if self.api_client.client_side_validation and local_var_params.get("update_languages_request_v_next") is None:  # noqa: E501
            raise ApiValueError("Missing the required parameter `update_languages_request_v_next` when calling `update_langs`")  # noqa: E501

        collection_formats = {}

        path_params = {}

        query_params = []

        header_params = dict(local_var_params.get("_headers", {}))

        form_params = []
        local_var_files = {}

        body_params = None
        if "update_languages_request_v_next" in local_var_params:
            body_params = local_var_params["update_languages_request_v_next"]
        # HTTP header `Accept`
        header_params["Accept"] = self.api_client.select_header_accept(["*/*"])  # noqa: E501

        # HTTP header `Content-Type`
        content_types_list = local_var_params.get("_content_type", self.api_client.select_header_content_type(["application/json"], "POST", body_params))  # noqa: E501
        if content_types_list:
            header_params["Content-Type"] = content_types_list

        # Authentication setting
        auth_settings = ["oauth2"]  # noqa: E501

        response_types_map = {}

        return self.api_client.call_api(
            "/cms/v3/blogs/authors/multi-language/update-languages",
            "POST",
            path_params,
            query_params,
            header_params,
            body=body_params,
            post_params=form_params,
            files=local_var_files,
            response_types_map=response_types_map,
            auth_settings=auth_settings,
            async_req=local_var_params.get("async_req"),
            _return_http_data_only=local_var_params.get("_return_http_data_only"),  # noqa: E501
            _preload_content=local_var_params.get("_preload_content", True),
            _request_timeout=local_var_params.get("_request_timeout"),
            collection_formats=collection_formats,
            _request_auth=local_var_params.get("_request_auth"),
        )
