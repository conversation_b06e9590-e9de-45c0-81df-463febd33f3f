{"auth": {"oauth2": {"scopes": {"https://www.googleapis.com/auth/cloud-platform": {"description": "See, edit, configure, and delete your Google Cloud data and see the email address for your Google Account."}}}}, "basePath": "", "baseUrl": "https://accesscontextmanager.googleapis.com/", "batchPath": "batch", "canonicalName": "Access Context Manager", "description": "An API for setting attribute based access control to requests to Google Cloud services.", "discoveryVersion": "v1", "documentationLink": "https://cloud.google.com/access-context-manager/docs/reference/rest/", "fullyEncodeReservedExpansion": true, "icons": {"x16": "http://www.google.com/images/icons/product/search-16.gif", "x32": "http://www.google.com/images/icons/product/search-32.gif"}, "id": "accesscontextmanager:v1beta", "kind": "discovery#restDescription", "mtlsRootUrl": "https://accesscontextmanager.mtls.googleapis.com/", "name": "accesscontextmanager", "ownerDomain": "google.com", "ownerName": "Google", "parameters": {"$.xgafv": {"description": "V1 error format.", "enum": ["1", "2"], "enumDescriptions": ["v1 error format", "v2 error format"], "location": "query", "type": "string"}, "access_token": {"description": "OAuth access token.", "location": "query", "type": "string"}, "alt": {"default": "json", "description": "Data format for response.", "enum": ["json", "media", "proto"], "enumDescriptions": ["Responses with Content-Type of application/json", "Media download with context-dependent Content-Type", "Responses with Content-Type of application/x-protobuf"], "location": "query", "type": "string"}, "callback": {"description": "JSONP", "location": "query", "type": "string"}, "fields": {"description": "Selector specifying which fields to include in a partial response.", "location": "query", "type": "string"}, "key": {"description": "API key. Your API key identifies your project and provides you with API access, quota, and reports. Required unless you provide an OAuth 2.0 token.", "location": "query", "type": "string"}, "oauth_token": {"description": "OAuth 2.0 token for the current user.", "location": "query", "type": "string"}, "prettyPrint": {"default": "true", "description": "Returns response with indentations and line breaks.", "location": "query", "type": "boolean"}, "quotaUser": {"description": "Available to use for quota purposes for server-side applications. Can be any arbitrary string assigned to a user, but should not exceed 40 characters.", "location": "query", "type": "string"}, "uploadType": {"description": "Legacy upload protocol for media (e.g. \"media\", \"multipart\").", "location": "query", "type": "string"}, "upload_protocol": {"description": "Upload protocol for media (e.g. \"raw\", \"multipart\").", "location": "query", "type": "string"}}, "protocol": "rest", "resources": {"accessPolicies": {"methods": {"create": {"description": "Create an `AccessPolicy`. Fails if this organization already has a `AccessPolicy`. The longrunning Operation will have a successful status once the `AccessPolicy` has propagated to long-lasting storage. Syntactic and basic semantic errors will be returned in `metadata` as a BadRequest proto.", "flatPath": "v1beta/accessPolicies", "httpMethod": "POST", "id": "accesscontextmanager.accessPolicies.create", "parameterOrder": [], "parameters": {}, "path": "v1beta/accessPolicies", "request": {"$ref": "AccessPolicy"}, "response": {"$ref": "Operation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "delete": {"description": "Delete an AccessPolicy by resource name. The longrunning Operation will have a successful status once the AccessPolicy has been removed from long-lasting storage.", "flatPath": "v1beta/accessPolicies/{accessPoliciesId}", "httpMethod": "DELETE", "id": "accesscontextmanager.accessPolicies.delete", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. Resource name for the access policy to delete. Format `accessPolicies/{policy_id}`", "location": "path", "pattern": "^accessPolicies/[^/]+$", "required": true, "type": "string"}}, "path": "v1beta/{+name}", "response": {"$ref": "Operation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "get": {"description": "Get an AccessPolicy by name.", "flatPath": "v1beta/accessPolicies/{accessPoliciesId}", "httpMethod": "GET", "id": "accesscontextmanager.accessPolicies.get", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. Resource name for the access policy to get. Format `accessPolicies/{policy_id}`", "location": "path", "pattern": "^accessPolicies/[^/]+$", "required": true, "type": "string"}}, "path": "v1beta/{+name}", "response": {"$ref": "AccessPolicy"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "list": {"description": "List all AccessPolicies under a container.", "flatPath": "v1beta/accessPolicies", "httpMethod": "GET", "id": "accesscontextmanager.accessPolicies.list", "parameterOrder": [], "parameters": {"pageSize": {"description": "Number of AccessPolicy instances to include in the list. Default 100.", "format": "int32", "location": "query", "type": "integer"}, "pageToken": {"description": "Next page token for the next batch of AccessPolicy instances. Defaults to the first page of results.", "location": "query", "type": "string"}, "parent": {"description": "Required. Resource name for the container to list AccessPolicy instances from. Format: `organizations/{org_id}`", "location": "query", "type": "string"}}, "path": "v1beta/accessPolicies", "response": {"$ref": "ListAccessPoliciesResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "patch": {"description": "Update an AccessPolicy. The longrunning Operation from this RPC will have a successful status once the changes to the AccessPolicy have propagated to long-lasting storage. Syntactic and basic semantic errors will be returned in `metadata` as a BadRequest proto.", "flatPath": "v1beta/accessPolicies/{accessPoliciesId}", "httpMethod": "PATCH", "id": "accesscontextmanager.accessPolicies.patch", "parameterOrder": ["name"], "parameters": {"name": {"description": "Output only. Resource name of the `AccessPolicy`. Format: `accessPolicies/{policy_id}`", "location": "path", "pattern": "^accessPolicies/[^/]+$", "required": true, "type": "string"}, "updateMask": {"description": "Required. Mask to control which fields get updated. Must be non-empty.", "format": "google-fieldmask", "location": "query", "type": "string"}}, "path": "v1beta/{+name}", "request": {"$ref": "AccessPolicy"}, "response": {"$ref": "Operation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}}, "resources": {"accessLevels": {"methods": {"create": {"description": "Create an Access Level. The longrunning operation from this RPC will have a successful status once the Access Level has propagated to long-lasting storage. Access Levels containing errors will result in an error response for the first error encountered.", "flatPath": "v1beta/accessPolicies/{accessPoliciesId}/accessLevels", "httpMethod": "POST", "id": "accesscontextmanager.accessPolicies.accessLevels.create", "parameterOrder": ["parent"], "parameters": {"parent": {"description": "Required. Resource name for the access policy which owns this Access Level. Format: `accessPolicies/{policy_id}`", "location": "path", "pattern": "^accessPolicies/[^/]+$", "required": true, "type": "string"}}, "path": "v1beta/{+parent}/accessLevels", "request": {"$ref": "AccessLevel"}, "response": {"$ref": "Operation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "delete": {"description": "Delete an Access Level by resource name. The longrunning operation from this RPC will have a successful status once the Access Level has been removed from long-lasting storage.", "flatPath": "v1beta/accessPolicies/{accessPoliciesId}/accessLevels/{accessLevelsId}", "httpMethod": "DELETE", "id": "accesscontextmanager.accessPolicies.accessLevels.delete", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. Resource name for the Access Level. Format: `accessPolicies/{policy_id}/accessLevels/{access_level_id}`", "location": "path", "pattern": "^accessPolicies/[^/]+/accessLevels/[^/]+$", "required": true, "type": "string"}}, "path": "v1beta/{+name}", "response": {"$ref": "Operation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "get": {"description": "Get an Access Level by resource name.", "flatPath": "v1beta/accessPolicies/{accessPoliciesId}/accessLevels/{accessLevelsId}", "httpMethod": "GET", "id": "accesscontextmanager.accessPolicies.accessLevels.get", "parameterOrder": ["name"], "parameters": {"accessLevelFormat": {"description": "Whether to return `BasicLevels` in the Cloud Common Expression Language rather than as `BasicLevels`. Defaults to AS_DEFINED, where Access Levels are returned as `BasicLevels` or `CustomLevels` based on how they were created. If set to CEL, all Access Levels are returned as `CustomLevels`. In the CEL case, `BasicLevels` are translated to equivalent `CustomLevels`.", "enum": ["LEVEL_FORMAT_UNSPECIFIED", "AS_DEFINED", "CEL"], "enumDescriptions": ["The format was not specified.", "Uses the format the resource was defined in. BasicLevels are returned as BasicLevels, CustomLevels are returned as CustomLevels.", "Use Cloud Common Expression Language when returning the resource. Both BasicLevels and CustomLevels are returned as CustomLevels."], "location": "query", "type": "string"}, "name": {"description": "Required. Resource name for the Access Level. Format: `accessPolicies/{policy_id}/accessLevels/{access_level_id}`", "location": "path", "pattern": "^accessPolicies/[^/]+/accessLevels/[^/]+$", "required": true, "type": "string"}}, "path": "v1beta/{+name}", "response": {"$ref": "AccessLevel"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "list": {"description": "List all Access Levels for an access policy.", "flatPath": "v1beta/accessPolicies/{accessPoliciesId}/accessLevels", "httpMethod": "GET", "id": "accesscontextmanager.accessPolicies.accessLevels.list", "parameterOrder": ["parent"], "parameters": {"accessLevelFormat": {"description": "Whether to return `BasicLevels` in the Cloud Common Expression language, as `CustomLevels`, rather than as `BasicLevels`. Defaults to returning `AccessLevels` in the format they were defined.", "enum": ["LEVEL_FORMAT_UNSPECIFIED", "AS_DEFINED", "CEL"], "enumDescriptions": ["The format was not specified.", "Uses the format the resource was defined in. BasicLevels are returned as BasicLevels, CustomLevels are returned as CustomLevels.", "Use Cloud Common Expression Language when returning the resource. Both BasicLevels and CustomLevels are returned as CustomLevels."], "location": "query", "type": "string"}, "pageSize": {"description": "Number of Access Levels to include in the list. Default 100.", "format": "int32", "location": "query", "type": "integer"}, "pageToken": {"description": "Next page token for the next batch of Access Level instances. Defaults to the first page of results.", "location": "query", "type": "string"}, "parent": {"description": "Required. Resource name for the access policy to list Access Levels from. Format: `accessPolicies/{policy_id}`", "location": "path", "pattern": "^accessPolicies/[^/]+$", "required": true, "type": "string"}}, "path": "v1beta/{+parent}/accessLevels", "response": {"$ref": "ListAccessLevelsResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "patch": {"description": "Update an Access Level. The longrunning operation from this RPC will have a successful status once the changes to the Access Level have propagated to long-lasting storage. Access Levels containing errors will result in an error response for the first error encountered.", "flatPath": "v1beta/accessPolicies/{accessPoliciesId}/accessLevels/{accessLevelsId}", "httpMethod": "PATCH", "id": "accesscontextmanager.accessPolicies.accessLevels.patch", "parameterOrder": ["name"], "parameters": {"name": {"description": "Resource name for the `AccessLevel`. Format: `accessPolicies/{access_policy}/accessLevels/{access_level}`. The `access_level` component must begin with a letter, followed by alphanumeric characters or `_`. Its maximum length is 50 characters. After you create an `AccessLevel`, you cannot change its `name`.", "location": "path", "pattern": "^accessPolicies/[^/]+/accessLevels/[^/]+$", "required": true, "type": "string"}, "updateMask": {"description": "Required. Mask to control which fields get updated. Must be non-empty.", "format": "google-fieldmask", "location": "query", "type": "string"}}, "path": "v1beta/{+name}", "request": {"$ref": "AccessLevel"}, "response": {"$ref": "Operation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}}}, "servicePerimeters": {"methods": {"create": {"description": "Create a Service Perimeter. The longrunning operation from this RPC will have a successful status once the Service Perimeter has propagated to long-lasting storage. Service Perimeters containing errors will result in an error response for the first error encountered.", "flatPath": "v1beta/accessPolicies/{accessPoliciesId}/servicePerimeters", "httpMethod": "POST", "id": "accesscontextmanager.accessPolicies.servicePerimeters.create", "parameterOrder": ["parent"], "parameters": {"parent": {"description": "Required. Resource name for the access policy which owns this Service Perimeter. Format: `accessPolicies/{policy_id}`", "location": "path", "pattern": "^accessPolicies/[^/]+$", "required": true, "type": "string"}}, "path": "v1beta/{+parent}/servicePerimeters", "request": {"$ref": "ServicePerimeter"}, "response": {"$ref": "Operation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "delete": {"description": "Delete a Service Perimeter by resource name. The longrunning operation from this RPC will have a successful status once the Service Perimeter has been removed from long-lasting storage.", "flatPath": "v1beta/accessPolicies/{accessPoliciesId}/servicePerimeters/{servicePerimetersId}", "httpMethod": "DELETE", "id": "accesscontextmanager.accessPolicies.servicePerimeters.delete", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. Resource name for the Service Perimeter. Format: `accessPolicies/{policy_id}/servicePerimeters/{service_perimeter_id}`", "location": "path", "pattern": "^accessPolicies/[^/]+/servicePerimeters/[^/]+$", "required": true, "type": "string"}}, "path": "v1beta/{+name}", "response": {"$ref": "Operation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "get": {"description": "Get a Service Perimeter by resource name.", "flatPath": "v1beta/accessPolicies/{accessPoliciesId}/servicePerimeters/{servicePerimetersId}", "httpMethod": "GET", "id": "accesscontextmanager.accessPolicies.servicePerimeters.get", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. Resource name for the Service Perimeter. Format: `accessPolicies/{policy_id}/servicePerimeters/{service_perimeters_id}`", "location": "path", "pattern": "^accessPolicies/[^/]+/servicePerimeters/[^/]+$", "required": true, "type": "string"}}, "path": "v1beta/{+name}", "response": {"$ref": "ServicePerimeter"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "list": {"description": "List all Service Perimeters for an access policy.", "flatPath": "v1beta/accessPolicies/{accessPoliciesId}/servicePerimeters", "httpMethod": "GET", "id": "accesscontextmanager.accessPolicies.servicePerimeters.list", "parameterOrder": ["parent"], "parameters": {"pageSize": {"description": "Number of Service Perimeters to include in the list. Default 100.", "format": "int32", "location": "query", "type": "integer"}, "pageToken": {"description": "Next page token for the next batch of Service Perimeter instances. Defaults to the first page of results.", "location": "query", "type": "string"}, "parent": {"description": "Required. Resource name for the access policy to list Service Perimeters from. Format: `accessPolicies/{policy_id}`", "location": "path", "pattern": "^accessPolicies/[^/]+$", "required": true, "type": "string"}}, "path": "v1beta/{+parent}/servicePerimeters", "response": {"$ref": "ListServicePerimetersResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "patch": {"description": "Update a Service Perimeter. The longrunning operation from this RPC will have a successful status once the changes to the Service Perimeter have propagated to long-lasting storage. Service Perimeter containing errors will result in an error response for the first error encountered.", "flatPath": "v1beta/accessPolicies/{accessPoliciesId}/servicePerimeters/{servicePerimetersId}", "httpMethod": "PATCH", "id": "accesscontextmanager.accessPolicies.servicePerimeters.patch", "parameterOrder": ["name"], "parameters": {"name": {"description": "Resource name for the `ServicePerimeter`. Format: `accessPolicies/{access_policy}/servicePerimeters/{service_perimeter}`. The `service_perimeter` component must begin with a letter, followed by alphanumeric characters or `_`. After you create a `ServicePerimeter`, you cannot change its `name`.", "location": "path", "pattern": "^accessPolicies/[^/]+/servicePerimeters/[^/]+$", "required": true, "type": "string"}, "updateMask": {"description": "Required. Mask to control which fields get updated. Must be non-empty.", "format": "google-fieldmask", "location": "query", "type": "string"}}, "path": "v1beta/{+name}", "request": {"$ref": "ServicePerimeter"}, "response": {"$ref": "Operation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}}}}}, "operations": {"methods": {"get": {"description": "Gets the latest state of a long-running operation. Clients can use this method to poll the operation result at intervals as recommended by the API service.", "flatPath": "v1beta/operations/{operationsId}", "httpMethod": "GET", "id": "accesscontextmanager.operations.get", "parameterOrder": ["name"], "parameters": {"name": {"description": "The name of the operation resource.", "location": "path", "pattern": "^operations/.*$", "required": true, "type": "string"}}, "path": "v1beta/{+name}", "response": {"$ref": "Operation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}}}}, "revision": "20230409", "rootUrl": "https://accesscontextmanager.googleapis.com/", "schemas": {"AccessContextManagerOperationMetadata": {"description": "Metadata of Access Context Manager's Long Running Operations.", "id": "AccessContextManagerOperationMetadata", "properties": {}, "type": "object"}, "AccessLevel": {"description": "An `AccessLevel` is a label that can be applied to requests to Google Cloud services, along with a list of requirements necessary for the label to be applied.", "id": "AccessLevel", "properties": {"basic": {"$ref": "BasicLevel", "description": "A `BasicLevel` composed of `Conditions`."}, "custom": {"$ref": "CustomLevel", "description": "A `CustomLevel` written in the Common Expression Language."}, "description": {"description": "Description of the `AccessLevel` and its use. Does not affect behavior.", "type": "string"}, "name": {"description": "Resource name for the `AccessLevel`. Format: `accessPolicies/{access_policy}/accessLevels/{access_level}`. The `access_level` component must begin with a letter, followed by alphanumeric characters or `_`. Its maximum length is 50 characters. After you create an `AccessLevel`, you cannot change its `name`.", "type": "string"}, "title": {"description": "Human readable title. Must be unique within the Policy.", "type": "string"}}, "type": "object"}, "AccessPolicy": {"description": "`AccessPolicy` is a container for `AccessLevels` (which define the necessary attributes to use Google Cloud services) and `ServicePerimeters` (which define regions of services able to freely pass data within a perimeter). An access policy is globally visible within an organization, and the restrictions it specifies apply to all projects within an organization.", "id": "AccessPolicy", "properties": {"name": {"description": "Output only. Resource name of the `AccessPolicy`. Format: `accessPolicies/{policy_id}`", "type": "string"}, "parent": {"description": "Required. The parent of this `AccessPolicy` in the Cloud Resource Hierarchy. Currently immutable once created. Format: `organizations/{organization_id}`", "type": "string"}, "title": {"description": "Required. Human readable title. Does not affect behavior.", "type": "string"}}, "type": "object"}, "BasicLevel": {"description": "`BasicLevel` is an `AccessLevel` using a set of recommended features.", "id": "BasicLevel", "properties": {"combiningFunction": {"description": "How the `conditions` list should be combined to determine if a request is granted this `AccessLevel`. If AND is used, each `Condition` in `conditions` must be satisfied for the `AccessLevel` to be applied. If OR is used, at least one `Condition` in `conditions` must be satisfied for the `AccessLevel` to be applied. Default behavior is AND.", "enum": ["AND", "OR"], "enumDescriptions": ["All `Conditions` must be true for the `BasicLevel` to be true.", "If at least one `Condition` is true, then the `BasicLevel` is true."], "type": "string"}, "conditions": {"description": "Required. A list of requirements for the `AccessLevel` to be granted.", "items": {"$ref": "Condition"}, "type": "array"}}, "type": "object"}, "Condition": {"description": "A condition necessary for an `AccessLevel` to be granted. The Condition is an AND over its fields. So a Condition is true if: 1) the request IP is from one of the listed subnetworks AND 2) the originating device complies with the listed device policy AND 3) all listed access levels are granted AND 4) the request was sent at a time allowed by the DateTimeRestriction.", "id": "Condition", "properties": {"devicePolicy": {"$ref": "DevicePolicy", "description": "Device specific restrictions, all restrictions must hold for the Condition to be true. If not specified, all devices are allowed."}, "ipSubnetworks": {"description": "CIDR block IP subnetwork specification. May be IPv4 or IPv6. Note that for a CIDR IP address block, the specified IP address portion must be properly truncated (i.e. all the host bits must be zero) or the input is considered malformed. For example, \"*********/24\" is accepted but \"*********/24\" is not. Similarly, for IPv6, \"2001:db8::/32\" is accepted whereas \"2001:db8::1/32\" is not. The originating IP of a request must be in one of the listed subnets in order for this Condition to be true. If empty, all IP addresses are allowed.", "items": {"type": "string"}, "type": "array"}, "members": {"description": "The request must be made by one of the provided user or service accounts. Groups are not supported. Syntax: `user:{emailid}` `serviceAccount:{emailid}` If not specified, a request may come from any user.", "items": {"type": "string"}, "type": "array"}, "negate": {"description": "Whether to negate the Condition. If true, the Condition becomes a NAND over its non-empty fields, each field must be false for the Condition overall to be satisfied. Defaults to false.", "type": "boolean"}, "regions": {"description": "The request must originate from one of the provided countries/regions. Must be valid ISO 3166-1 alpha-2 codes.", "items": {"type": "string"}, "type": "array"}, "requiredAccessLevels": {"description": "A list of other access levels defined in the same `Policy`, referenced by resource name. Referencing an `AccessLevel` which does not exist is an error. All access levels listed must be granted for the Condition to be true. Example: \"`accessPolicies/MY_POLICY/accessLevels/LEVEL_NAME\"`", "items": {"type": "string"}, "type": "array"}}, "type": "object"}, "CustomLevel": {"description": "`CustomLevel` is an `AccessLevel` using the Cloud Common Expression Language to represent the necessary conditions for the level to apply to a request. See CEL spec at: https://github.com/google/cel-spec", "id": "CustomLevel", "properties": {"expr": {"$ref": "Expr", "description": "Required. A Cloud CEL expression evaluating to a boolean."}}, "type": "object"}, "DevicePolicy": {"description": "`DevicePolicy` specifies device specific restrictions necessary to acquire a given access level. A `DevicePolicy` specifies requirements for requests from devices to be granted access levels, it does not do any enforcement on the device. `DevicePolicy` acts as an AND over all specified fields, and each repeated field is an OR over its elements. Any unset fields are ignored. For example, if the proto is { os_type : DESKTOP_WINDOWS, os_type : DESKTOP_LINUX, encryption_status: ENCRYPTED}, then the DevicePolicy will be true for requests originating from encrypted Linux desktops and encrypted Windows desktops.", "id": "DevicePolicy", "properties": {"allowedDeviceManagementLevels": {"description": "Allowed device management levels, an empty list allows all management levels.", "items": {"enum": ["MANAGEMENT_UNSPECIFIED", "NONE", "BASIC", "COMPLETE"], "enumDescriptions": ["The device's management level is not specified or not known.", "The device is not managed.", "Basic management is enabled, which is generally limited to monitoring and wiping the corporate account.", "Complete device management. This includes more thorough monitoring and the ability to directly manage the device (such as remote wiping). This can be enabled through the Android Enterprise Platform."], "type": "string"}, "type": "array"}, "allowedEncryptionStatuses": {"description": "Allowed encryptions statuses, an empty list allows all statuses.", "items": {"enum": ["ENCRYPTION_UNSPECIFIED", "ENCRYPTION_UNSUPPORTED", "UNENCRYPTED", "ENCRYPTED"], "enumDescriptions": ["The encryption status of the device is not specified or not known.", "The device does not support encryption.", "The device supports encryption, but is currently unencrypted.", "The device is encrypted."], "type": "string"}, "type": "array"}, "osConstraints": {"description": "Allowed OS versions, an empty list allows all types and all versions.", "items": {"$ref": "OsConstraint"}, "type": "array"}, "requireAdminApproval": {"description": "Whether the device needs to be approved by the customer admin.", "type": "boolean"}, "requireCorpOwned": {"description": "Whether the device needs to be corp owned.", "type": "boolean"}, "requireScreenlock": {"description": "Whether or not screenlock is required for the DevicePolicy to be true. Defaults to `false`.", "type": "boolean"}}, "type": "object"}, "Expr": {"description": "Represents a textual expression in the Common Expression Language (CEL) syntax. CEL is a C-like expression language. The syntax and semantics of CEL are documented at https://github.com/google/cel-spec. Example (Comparison): title: \"Summary size limit\" description: \"Determines if a summary is less than 100 chars\" expression: \"document.summary.size() < 100\" Example (Equality): title: \"Requestor is owner\" description: \"Determines if requestor is the document owner\" expression: \"document.owner == request.auth.claims.email\" Example (Logic): title: \"Public documents\" description: \"Determine whether the document should be publicly visible\" expression: \"document.type != 'private' && document.type != 'internal'\" Example (Data Manipulation): title: \"Notification string\" description: \"Create a notification string with a timestamp.\" expression: \"'New message received at ' + string(document.create_time)\" The exact variables and functions that may be referenced within an expression are determined by the service that evaluates it. See the service documentation for additional information.", "id": "Expr", "properties": {"description": {"description": "Optional. Description of the expression. This is a longer text which describes the expression, e.g. when hovered over it in a UI.", "type": "string"}, "expression": {"description": "Textual representation of an expression in Common Expression Language syntax.", "type": "string"}, "location": {"description": "Optional. String indicating the location of the expression for error reporting, e.g. a file name and a position in the file.", "type": "string"}, "title": {"description": "Optional. Title for the expression, i.e. a short string describing its purpose. This can be used e.g. in UIs which allow to enter the expression.", "type": "string"}}, "type": "object"}, "ListAccessLevelsResponse": {"description": "A response to `ListAccessLevelsRequest`.", "id": "ListAccessLevelsResponse", "properties": {"accessLevels": {"description": "List of the Access Level instances.", "items": {"$ref": "AccessLevel"}, "type": "array"}, "nextPageToken": {"description": "The pagination token to retrieve the next page of results. If the value is empty, no further results remain.", "type": "string"}}, "type": "object"}, "ListAccessPoliciesResponse": {"description": "A response to `ListAccessPoliciesRequest`.", "id": "ListAccessPoliciesResponse", "properties": {"accessPolicies": {"description": "List of the AccessPolicy instances.", "items": {"$ref": "AccessPolicy"}, "type": "array"}, "nextPageToken": {"description": "The pagination token to retrieve the next page of results. If the value is empty, no further results remain.", "type": "string"}}, "type": "object"}, "ListServicePerimetersResponse": {"description": "A response to `ListServicePerimetersRequest`.", "id": "ListServicePerimetersResponse", "properties": {"nextPageToken": {"description": "The pagination token to retrieve the next page of results. If the value is empty, no further results remain.", "type": "string"}, "servicePerimeters": {"description": "List of the Service Perimeter instances.", "items": {"$ref": "ServicePerimeter"}, "type": "array"}}, "type": "object"}, "Operation": {"description": "This resource represents a long-running operation that is the result of a network API call.", "id": "Operation", "properties": {"done": {"description": "If the value is `false`, it means the operation is still in progress. If `true`, the operation is completed, and either `error` or `response` is available.", "type": "boolean"}, "error": {"$ref": "Status", "description": "The error result of the operation in case of failure or cancellation."}, "metadata": {"additionalProperties": {"description": "Properties of the object. Contains field @type with type URL.", "type": "any"}, "description": "Service-specific metadata associated with the operation. It typically contains progress information and common metadata such as create time. Some services might not provide such metadata. Any method that returns a long-running operation should document the metadata type, if any.", "type": "object"}, "name": {"description": "The server-assigned name, which is only unique within the same service that originally returns it. If you use the default HTTP mapping, the `name` should be a resource name ending with `operations/{unique_id}`.", "type": "string"}, "response": {"additionalProperties": {"description": "Properties of the object. Contains field @type with type URL.", "type": "any"}, "description": "The normal response of the operation in case of success. If the original method returns no data on success, such as `Delete`, the response is `google.protobuf.Empty`. If the original method is standard `Get`/`Create`/`Update`, the response should be the resource. For other methods, the response should have the type `XxxResponse`, where `Xxx` is the original method name. For example, if the original method name is `TakeSnapshot()`, the inferred response type is `TakeSnapshotResponse`.", "type": "object"}}, "type": "object"}, "OsConstraint": {"description": "A restriction on the OS type and version of devices making requests.", "id": "OsConstraint", "properties": {"minimumVersion": {"description": "The minimum allowed OS version. If not set, any version of this OS satisfies the constraint. Format: `\"major.minor.patch\"`. Examples: `\"10.5.301\"`, `\"9.2.1\"`.", "type": "string"}, "osType": {"description": "Required. The allowed OS type.", "enum": ["OS_UNSPECIFIED", "DESKTOP_MAC", "DESKTOP_WINDOWS", "DESKTOP_LINUX", "DESKTOP_CHROME_OS", "ANDROID", "IOS"], "enumDescriptions": ["The operating system of the device is not specified or not known.", "A desktop Mac operating system.", "A desktop Windows operating system.", "A desktop Linux operating system.", "A desktop ChromeOS operating system.", "An Android operating system.", "An iOS operating system."], "type": "string"}, "requireVerifiedChromeOs": {"description": "Only allows requests from devices with a verified Chrome OS. Verifications includes requirements that the device is enterprise-managed, conformant to domain policies, and the caller has permission to call the API targeted by the request.", "type": "boolean"}}, "type": "object"}, "ServicePerimeter": {"description": "`ServicePerimeter` describes a set of Google Cloud resources which can freely import and export data amongst themselves, but not export outside of the `ServicePerimeter`. If a request with a source within this `ServicePerimeter` has a target outside of the `ServicePerimeter`, the request will be blocked. Otherwise the request is allowed. There are two types of Service Perimeter - Regular and Bridge. Regular Service Perimeters cannot overlap, a single Google Cloud project can only belong to a single regular Service Perimeter. Service Perimeter Bridges can contain only Google Cloud projects as members, a single Google Cloud project may belong to multiple Service Perimeter Bridges.", "id": "ServicePerimeter", "properties": {"description": {"description": "Description of the `ServicePerimeter` and its use. Does not affect behavior.", "type": "string"}, "name": {"description": "Resource name for the `ServicePerimeter`. Format: `accessPolicies/{access_policy}/servicePerimeters/{service_perimeter}`. The `service_perimeter` component must begin with a letter, followed by alphanumeric characters or `_`. After you create a `ServicePerimeter`, you cannot change its `name`.", "type": "string"}, "perimeterType": {"description": "Perimeter type indicator. A single project is allowed to be a member of single regular perimeter, but multiple service perimeter bridges. A project cannot be a included in a perimeter bridge without being included in regular perimeter. For perimeter bridges, restricted/unrestricted service lists as well as access lists must be empty.", "enum": ["PERIMETER_TYPE_REGULAR", "PERIMETER_TYPE_BRIDGE"], "enumDescriptions": ["Regular Perimeter. When no value is specified, the perimeter uses this type.", "Perimeter Bridge."], "type": "string"}, "status": {"$ref": "ServicePerimeterConfig", "description": "Current ServicePerimeter configuration. Specifies sets of resources, restricted/unrestricted services and access levels that determine perimeter content and boundaries."}, "title": {"description": "Human readable title. Must be unique within the Policy.", "type": "string"}}, "type": "object"}, "ServicePerimeterConfig": {"description": "`ServicePerimeterConfig` specifies a set of Google Cloud resources that describe specific Service Perimeter configuration.", "id": "ServicePerimeterConfig", "properties": {"accessLevels": {"description": "A list of `AccessLevel` resource names that allow resources within the `ServicePerimeter` to be accessed from the internet. `AccessLevels` listed must be in the same policy as this `ServicePerimeter`. Referencing a nonexistent `AccessLevel` is a syntax error. If no `AccessLevel` names are listed, resources within the perimeter can only be accessed via Google Cloud calls with request origins within the perimeter. Example: `\"accessPolicies/MY_POLICY/accessLevels/MY_LEVEL\"`. For Service Perimeter Bridge, must be empty.", "items": {"type": "string"}, "type": "array"}, "resources": {"description": "A list of Google Cloud resources that are inside of the service perimeter. Currently only projects are allowed. Format: `projects/{project_number}`", "items": {"type": "string"}, "type": "array"}, "restrictedServices": {"description": "Google Cloud services that are subject to the Service Perimeter restrictions. Must contain a list of services. For example, if `storage.googleapis.com` is specified, access to the storage buckets inside the perimeter must meet the perimeter's access restrictions.", "items": {"type": "string"}, "type": "array"}, "unrestrictedServices": {"description": "Google Cloud services that are not subject to the Service Perimeter restrictions. Deprecated. Must be set to a single wildcard \"*\". The wildcard means that unless explicitly specified by \"restricted_services\" list, any service is treated as unrestricted.", "items": {"type": "string"}, "type": "array"}, "vpcAccessibleServices": {"$ref": "VpcAccessibleServices", "description": "Beta. Configuration for APIs allowed within Perimeter."}}, "type": "object"}, "Status": {"description": "The `Status` type defines a logical error model that is suitable for different programming environments, including REST APIs and RPC APIs. It is used by [gRPC](https://github.com/grpc). Each `Status` message contains three pieces of data: error code, error message, and error details. You can find out more about this error model and how to work with it in the [API Design Guide](https://cloud.google.com/apis/design/errors).", "id": "Status", "properties": {"code": {"description": "The status code, which should be an enum value of google.rpc.Code.", "format": "int32", "type": "integer"}, "details": {"description": "A list of messages that carry the error details. There is a common set of message types for APIs to use.", "items": {"additionalProperties": {"description": "Properties of the object. Contains field @type with type URL.", "type": "any"}, "type": "object"}, "type": "array"}, "message": {"description": "A developer-facing error message, which should be in English. Any user-facing error message should be localized and sent in the google.rpc.Status.details field, or localized by the client.", "type": "string"}}, "type": "object"}, "VpcAccessibleServices": {"description": "Specifies how APIs are allowed to communicate within the Service Perimeter.", "id": "VpcAccessibleServices", "properties": {"allowedServices": {"description": "The list of APIs usable within the Service Perimeter. Must be empty unless 'enable_restriction' is True. You can specify a list of individual services, as well as include the 'RESTRICTED-SERVICES' value, which automatically includes all of the services protected by the perimeter.", "items": {"type": "string"}, "type": "array"}, "enableRestriction": {"description": "Whether to restrict API calls within the Service Perimeter to the list of APIs specified in 'allowed_services'.", "type": "boolean"}}, "type": "object"}}, "servicePath": "", "title": "Access Context Manager API", "version": "v1beta", "version_module": true}