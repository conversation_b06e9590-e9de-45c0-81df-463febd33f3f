../../Scripts/transformers-cli.exe,sha256=cIT-k0sQ_XWFPZ4J54x-W2GpFMGVA2SEOQujiQjDWNE,108390
transformers-4.49.0.dist-info/INSTALLER,sha256=zuuue4knoyJ-UwPPXg8fezS7VCrXJQrAP7zeNuwvFQg,4
transformers-4.49.0.dist-info/LICENSE,sha256=d_1HEN757DwPYiWADgI18VpCWr1KiwNVkSf814JhIEk,11418
transformers-4.49.0.dist-info/METADATA,sha256=IGnJzbmH-AmJG_f6rjUcyr86mPgGUUxavL6818guipA,44023
transformers-4.49.0.dist-info/RECORD,,
transformers-4.49.0.dist-info/REQUESTED,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
transformers-4.49.0.dist-info/WHEEL,sha256=tZoeGjtWxWRfdplE7E3d45VPlLNQnvbKiYnx7gwAy8A,92
transformers-4.49.0.dist-info/entry_points.txt,sha256=kgdW_0F_tXNrWKSZXKWKeUD_LqVgcji9j7atGXve8z4,81
transformers-4.49.0.dist-info/top_level.txt,sha256=GLBaeTo_CSdhnHvbxQ0kzpEHdlLuA_33foIogaWxntI,13
transformers/__init__.py,sha256=vkYYfSK7am-dwIfyBM4qGDcAk7MKDd8MOj2MrzncoWQ,320563
transformers/__pycache__/__init__.cpython-312.pyc,,
transformers/__pycache__/activations.cpython-312.pyc,,
transformers/__pycache__/activations_tf.cpython-312.pyc,,
transformers/__pycache__/audio_utils.cpython-312.pyc,,
transformers/__pycache__/cache_utils.cpython-312.pyc,,
transformers/__pycache__/configuration_utils.cpython-312.pyc,,
transformers/__pycache__/convert_graph_to_onnx.cpython-312.pyc,,
transformers/__pycache__/convert_pytorch_checkpoint_to_tf2.cpython-312.pyc,,
transformers/__pycache__/convert_slow_tokenizer.cpython-312.pyc,,
transformers/__pycache__/convert_slow_tokenizers_checkpoints_to_fast.cpython-312.pyc,,
transformers/__pycache__/convert_tf_hub_seq_to_seq_bert_to_pytorch.cpython-312.pyc,,
transformers/__pycache__/debug_utils.cpython-312.pyc,,
transformers/__pycache__/dependency_versions_check.cpython-312.pyc,,
transformers/__pycache__/dependency_versions_table.cpython-312.pyc,,
transformers/__pycache__/dynamic_module_utils.cpython-312.pyc,,
transformers/__pycache__/feature_extraction_sequence_utils.cpython-312.pyc,,
transformers/__pycache__/feature_extraction_utils.cpython-312.pyc,,
transformers/__pycache__/file_utils.cpython-312.pyc,,
transformers/__pycache__/hf_argparser.cpython-312.pyc,,
transformers/__pycache__/hyperparameter_search.cpython-312.pyc,,
transformers/__pycache__/image_processing_base.cpython-312.pyc,,
transformers/__pycache__/image_processing_utils.cpython-312.pyc,,
transformers/__pycache__/image_processing_utils_fast.cpython-312.pyc,,
transformers/__pycache__/image_transforms.cpython-312.pyc,,
transformers/__pycache__/image_utils.cpython-312.pyc,,
transformers/__pycache__/keras_callbacks.cpython-312.pyc,,
transformers/__pycache__/modelcard.cpython-312.pyc,,
transformers/__pycache__/modeling_attn_mask_utils.cpython-312.pyc,,
transformers/__pycache__/modeling_flash_attention_utils.cpython-312.pyc,,
transformers/__pycache__/modeling_flax_outputs.cpython-312.pyc,,
transformers/__pycache__/modeling_flax_pytorch_utils.cpython-312.pyc,,
transformers/__pycache__/modeling_flax_utils.cpython-312.pyc,,
transformers/__pycache__/modeling_gguf_pytorch_utils.cpython-312.pyc,,
transformers/__pycache__/modeling_outputs.cpython-312.pyc,,
transformers/__pycache__/modeling_rope_utils.cpython-312.pyc,,
transformers/__pycache__/modeling_tf_outputs.cpython-312.pyc,,
transformers/__pycache__/modeling_tf_pytorch_utils.cpython-312.pyc,,
transformers/__pycache__/modeling_tf_utils.cpython-312.pyc,,
transformers/__pycache__/modeling_utils.cpython-312.pyc,,
transformers/__pycache__/optimization.cpython-312.pyc,,
transformers/__pycache__/optimization_tf.cpython-312.pyc,,
transformers/__pycache__/processing_utils.cpython-312.pyc,,
transformers/__pycache__/pytorch_utils.cpython-312.pyc,,
transformers/__pycache__/safetensors_conversion.cpython-312.pyc,,
transformers/__pycache__/testing_utils.cpython-312.pyc,,
transformers/__pycache__/tf_utils.cpython-312.pyc,,
transformers/__pycache__/time_series_utils.cpython-312.pyc,,
transformers/__pycache__/tokenization_utils.cpython-312.pyc,,
transformers/__pycache__/tokenization_utils_base.cpython-312.pyc,,
transformers/__pycache__/tokenization_utils_fast.cpython-312.pyc,,
transformers/__pycache__/trainer.cpython-312.pyc,,
transformers/__pycache__/trainer_callback.cpython-312.pyc,,
transformers/__pycache__/trainer_pt_utils.cpython-312.pyc,,
transformers/__pycache__/trainer_seq2seq.cpython-312.pyc,,
transformers/__pycache__/trainer_utils.cpython-312.pyc,,
transformers/__pycache__/training_args.cpython-312.pyc,,
transformers/__pycache__/training_args_seq2seq.cpython-312.pyc,,
transformers/__pycache__/training_args_tf.cpython-312.pyc,,
transformers/activations.py,sha256=XkWFK1GO0_37_02KRNyTS3T2YGZw_ZDy-2xIYXL5eSw,8200
transformers/activations_tf.py,sha256=u2Y9dgDRgW-YbN_J-xmd05EK4p24rV8ZkzrQzpz4lCI,4689
transformers/agents/__init__.py,sha256=wVjvkIafUIotTIDrniFMuqMc_iyq4MGAG9aim8VYFMU,2859
transformers/agents/__pycache__/__init__.cpython-312.pyc,,
transformers/agents/__pycache__/agent_types.cpython-312.pyc,,
transformers/agents/__pycache__/agents.cpython-312.pyc,,
transformers/agents/__pycache__/default_tools.cpython-312.pyc,,
transformers/agents/__pycache__/document_question_answering.cpython-312.pyc,,
transformers/agents/__pycache__/evaluate_agent.cpython-312.pyc,,
transformers/agents/__pycache__/image_question_answering.cpython-312.pyc,,
transformers/agents/__pycache__/llm_engine.cpython-312.pyc,,
transformers/agents/__pycache__/monitoring.cpython-312.pyc,,
transformers/agents/__pycache__/prompts.cpython-312.pyc,,
transformers/agents/__pycache__/python_interpreter.cpython-312.pyc,,
transformers/agents/__pycache__/search.cpython-312.pyc,,
transformers/agents/__pycache__/speech_to_text.cpython-312.pyc,,
transformers/agents/__pycache__/text_to_speech.cpython-312.pyc,,
transformers/agents/__pycache__/tools.cpython-312.pyc,,
transformers/agents/__pycache__/translation.cpython-312.pyc,,
transformers/agents/agent_types.py,sha256=yjhBd2nxYkMYLqCfo4lbMUnlo7yT_wbZ_a1nHTSM7jc,8373
transformers/agents/agents.py,sha256=7kuR4HM0jpjYS25ePrhrAF1X993De2d-Emb2XNs0Czg,52066
transformers/agents/default_tools.py,sha256=kY4Ae9yn1l_hs1E-wQe3zT48WBU9hitIsBPiCvZBFVU,5252
transformers/agents/document_question_answering.py,sha256=-xI5v_R5MATPdra5VMHAEi1Zt3oiRv_fJu4N16Expkk,3634
transformers/agents/evaluate_agent.py,sha256=-q4K6vyCFIbPx8uYtf85jh8i7a6wFY_u5XOiRas58UQ,14929
transformers/agents/image_question_answering.py,sha256=yk7pGDeL0MVoRj56H11qt7JPwNTR-8q5dD80kbaHFW8,2004
transformers/agents/llm_engine.py,sha256=Uw2fsrqj92cGatLMOhWm7AWqSzbs9spidldbiriCumo,9233
transformers/agents/monitoring.py,sha256=l8M3Gk3h0l7RYhDTqKfv318ma9S0xxal2SO25J-sD9g,4684
transformers/agents/prompts.py,sha256=qNRNH562oGvoAh_lOEluo6jaTpl_WAtc6tQaIyHTJWE,31416
transformers/agents/python_interpreter.py,sha256=yT_a2h2a6lMj4QCCTSbal8jFT2n49FZyzCjzKJe5l8Q,38358
transformers/agents/search.py,sha256=Ai0qXG6ytcUW67VrrM40TVY-uGoJtWvp0LL4jleYfIs,2777
transformers/agents/speech_to_text.py,sha256=9VNfM56c-H9bL0g84hzGkDCT1_EWGwBtF0A8mQYpJ88,1496
transformers/agents/text_to_speech.py,sha256=FhVbSolKvMlSB454HF_RqtAULygdQ8xMas_B2G8gIyw,2468
transformers/agents/tools.py,sha256=-wtxZMO3jkHXN-Mkmzlr5f4qCZt_Y0Y38k83wsWuVnY,39568
transformers/agents/translation.py,sha256=P-Dnk3rUA3NPrJKiveRKptCwJ_M-3k1JEPyNze7VAAc,8671
transformers/audio_utils.py,sha256=todXMr1hYcfUn4VGufemLJMImTbqYn4OQFxKfQ52nMw,50318
transformers/cache_utils.py,sha256=Y3peOecX-Z7yzm13daOrB31iseHB-EH04kbo8iqwoqU,104108
transformers/commands/__init__.py,sha256=aFO3I7C6G9OLA9JZSc_yMaZl0glOQtjNPjqMFfu9wfQ,923
transformers/commands/__pycache__/__init__.cpython-312.pyc,,
transformers/commands/__pycache__/add_fast_image_processor.cpython-312.pyc,,
transformers/commands/__pycache__/add_new_model_like.cpython-312.pyc,,
transformers/commands/__pycache__/chat.cpython-312.pyc,,
transformers/commands/__pycache__/convert.cpython-312.pyc,,
transformers/commands/__pycache__/download.cpython-312.pyc,,
transformers/commands/__pycache__/env.cpython-312.pyc,,
transformers/commands/__pycache__/run.cpython-312.pyc,,
transformers/commands/__pycache__/serving.cpython-312.pyc,,
transformers/commands/__pycache__/train.cpython-312.pyc,,
transformers/commands/__pycache__/transformers_cli.cpython-312.pyc,,
transformers/commands/add_fast_image_processor.py,sha256=1AGXrOXQI4YHDXe5zS5PzqMjNNNOEEilf-4uy-GnueU,28829
transformers/commands/add_new_model_like.py,sha256=Nl8vdHyP1it2FieiiymuEmyjURPytbBNNhgSW0QsrgU,71130
transformers/commands/chat.py,sha256=o7JYCrip9hXV4F4pja3DoaAEcssfpViBhoM-LKBA--M,22127
transformers/commands/convert.py,sha256=lHz2sQti9HubMNwObLCc_sw9Y7L-IPcaYJMSJR_AVWM,7068
transformers/commands/download.py,sha256=GKPadx-YGBL7dHJSEcUp-QNOP3R2L71-gPGP0z6NNQI,2395
transformers/commands/env.py,sha256=2Lr2GZL8s0XfoyCSwwiXBlJGrvJ19xs7TrSKarCurpw,6210
transformers/commands/run.py,sha256=nyEe2lOoj6e0EOxjKeF08hdW9WVWa101r9hWXl9v3Jo,4249
transformers/commands/serving.py,sha256=CnNHFVM_SK_-aNxEJnq7vJK5dBqDBw7bxxQiv5truEU,8027
transformers/commands/train.py,sha256=FKlH-IYr3mVc7_mS5ObCyJaHs9JincYLg3Zt6WQz1ag,6341
transformers/commands/transformers_cli.py,sha256=kEOEKEY29_6fE-bgbdE_TsiD4YsO9ep1PyZQvhgakxk,1916
transformers/configuration_utils.py,sha256=3YtnBAUbVaJIIDRNS1mHZGYTxRUYMx5uEu63IdHq-Qs,58190
transformers/convert_graph_to_onnx.py,sha256=eoA4_4LmxwK-dirCgB0A75dAIMn_v9BoYEoJ_HaJc1Q,20151
transformers/convert_pytorch_checkpoint_to_tf2.py,sha256=oV_wzdVHbtpztPGQC_msuHh4zudzoj3WDABDUiDzKkE,14539
transformers/convert_slow_tokenizer.py,sha256=1GM1B5a2T8AtWqTlSXl4AT_-W6_1mXPQr_9Brq5YuOA,63233
transformers/convert_slow_tokenizers_checkpoints_to_fast.py,sha256=5FiWOleZOLTCtR7T8h2l1-XaryCz8VPbOfT8lK46vMQ,5076
transformers/convert_tf_hub_seq_to_seq_bert_to_pytorch.py,sha256=dy9yjETinWJl2MeQ-wv1J5HtmGm3j6Ki3r65optejRg,2910
transformers/data/__init__.py,sha256=MuXSchTzRSaUtUDC1uSeDkHiSbjtrQZg4IoKeKHoH6A,1490
transformers/data/__pycache__/__init__.cpython-312.pyc,,
transformers/data/__pycache__/data_collator.cpython-312.pyc,,
transformers/data/data_collator.py,sha256=OFEigTOKg2yJiVZvEnCaaDFd9LHxdJrOb-UgOkkbiSE,90582
transformers/data/datasets/__init__.py,sha256=PGzUJjdmTPOPMyjV4-Tj3sNrmmh-lspjyxrVbrfJoX8,909
transformers/data/datasets/__pycache__/__init__.cpython-312.pyc,,
transformers/data/datasets/__pycache__/glue.cpython-312.pyc,,
transformers/data/datasets/__pycache__/language_modeling.cpython-312.pyc,,
transformers/data/datasets/__pycache__/squad.cpython-312.pyc,,
transformers/data/datasets/glue.py,sha256=K3h2KxjIg0kWegPCw6ikbOL-lCFbKoQewb7R8wLZoIc,6163
transformers/data/datasets/language_modeling.py,sha256=E-VGwuyb09J4KmV8v37bNH5in90wDPuZHCYsqGdT7W0,23721
transformers/data/datasets/squad.py,sha256=OUTQDd687SQns7HRWDCgAjnuo_ZXihifLS6jF2bhUhc,9219
transformers/data/metrics/__init__.py,sha256=o9t_VTQtqU3lEhqvocDzFMm7OvAKD-uxrjPWy0r74BI,3632
transformers/data/metrics/__pycache__/__init__.cpython-312.pyc,,
transformers/data/metrics/__pycache__/squad_metrics.cpython-312.pyc,,
transformers/data/metrics/squad_metrics.py,sha256=mP6eaDcGTLsS4EhnvnD3U_Yyvcua_LVgElCkuxy2XJE,29697
transformers/data/processors/__init__.py,sha256=lvN5mp9mdrr5v6QvZT6VcoZ78zZUvXiumTm6Gdvlgvo,1014
transformers/data/processors/__pycache__/__init__.cpython-312.pyc,,
transformers/data/processors/__pycache__/glue.cpython-312.pyc,,
transformers/data/processors/__pycache__/squad.cpython-312.pyc,,
transformers/data/processors/__pycache__/utils.cpython-312.pyc,,
transformers/data/processors/__pycache__/xnli.cpython-312.pyc,,
transformers/data/processors/glue.py,sha256=1sHcfSWbl-ooNIEu3emKmDlpW-95UZT1JfDlGYx5TFA,23218
transformers/data/processors/squad.py,sha256=6aL-rVTo_eXVuc-Z4pV-oMC9H8Ci0wHU6s10Ca1sIyQ,33168
transformers/data/processors/utils.py,sha256=GSaZbJ--XYq57vqyRVx_5LHSR4tklzFyR7ZKHGWsTAs,13829
transformers/data/processors/xnli.py,sha256=sgcYz9YSfHY9NS0LO_YeFRRjq-nJFsDhFUP4NJeu-Q4,3481
transformers/debug_utils.py,sha256=6q8ArB104GdcIC2qfBQzKLxO7PfXmHEKdYtfL2FOK2w,12907
transformers/dependency_versions_check.py,sha256=6HbgtT2Wp-QZGOAdyUOklHvNA4rOVITGHrX34dtMOqg,2115
transformers/dependency_versions_table.py,sha256=BNyZA9GQ4GP1vnNWLFAFgS-N9Dq9hbGCwtDh0-wvOuE,3566
transformers/dynamic_module_utils.py,sha256=DhCKKFRZkyVpqGYj-9dk8SZqvDHq1KW37plnUHvWhNI,29689
transformers/feature_extraction_sequence_utils.py,sha256=xE5f0cSWWodEjCwNDsG0Dl9kL3B9KPs-SsF4YTWNh0M,18307
transformers/feature_extraction_utils.py,sha256=Db51L6rXokWQ3Zu7HNp2IWG-_W2MKoBceoa2QMcv6JU,30508
transformers/file_utils.py,sha256=_TiPVfyjYPltudGAj6Fm4Bq0hJhzwb_V7zfoFwxRUkE,3722
transformers/generation/__init__.py,sha256=rw0kiEMJVs2VTKFI1Td2gPASLQNqZUJTJ_52Kgc1lRM,12303
transformers/generation/__pycache__/__init__.cpython-312.pyc,,
transformers/generation/__pycache__/beam_constraints.cpython-312.pyc,,
transformers/generation/__pycache__/beam_search.cpython-312.pyc,,
transformers/generation/__pycache__/candidate_generator.cpython-312.pyc,,
transformers/generation/__pycache__/configuration_utils.cpython-312.pyc,,
transformers/generation/__pycache__/flax_logits_process.cpython-312.pyc,,
transformers/generation/__pycache__/flax_utils.cpython-312.pyc,,
transformers/generation/__pycache__/logits_process.cpython-312.pyc,,
transformers/generation/__pycache__/stopping_criteria.cpython-312.pyc,,
transformers/generation/__pycache__/streamers.cpython-312.pyc,,
transformers/generation/__pycache__/tf_logits_process.cpython-312.pyc,,
transformers/generation/__pycache__/tf_utils.cpython-312.pyc,,
transformers/generation/__pycache__/utils.cpython-312.pyc,,
transformers/generation/__pycache__/watermarking.cpython-312.pyc,,
transformers/generation/beam_constraints.py,sha256=Yt9dtQARVET_WaC26Sil49DO-b2YfosyhP2Rx5ILReI,19274
transformers/generation/beam_search.py,sha256=rGjKNmKPa61POCkEh5VmZn9oUaqM3eIJ1jXhkn7CPYU,49536
transformers/generation/candidate_generator.py,sha256=BR0dBDyQb6D8TrnhWJKad4HmiizxElYTTdAjeiqOV-0,43796
transformers/generation/configuration_utils.py,sha256=mn2-34gpaLbfLizxE7j7QX66Y_VqdIulTzSQNhz4s4A,85121
transformers/generation/flax_logits_process.py,sha256=w5WaWXrp6QmD4aqLYrXgjqpb21rpPV3rzL_etJ1RBhI,23007
transformers/generation/flax_utils.py,sha256=JekzlRjyMczgTdbt3VL4I_xeMO2uzgzqHJ7QxaWyx20,50469
transformers/generation/logits_process.py,sha256=GPhfxAwBTjRDE1D9duNaj1zdKG31EtHJvVqDMi6DQyo,137733
transformers/generation/stopping_criteria.py,sha256=Eb6Hg0eQ6WuW3e8PnM9OJz6bFNYQLACgLIWBa1m4xV0,28945
transformers/generation/streamers.py,sha256=Tn1mP3Tb6jTSoJxG0wf6Dbf0SoHC15_r_wJLOYJUGkw,13026
transformers/generation/tf_logits_process.py,sha256=qLtG-EBD4SgK6QaSYApwpegJ4FrlR-rKdxF0FMjhAG8,28714
transformers/generation/tf_utils.py,sha256=TjWN_pKijyZs0oDz8w7Jff_vmVz8RE6srcB2ZtA0mZI,175496
transformers/generation/utils.py,sha256=d8X8E53CfPEOAxHXMpdsFLfYnvUX45EIuoSs6Hl9M4M,253488
transformers/generation/watermarking.py,sha256=8d4AFs9CRnGsguiqLKy8Lz6g2pg0wLAaZtAwA7qsIxA,24416
transformers/hf_argparser.py,sha256=LAsCw9jA07NE01SwSAS9r1igHuBEP091J-ALRtqI8h4,20636
transformers/hyperparameter_search.py,sha256=wmfAWk_NTUQj3MezO_6CaDaJyUt9pbARcs-tbo_BdeM,4171
transformers/image_processing_base.py,sha256=Jl2W4htov0a32MNSoAr5NTQCXFUUabteSFKBxuS9yLU,25430
transformers/image_processing_utils.py,sha256=JLn4CzUxw4Q7Slzsl_9nwpu2gbslfFJb9yFhfZllMy0,13498
transformers/image_processing_utils_fast.py,sha256=orHQV5qhLsgabEPqXdM6iYhIxPZY3RzuritRnaiNrJw,29072
transformers/image_transforms.py,sha256=uJcJo6vYjFgv0dwoQ4aNePdQ_BH5sRMayW9Hn-q3EeM,37753
transformers/image_utils.py,sha256=c7zk7XpTZ5DTdoxqzk_RLh6bnoDY8G08hCa6xryEJY4,53194
transformers/integrations/__init__.py,sha256=dgXt8q9xlJ6EptztXRpjERw0PU8ScthpBZCay1SH4pI,7256
transformers/integrations/__pycache__/__init__.cpython-312.pyc,,
transformers/integrations/__pycache__/aqlm.cpython-312.pyc,,
transformers/integrations/__pycache__/awq.cpython-312.pyc,,
transformers/integrations/__pycache__/bitnet.cpython-312.pyc,,
transformers/integrations/__pycache__/bitsandbytes.cpython-312.pyc,,
transformers/integrations/__pycache__/deepspeed.cpython-312.pyc,,
transformers/integrations/__pycache__/eetq.cpython-312.pyc,,
transformers/integrations/__pycache__/executorch.cpython-312.pyc,,
transformers/integrations/__pycache__/fbgemm_fp8.cpython-312.pyc,,
transformers/integrations/__pycache__/finegrained_fp8.cpython-312.pyc,,
transformers/integrations/__pycache__/flash_attention.cpython-312.pyc,,
transformers/integrations/__pycache__/flex_attention.cpython-312.pyc,,
transformers/integrations/__pycache__/fsdp.cpython-312.pyc,,
transformers/integrations/__pycache__/ggml.cpython-312.pyc,,
transformers/integrations/__pycache__/higgs.cpython-312.pyc,,
transformers/integrations/__pycache__/hqq.cpython-312.pyc,,
transformers/integrations/__pycache__/integration_utils.cpython-312.pyc,,
transformers/integrations/__pycache__/mistral.cpython-312.pyc,,
transformers/integrations/__pycache__/peft.cpython-312.pyc,,
transformers/integrations/__pycache__/quanto.cpython-312.pyc,,
transformers/integrations/__pycache__/sdpa_attention.cpython-312.pyc,,
transformers/integrations/__pycache__/spqr.cpython-312.pyc,,
transformers/integrations/__pycache__/tiktoken.cpython-312.pyc,,
transformers/integrations/__pycache__/tpu.cpython-312.pyc,,
transformers/integrations/__pycache__/vptq.cpython-312.pyc,,
transformers/integrations/aqlm.py,sha256=g4ujHmqCr66ugoP93lemtIaMQLLDMDZ1TeeR0U1MIOw,4536
transformers/integrations/awq.py,sha256=gZjVCxWhVErViMh1AHtCWw3bjtHRGwlu5ick6Qy2pFY,20580
transformers/integrations/bitnet.py,sha256=6GAtWEDsaelz79kPx2mHXcz273r6gDl0nrO9RaW-8EU,10706
transformers/integrations/bitsandbytes.py,sha256=BXP6UURMb8YTQGcDRyCVnbfzIhvizjgcISOWCebrz-E,24797
transformers/integrations/deepspeed.py,sha256=Kgu_D-dO6m9UIJI5r7mlBqVhbKoYyu3yZNXJsCzdJzI,19140
transformers/integrations/eetq.py,sha256=ZDxYk6vfjGsjQoQeFOQ2odn58hc6xrYbwdKtfLdDp-Q,5365
transformers/integrations/executorch.py,sha256=XEp6lz81r46onEUr8c0j4YuVja6kYp-kuGsLb-U3muU,9514
transformers/integrations/fbgemm_fp8.py,sha256=g322A5Hhz7agAACHYgeEvh6P4gWcnRt1BxkUBwYbMLA,7431
transformers/integrations/finegrained_fp8.py,sha256=leOHpHCNqZCbGl3XEIWUoB-QC3xmgjJDdDAkemFA3U4,14669
transformers/integrations/flash_attention.py,sha256=Y3uV1y5mPEix0ygglQNHqvkIrWDdvxH8amPMxbNJdL4,2293
transformers/integrations/flex_attention.py,sha256=ghWhZc4hnHCnOY6bZhpOdu5NLxgoyoOh-5kVdfFaXQI,1617
transformers/integrations/fsdp.py,sha256=fIrl8PQhkQiCQ5EqJjkwCdbTRLXMwmb9qn-Klp9Gew0,1138
transformers/integrations/ggml.py,sha256=hDvrBQtz6SnWM1z7PFNNs8jmDJwjqi91VOVIU0URRkM,26543
transformers/integrations/higgs.py,sha256=y2XVtfatlyeL5gqnQyZBM0WfKCnQwB8UTcCV9KSbTWo,31382
transformers/integrations/hqq.py,sha256=meBkmmy99NgsTG0Oir9P_DMEifRgpbuCvbmgu73KTTM,5088
transformers/integrations/integration_utils.py,sha256=XFPbY3JSe9IK4H7JTs5kQuGRxq88KWtZpuoorba0H8Q,97337
transformers/integrations/mistral.py,sha256=xMEkFF5aKOLcXWS_cnRXma4RMOSXn94uacKy1OVIRJU,4017
transformers/integrations/peft.py,sha256=B6QezThvFBaXD6r3QhEySY8YVSsulp7ZyXx5FAp3xWM,27464
transformers/integrations/quanto.py,sha256=UfGtKGRFB8eQiUtTBiyENo4B5roY1DlmlCCWrPbjl5g,4378
transformers/integrations/sdpa_attention.py,sha256=V4gU9nhaPb9nK4HZuaKI9PNVHHSgDjKOrk8XZWGhCeE,2547
transformers/integrations/spqr.py,sha256=nHTdlyfkCc5vJO60TMZuE9pUiTTPfaqYV7kVLF6PMd0,5525
transformers/integrations/tiktoken.py,sha256=2s3O3_3dsA7pbsz1Lu_eLA2SrloMZWVpg0NklRxPMlY,1627
transformers/integrations/tpu.py,sha256=Y8YMwIrEgh1s-OCNbOQZFD1_3Tvqpo3e1H6eECTceSU,1392
transformers/integrations/vptq.py,sha256=JxwcJsdKbU_Hhi0W9urWXuInRFOEuAVOU1BQoT-IyuA,4545
transformers/keras_callbacks.py,sha256=i95nrEd_QsEo10x3T9RqZf3xGzfPiMOhmU1Ef_HvnGE,20675
transformers/kernels/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
transformers/kernels/__pycache__/__init__.cpython-312.pyc,,
transformers/kernels/deformable_detr/cpu/ms_deform_attn_cpu.cpp,sha256=VcCGm9IrvgVvmyZt0KyP16Q-ONmbeg6bKwccP6KadL0,1255
transformers/kernels/deformable_detr/cpu/ms_deform_attn_cpu.h,sha256=nvVsKj9nabQ7IaNY4di5xVx6u-0lIifQvLg2JCoxiik,1138
transformers/kernels/deformable_detr/cuda/ms_deform_attn_cuda.cu,sha256=lOZXaTmKyo-sk1NCFxkmPqVCSc1Q--dT9TPHyMJKOJE,7550
transformers/kernels/deformable_detr/cuda/ms_deform_attn_cuda.cuh,sha256=i1PdHlmUAGWRdRBOCHAI0z9SZ_uPdG59xDljfjt5rYM,61655
transformers/kernels/deformable_detr/cuda/ms_deform_attn_cuda.h,sha256=xxP17aer-SiU9J5ASLHdtLIyhFmHC5iLcPIPNW2xkrg,1694
transformers/kernels/deformable_detr/cuda/ms_deform_im2col_cuda.cuh,sha256=2bq9KLsXxJfA9LjtS3FJWPJdPR2Tdq64eMx0CbmJ1kM,54814
transformers/kernels/deformable_detr/ms_deform_attn.h,sha256=OlevndFgZvH6NHoHDQh8qqh8FBbguaxEiVC1vAXAFhU,1823
transformers/kernels/deformable_detr/vision.cpp,sha256=8RvZy7P_MMx5QEszo_MwNODddJLQ8mKcmmMfgLYC_HA,798
transformers/kernels/deta/cpu/ms_deform_attn_cpu.cpp,sha256=VcCGm9IrvgVvmyZt0KyP16Q-ONmbeg6bKwccP6KadL0,1255
transformers/kernels/deta/cpu/ms_deform_attn_cpu.h,sha256=nvVsKj9nabQ7IaNY4di5xVx6u-0lIifQvLg2JCoxiik,1138
transformers/kernels/deta/cuda/ms_deform_attn_cuda.cu,sha256=M5-bW9g5z-upTFMNPIfnyLAqKTxGMCjAPqBr0GmWHX8,7360
transformers/kernels/deta/cuda/ms_deform_attn_cuda.cuh,sha256=hygB20Vh3RttOSdCuTFz8V0d3CXNp-Q89x22rYmD258,61433
transformers/kernels/deta/cuda/ms_deform_attn_cuda.h,sha256=rPWOOMo3QyFdB5kMiexpApLFZ4dnRtx4CluEAGwsfO8,1139
transformers/kernels/deta/cuda/ms_deform_im2col_cuda.cuh,sha256=BRN8-yfSHY8ChLij8jFl2_z2LL0LEFKuVF6Byi-YLAY,54695
transformers/kernels/deta/ms_deform_attn.h,sha256=H2bBXGyl0R-v2DqGVz11asoRvxbjZ9iWB9djomZTpgY,1837
transformers/kernels/deta/vision.cpp,sha256=8RvZy7P_MMx5QEszo_MwNODddJLQ8mKcmmMfgLYC_HA,798
transformers/kernels/falcon_mamba/__init__.py,sha256=bt0j851F1uuH7flSsTvIqdh9zdKVTOVKWt3datb15SI,721
transformers/kernels/falcon_mamba/__pycache__/__init__.cpython-312.pyc,,
transformers/kernels/falcon_mamba/__pycache__/selective_scan_with_ln_interface.cpython-312.pyc,,
transformers/kernels/falcon_mamba/selective_scan_with_ln_interface.py,sha256=649oJD0sox1I-TCkZuRMjYm3tWQkQ3VoPXLNeOcN_ss,19731
transformers/kernels/mra/cuda_kernel.cu,sha256=LxxRYTymSoBEQpWXHA0PMzwZwpolcwX7mFAjwU8-ZMc,11678
transformers/kernels/mra/cuda_kernel.h,sha256=UJvYq_MDzhcp07bZpYcOBn8ZGFcf_Ax1dynuiVTBvmA,1682
transformers/kernels/mra/cuda_launch.cu,sha256=Ox5MTACriC30CGyn-g1Kb5EgQSMAZSaN6fpit3xLFWc,4072
transformers/kernels/mra/cuda_launch.h,sha256=RVCkN_euasvgPK0zADNRvRYGWd4ah5l9X-7UG_AcdH8,707
transformers/kernels/mra/torch_extension.cpp,sha256=N0YdBLVX0lZabckJzV_RYTHS2atCNvn13E4Ivobt25g,1405
transformers/kernels/rwkv/wkv_cuda.cu,sha256=EvaUrEnw_qr2EjMKP-Pq7VPzFfGlMJnFhdHNLtn1fPU,6219
transformers/kernels/rwkv/wkv_cuda_bf16.cu,sha256=DG9hTtOAlrnpDFahjt-MmnOxjMuhGU55GPsmV21HtrQ,6633
transformers/kernels/rwkv/wkv_op.cpp,sha256=qSExhKdT6p3hyaTv5SypCnH_c7EmaX6HbhTcCntvZWg,4022
transformers/kernels/yoso/common.h,sha256=Tq2rOUtE8Y4DRAUrRISvwIwVI3u8JBf21WgWSAYiDlQ,273
transformers/kernels/yoso/common_cuda.h,sha256=Sji70AuVcuZSotLF7Gotmun9MJuOHo8wEkxizKXLRtc,258
transformers/kernels/yoso/common_cuda_device.h,sha256=y6WUgAiapnMKqthRMS5s-DMSWNVkar_i8g4KPFvqiuk,2063
transformers/kernels/yoso/fast_lsh_cumulation.cu,sha256=LA4LGNgyXT3osIyQtFBcRanSyNQWm8yqmpz7AeLP7cw,19061
transformers/kernels/yoso/fast_lsh_cumulation.h,sha256=1cTWZjOm751HGiEB5P-UPJ8SE1VO7XRyXmBgyxYDyjI,1575
transformers/kernels/yoso/fast_lsh_cumulation_cuda.cu,sha256=HKGLWl-WFz5BXjaAPHTNTbG6IUkJjhBdvFf2K7hrDVQ,32870
transformers/kernels/yoso/fast_lsh_cumulation_cuda.h,sha256=_KGI8HQbVFtCN5KAcSGpyiJ2foGi26RKen138CUc2fY,5490
transformers/kernels/yoso/fast_lsh_cumulation_torch.cpp,sha256=-Rh7o39Z3rtOPwNnEM-c51TCqywpVdK0WVaA7VRrXbQ,3154
transformers/loss/__init__.py,sha256=qETsqCwayu6Ymj_J4_A_eiwiaMRHQ0noWKM35naanzc,606
transformers/loss/__pycache__/__init__.cpython-312.pyc,,
transformers/loss/__pycache__/loss_deformable_detr.cpython-312.pyc,,
transformers/loss/__pycache__/loss_for_object_detection.cpython-312.pyc,,
transformers/loss/__pycache__/loss_rt_detr.cpython-312.pyc,,
transformers/loss/__pycache__/loss_utils.cpython-312.pyc,,
transformers/loss/loss_deformable_detr.py,sha256=6nybwni_dj2_H8UEe3e2o3kymMVHcbFhPpjPyx87Kqc,7335
transformers/loss/loss_for_object_detection.py,sha256=__Siy5m1zEQPZk0JbUOcA3ZtoJ0jRt0qq7dwGR6MGtA,24594
transformers/loss/loss_rt_detr.py,sha256=LmKmN5nRyEqo2SdP1-Q34P9m5ERGy7lUxFsKiwE0stY,22130
transformers/loss/loss_utils.py,sha256=2-auAN_ytfKHPKphdqdxmwrarNyap6k1UQD2Hcw_FA8,5930
transformers/modelcard.py,sha256=ypsfFeoDjL8ENCrIqYAqcUZJZCS0p_XuzaOqtDQaAFc,35618
transformers/modeling_attn_mask_utils.py,sha256=FXKojWtrHCZ3B68Lvrfd8e4berJl3z5UR_e3mdUl688,21234
transformers/modeling_flash_attention_utils.py,sha256=-OvWxttztW00zNb9h0DM0gsK74FSSJR-kI2eOyM2s-k,17050
transformers/modeling_flax_outputs.py,sha256=wXse1g9VyQyVOZ9DrbPALeoZBdS45fsBA9fNrGnwaZc,41961
transformers/modeling_flax_pytorch_utils.py,sha256=ua9Y5tMv5AevLU0CUS3GLqcEueCupW_ozL77FH1AG7U,21634
transformers/modeling_flax_utils.py,sha256=koz6c6GgLrK2YpJdLUQr_WsBPKl9QE9J_G_o_7FbkOo,61486
transformers/modeling_gguf_pytorch_utils.py,sha256=uVa6OykFqzB52JvSB_jHtzob-scvOeZmac5HOxmppxw,19786
transformers/modeling_outputs.py,sha256=CYpjijqZNOVUc-kixDLI-jMFru9MhpDQvnncSfp0wb4,112567
transformers/modeling_rope_utils.py,sha256=KQP_e3ltxrC4tnYpi4w4DMMZT-IVEkHf-TN1Z4VwvTQ,28557
transformers/modeling_tf_outputs.py,sha256=nXCMOmFZ7IZFVuiQr7EU2ciV9QqwOYPYld_r2jBxVpE,56074
transformers/modeling_tf_pytorch_utils.py,sha256=eJHbaqN6Orz3fGEeSyP213OMCjTWt3I53Ha2gStR9zo,27773
transformers/modeling_tf_utils.py,sha256=WlNkvHIMXllppL1zJ0z5JWsq6Yzi8ifxqzZIEfGtzA4,166959
transformers/modeling_utils.py,sha256=mjt6Fe_Vd3ugWBz9LP_hTLY_ylSIOW5rycTBuc7a0hU,285939
transformers/models/__init__.py,sha256=YYubQLM1SJ48mlEk9QfaV1TJblYIFDcl2yAUAXf7M-Y,4788
transformers/models/__pycache__/__init__.cpython-312.pyc,,
transformers/models/albert/__init__.py,sha256=WjQ4NtNxKNj7Hvk9lA2OXmdgD_SNFp1wLS2eeL3-WoE,1154
transformers/models/albert/__pycache__/__init__.cpython-312.pyc,,
transformers/models/albert/__pycache__/configuration_albert.cpython-312.pyc,,
transformers/models/albert/__pycache__/modeling_albert.cpython-312.pyc,,
transformers/models/albert/__pycache__/modeling_flax_albert.cpython-312.pyc,,
transformers/models/albert/__pycache__/modeling_tf_albert.cpython-312.pyc,,
transformers/models/albert/__pycache__/tokenization_albert.cpython-312.pyc,,
transformers/models/albert/__pycache__/tokenization_albert_fast.cpython-312.pyc,,
transformers/models/albert/configuration_albert.py,sha256=nwBi1Gg1MRw_z-9Pwr7kjtJykLOhj5_zJC9zGzggrfQ,8137
transformers/models/albert/modeling_albert.py,sha256=rbM9k-zdZfH_qqKQCwkgxAaJKZQU2yM2aIJheQaTsSo,64790
transformers/models/albert/modeling_flax_albert.py,sha256=_oJ81lgLHwfYC-9DQq96Isrlj99jI02X5mol4eyu_j4,41027
transformers/models/albert/modeling_tf_albert.py,sha256=l4cknO2O9IvaGgQgHD5drqAWkEjfOvMjdc6oBooSoYc,69145
transformers/models/albert/tokenization_albert.py,sha256=y71xBZjcpGMdpWk6PC8ljQ1O4V4FLPDHHk6QF2e-LbY,14531
transformers/models/albert/tokenization_albert_fast.py,sha256=2I7-4DbfP_xh3kib611aL7FgFY3TE3R3jrCCY0KUquw,8866
transformers/models/align/__init__.py,sha256=QqTKk-Z4BylY6EkBSlYvKXVhT2te-m2Al626OUAz-r4,1027
transformers/models/align/__pycache__/__init__.cpython-312.pyc,,
transformers/models/align/__pycache__/configuration_align.cpython-312.pyc,,
transformers/models/align/__pycache__/modeling_align.cpython-312.pyc,,
transformers/models/align/__pycache__/processing_align.cpython-312.pyc,,
transformers/models/align/configuration_align.py,sha256=O8IZG-4Rcc2vwQdfO_9yXtMh69EWabsQpcU0Zy7DrpY,16538
transformers/models/align/modeling_align.py,sha256=nBuKM89UxX3vfBsGky5y2Om_TikGIJ8yhKiUW-AaWC4,71972
transformers/models/align/processing_align.py,sha256=BrpIj1U9nYwxnuOQM9NmsMwuxWJxK-2gyHxqhp-b9Ik,7311
transformers/models/altclip/__init__.py,sha256=405IijUCYr1EGvOqg1xzds_GHOlxCl0HCsf1rI0wtPY,1033
transformers/models/altclip/__pycache__/__init__.cpython-312.pyc,,
transformers/models/altclip/__pycache__/configuration_altclip.cpython-312.pyc,,
transformers/models/altclip/__pycache__/modeling_altclip.cpython-312.pyc,,
transformers/models/altclip/__pycache__/processing_altclip.cpython-312.pyc,,
transformers/models/altclip/configuration_altclip.py,sha256=Gnib9SMogm262A56roRiT7ITaVSHOv5skI_LwHRH0TU,18980
transformers/models/altclip/modeling_altclip.py,sha256=b2hEJSG_wtTq1EBF1XbvB1zVr0JJPkULJqUEgjui_VQ,81014
transformers/models/altclip/processing_altclip.py,sha256=ak5ALJowiGiUcIrgMOIK1OaBYGqneQQuwcxoEL_1qSI,6904
transformers/models/aria/__init__.py,sha256=I3vYPjV-sDl0OAILLADGZ7hUkk9ZsmyZ8CEf9tie_dY,1066
transformers/models/aria/__pycache__/__init__.cpython-312.pyc,,
transformers/models/aria/__pycache__/configuration_aria.cpython-312.pyc,,
transformers/models/aria/__pycache__/image_processing_aria.cpython-312.pyc,,
transformers/models/aria/__pycache__/modeling_aria.cpython-312.pyc,,
transformers/models/aria/__pycache__/modular_aria.cpython-312.pyc,,
transformers/models/aria/__pycache__/processing_aria.cpython-312.pyc,,
transformers/models/aria/configuration_aria.py,sha256=RKS7g4q8HWz350Zidfi5JdjRDeWO13_96ew5JUujID0,16330
transformers/models/aria/image_processing_aria.py,sha256=Ltyy955sInynlbSLrj3pCxzN5tQOUCVc6E9rTY3omPQ,22274
transformers/models/aria/modeling_aria.py,sha256=0acjIKDCe_51_D4D7_UZH04Ez-dGfi-yIzTk6jGSl0A,71526
transformers/models/aria/modular_aria.py,sha256=Ht3OFpE9MWJV87ndBg1DjTteYUwSYdyJtHaMjyV5StU,70414
transformers/models/aria/processing_aria.py,sha256=idIaGfGlfzb2syiDxVWRBw-iiwS9-mT9gfFRWNtVFoc,7954
transformers/models/audio_spectrogram_transformer/__init__.py,sha256=a_YVwB1p4_PPeqPFWqFsGSGSQVTaSUXY0xsOd_Gflqs,1107
transformers/models/audio_spectrogram_transformer/__pycache__/__init__.cpython-312.pyc,,
transformers/models/audio_spectrogram_transformer/__pycache__/configuration_audio_spectrogram_transformer.cpython-312.pyc,,
transformers/models/audio_spectrogram_transformer/__pycache__/feature_extraction_audio_spectrogram_transformer.cpython-312.pyc,,
transformers/models/audio_spectrogram_transformer/__pycache__/modeling_audio_spectrogram_transformer.cpython-312.pyc,,
transformers/models/audio_spectrogram_transformer/configuration_audio_spectrogram_transformer.py,sha256=m7jyBXJRTnsq7WLvGObn16eS-2QEvJ-yYyn4DTMhgis,5907
transformers/models/audio_spectrogram_transformer/feature_extraction_audio_spectrogram_transformer.py,sha256=7azuno1_d7rZRetXaQ5M-gbPdMXmfQ9qQZsrUc4TGKE,9944
transformers/models/audio_spectrogram_transformer/modeling_audio_spectrogram_transformer.py,sha256=94f9FarljO4S0XnRap5nO-yTluHOimHykkuKq3PbWHg,28425
transformers/models/auto/__init__.py,sha256=b70YsNl9fYHKgw7tHRmDnlx9BBUHFrHGC1IPHWjD20s,17026
transformers/models/auto/__pycache__/__init__.cpython-312.pyc,,
transformers/models/auto/__pycache__/auto_factory.cpython-312.pyc,,
transformers/models/auto/__pycache__/configuration_auto.cpython-312.pyc,,
transformers/models/auto/__pycache__/feature_extraction_auto.cpython-312.pyc,,
transformers/models/auto/__pycache__/image_processing_auto.cpython-312.pyc,,
transformers/models/auto/__pycache__/modeling_auto.cpython-312.pyc,,
transformers/models/auto/__pycache__/modeling_flax_auto.cpython-312.pyc,,
transformers/models/auto/__pycache__/modeling_tf_auto.cpython-312.pyc,,
transformers/models/auto/__pycache__/processing_auto.cpython-312.pyc,,
transformers/models/auto/__pycache__/tokenization_auto.cpython-312.pyc,,
transformers/models/auto/auto_factory.py,sha256=zGco54HXmSlaVAut_FKxbu_3FeidbIgTNkGIZZBSLmk,44550
transformers/models/auto/configuration_auto.py,sha256=4QwQxzcSBoY6kWSINmQK58kNKuemGjGdbwfX-jFHiLs,43994
transformers/models/auto/feature_extraction_auto.py,sha256=RS2QMs6iH-IMQDTmGKkGBp-6b70Pwcp9z-X8k7lPiI0,19725
transformers/models/auto/image_processing_auto.py,sha256=KRVmyc0ND9yL-3WWMzuBy27f_LKcM6e4p4YKE0KMLv4,33644
transformers/models/auto/modeling_auto.py,sha256=Vp2GNjZNllpQT-WOXSE9v5nYlfyINtWo-SgI_--gfjQ,78118
transformers/models/auto/modeling_flax_auto.py,sha256=UTBFXa5sZxdO_663zWvhcJGpdt1bhpLCl337eZLB7H0,14568
transformers/models/auto/modeling_tf_auto.py,sha256=hgO8Re6rW2lPm1r9etcjQLmgF2aiIVL5m1aWyyZ5szE,28420
transformers/models/auto/processing_auto.py,sha256=fpzvKwe83VHxvmI4aoHEnEjLRAlgaQMzt7nlR37mP4Y,17883
transformers/models/auto/tokenization_auto.py,sha256=whZLQW1HMevra0j6V-d99Cs3v_kCGYt1l67MZcZMM9w,51760
transformers/models/autoformer/__init__.py,sha256=5xv9eb6R-4PmNJ4v-ogeo7pgobDRokFl4nWqqjnWII0,1691
transformers/models/autoformer/__pycache__/__init__.cpython-312.pyc,,
transformers/models/autoformer/__pycache__/configuration_autoformer.cpython-312.pyc,,
transformers/models/autoformer/__pycache__/modeling_autoformer.cpython-312.pyc,,
transformers/models/autoformer/configuration_autoformer.py,sha256=IqjxujPU316HLjmljrBarAH8rnTsbJrlmrwyR7oTLPU,12165
transformers/models/autoformer/modeling_autoformer.py,sha256=nH0M5AHGv6ft8hcVU2PpGPZOBfh0gGnNM2QZ3oZx0Ms,108745
transformers/models/bamba/__init__.py,sha256=gtebRUrAdiwq-rJmlM5qpbtbGEg-xxA3pjivOHJvaRs,1040
transformers/models/bamba/__pycache__/__init__.cpython-312.pyc,,
transformers/models/bamba/__pycache__/configuration_bamba.cpython-312.pyc,,
transformers/models/bamba/__pycache__/modeling_bamba.cpython-312.pyc,,
transformers/models/bamba/__pycache__/modular_bamba.cpython-312.pyc,,
transformers/models/bamba/configuration_bamba.py,sha256=q2UmQtJL21N4eLJpPOZilkbKWvULgCluFwYGPQJFiYE,9886
transformers/models/bamba/modeling_bamba.py,sha256=oFxkou0__97EshYJ6wZr5ItjBNRE3KmN3Ou3Sv4DDlQ,76672
transformers/models/bamba/modular_bamba.py,sha256=3d6AGhetkK_xGip8EBrmr_rx1BuI8vMI47GT3ugUpC8,60768
transformers/models/bark/__init__.py,sha256=fIlOQ6RPBARVhUKdjNx2Nvf09azEI6AiPv3lyWjk0Gc,1024
transformers/models/bark/__pycache__/__init__.cpython-312.pyc,,
transformers/models/bark/__pycache__/configuration_bark.cpython-312.pyc,,
transformers/models/bark/__pycache__/generation_configuration_bark.cpython-312.pyc,,
transformers/models/bark/__pycache__/modeling_bark.cpython-312.pyc,,
transformers/models/bark/__pycache__/processing_bark.cpython-312.pyc,,
transformers/models/bark/configuration_bark.py,sha256=Wv8Q0u_bFuHAag_jiS07alb9tZ3gf_V_RMOKamDQeAs,11895
transformers/models/bark/generation_configuration_bark.py,sha256=6YiZkHuloUVbfqzR36xmvhKm5SZ2-b2MWMHSwMvRWTA,14947
transformers/models/bark/modeling_bark.py,sha256=xyHcv3_1gHVvOtK56L2Gy9wjlyJrP1cfG4Rc_ftslk0,83558
transformers/models/bark/processing_bark.py,sha256=xeyTkcRTU8-Wgmp79Q3nRbmYL-VeEVsb_CPyT3iYcVU,13340
transformers/models/bart/__init__.py,sha256=1_kCOlvj4hcCbNiAsAhH0PYAK4zopuVKAYKZ_64O3_c,1142
transformers/models/bart/__pycache__/__init__.cpython-312.pyc,,
transformers/models/bart/__pycache__/configuration_bart.cpython-312.pyc,,
transformers/models/bart/__pycache__/modeling_bart.cpython-312.pyc,,
transformers/models/bart/__pycache__/modeling_flax_bart.cpython-312.pyc,,
transformers/models/bart/__pycache__/modeling_tf_bart.cpython-312.pyc,,
transformers/models/bart/__pycache__/tokenization_bart.cpython-312.pyc,,
transformers/models/bart/__pycache__/tokenization_bart_fast.cpython-312.pyc,,
transformers/models/bart/configuration_bart.py,sha256=a7p9WvNZTjkz9OcqS4Yc7tmMiRUyHMHfsrwdp7lwBeo,18828
transformers/models/bart/modeling_bart.py,sha256=3tpjY3GTdHvRaozWdSWYexUlkTJN9NVGSAfQJKcL0iU,102693
transformers/models/bart/modeling_flax_bart.py,sha256=pUatshruENz2rbOZSZPy_axyfb0XwK50AfBVJY4KG4s,82964
transformers/models/bart/modeling_tf_bart.py,sha256=qh1prHYMgGuDiVIVmXPkyo55T8tFP33WtXEvaWbu5CU,80893
transformers/models/bart/tokenization_bart.py,sha256=I46HzuT0apIw2JMFyS5Iy4gMbmLNHqt7wxE_MqVXCmM,16280
transformers/models/bart/tokenization_bart_fast.py,sha256=Yp5AGUVsIuitpZF7Sy-NL5qcyb6eRpD3VraIAXwrMhk,11288
transformers/models/barthez/__init__.py,sha256=21WBGVafx-0kV-K_2jBdpBg0NBWsRKJqJowo03g2S9A,1003
transformers/models/barthez/__pycache__/__init__.cpython-312.pyc,,
transformers/models/barthez/__pycache__/tokenization_barthez.cpython-312.pyc,,
transformers/models/barthez/__pycache__/tokenization_barthez_fast.cpython-312.pyc,,
transformers/models/barthez/tokenization_barthez.py,sha256=rUYGiy59C87gLqA7Nih1oZAuHBNgwwn6DAk5t327h6w,12097
transformers/models/barthez/tokenization_barthez_fast.py,sha256=kJR7nOw1ogVLi9oTbRo9jbX6T1zeSOXIpws9WgJb_Ls,7873
transformers/models/bartpho/__init__.py,sha256=DN0zgU4dM841Kqqo6wN8FpWFeWYHCBxIq3lxrg5vUoU,958
transformers/models/bartpho/__pycache__/__init__.cpython-312.pyc,,
transformers/models/bartpho/__pycache__/tokenization_bartpho.cpython-312.pyc,,
transformers/models/bartpho/tokenization_bartpho.py,sha256=nrzFWM36nFdiK33XQ6hkW2dY9XKRJD2VzsrXXzA7BtM,13556
transformers/models/beit/__init__.py,sha256=1YMzI3uPQsE2JABveFTVSkuHE8DJkB_4RBWtyCkiHUI,1111
transformers/models/beit/__pycache__/__init__.cpython-312.pyc,,
transformers/models/beit/__pycache__/configuration_beit.cpython-312.pyc,,
transformers/models/beit/__pycache__/feature_extraction_beit.cpython-312.pyc,,
transformers/models/beit/__pycache__/image_processing_beit.cpython-312.pyc,,
transformers/models/beit/__pycache__/modeling_beit.cpython-312.pyc,,
transformers/models/beit/__pycache__/modeling_flax_beit.cpython-312.pyc,,
transformers/models/beit/configuration_beit.py,sha256=L9kQi9U7uHwvUieA8VhbjrHCi7PgdPmu1dHypyFy3x8,11593
transformers/models/beit/feature_extraction_beit.py,sha256=_dd_Cd95PPiQQDd5lT9pAybrUhdiqHZPA6I2c54y8S4,1209
transformers/models/beit/image_processing_beit.py,sha256=SVy2cgWTwZMReR8sEmiFs5t_e3os9MS_UAzX6zOraxs,24451
transformers/models/beit/modeling_beit.py,sha256=LW3X18XOac4t2pdmr-aSUPdiq7-mg9jqMuZfKdK8ato,69348
transformers/models/beit/modeling_flax_beit.py,sha256=P2tedtc4boQ-MRDzBBvi3gjbX621Q_x_J1ohU3icUMU,37140
transformers/models/bert/__init__.py,sha256=8IqoRT5cO4DU3GmQHsJgW-n6MclOZTmho5VYkKDMbnU,1182
transformers/models/bert/__pycache__/__init__.cpython-312.pyc,,
transformers/models/bert/__pycache__/configuration_bert.cpython-312.pyc,,
transformers/models/bert/__pycache__/modeling_bert.cpython-312.pyc,,
transformers/models/bert/__pycache__/modeling_flax_bert.cpython-312.pyc,,
transformers/models/bert/__pycache__/modeling_tf_bert.cpython-312.pyc,,
transformers/models/bert/__pycache__/tokenization_bert.cpython-312.pyc,,
transformers/models/bert/__pycache__/tokenization_bert_fast.cpython-312.pyc,,
transformers/models/bert/__pycache__/tokenization_bert_tf.cpython-312.pyc,,
transformers/models/bert/configuration_bert.py,sha256=YoCFuobD5d-xaBesGmhyTrS_2Po5DIdpEBGXEWQZ1nw,7289
transformers/models/bert/modeling_bert.py,sha256=ksimE-U38WAIDu6VUA7G60xGN5UnwPbrFLbMFgUhQ4U,90338
transformers/models/bert/modeling_flax_bert.py,sha256=d7XmXxyiccYVK5Ya-3yWmdYZ9uCNISWRgZVJW-CoU68,64013
transformers/models/bert/modeling_tf_bert.py,sha256=5NgXMUeMaXoAP0Yrb7oA23zsAUJm5uKLc8x5SKfGABc,94661
transformers/models/bert/tokenization_bert.py,sha256=IXUVFnhEH-lSdEpV0wm9regp3K5Ja_H_2pDpPp-NCI8,20915
transformers/models/bert/tokenization_bert_fast.py,sha256=sxlyN7qodKx3BoKxkmrhKnySNtlc04FQM1OcHp7S4sA,7686
transformers/models/bert/tokenization_bert_tf.py,sha256=p6LfPErF6Hm2kVa6OBqNkWkkwuuCFje-WqX4TaVdICw,11927
transformers/models/bert_generation/__init__.py,sha256=sLEyyFf2yI6QflP1lTI9LXUF5PvWBvu-fsaFbjund5I,1059
transformers/models/bert_generation/__pycache__/__init__.cpython-312.pyc,,
transformers/models/bert_generation/__pycache__/configuration_bert_generation.cpython-312.pyc,,
transformers/models/bert_generation/__pycache__/modeling_bert_generation.cpython-312.pyc,,
transformers/models/bert_generation/__pycache__/tokenization_bert_generation.cpython-312.pyc,,
transformers/models/bert_generation/configuration_bert_generation.py,sha256=OknGKh0MkhqzzbPRXJO_-CNMVURP3OnRmUOa5Y0NIFw,6377
transformers/models/bert_generation/modeling_bert_generation.py,sha256=1jXGKfYjjBFgwGdCEZY7VrQcO_MVc2yKNCquRl3SESY,47496
transformers/models/bert_generation/tokenization_bert_generation.py,sha256=JJXKV2CzPIxkKm1WErhm1LF4DA-_UPZ1-g3rLz5L7ak,7116
transformers/models/bert_japanese/__init__.py,sha256=94xfgVPnIQuHQxvmc55_EedJlJQTnHiL4va6Ry6x3LE,964
transformers/models/bert_japanese/__pycache__/__init__.cpython-312.pyc,,
transformers/models/bert_japanese/__pycache__/tokenization_bert_japanese.cpython-312.pyc,,
transformers/models/bert_japanese/tokenization_bert_japanese.py,sha256=3b-_Oyf0_1Fw6N7lJEcZ6w0p6Qse4JqRyIETR4vLqaw,39086
transformers/models/bertweet/__init__.py,sha256=EZegs0rWTTCiOC_eY-M8eV7bCcwU60dB0HsM1S1VDzQ,959
transformers/models/bertweet/__pycache__/__init__.cpython-312.pyc,,
transformers/models/bertweet/__pycache__/tokenization_bertweet.cpython-312.pyc,,
transformers/models/bertweet/tokenization_bertweet.py,sha256=xysk7JjQ_Q1faNzHYNy6kHqYZiR3gMgG1VeknOYTZCk,27020
transformers/models/big_bird/__init__.py,sha256=3rloOuQNKURURWgk5Td4OBQBAzBdTJ2_fM_CI6yPrV0,1126
transformers/models/big_bird/__pycache__/__init__.cpython-312.pyc,,
transformers/models/big_bird/__pycache__/configuration_big_bird.cpython-312.pyc,,
transformers/models/big_bird/__pycache__/modeling_big_bird.cpython-312.pyc,,
transformers/models/big_bird/__pycache__/modeling_flax_big_bird.cpython-312.pyc,,
transformers/models/big_bird/__pycache__/tokenization_big_bird.cpython-312.pyc,,
transformers/models/big_bird/__pycache__/tokenization_big_bird_fast.cpython-312.pyc,,
transformers/models/big_bird/configuration_big_bird.py,sha256=e2xRL0lOIxVdUY3bXVcRJEXBM2bWWIavrx9-BXYKyoc,7883
transformers/models/big_bird/modeling_big_bird.py,sha256=FRyFd0nz9HsESx4j8zlnkQQQ49zQSAy1ghwLduv0OyI,141758
transformers/models/big_bird/modeling_flax_big_bird.py,sha256=9UqJX4gIWm4vYPQTA4ZwMZHyMRQ2EO5y3AeAMUJ2vFk,109837
transformers/models/big_bird/tokenization_big_bird.py,sha256=dcrTNu1L4weScAFig7pY1MlQx8ctDF-RBdaUt2NJ55U,14250
transformers/models/big_bird/tokenization_big_bird_fast.py,sha256=ZXRIpdmUAEcjUZgsC9PBOutjKdrtI23Tg4T_gtMhvkM,10203
transformers/models/bigbird_pegasus/__init__.py,sha256=7zOl1EhO8W2S9jE0FsyEoW8kV6yn5bLA0dspGFM1mLQ,1011
transformers/models/bigbird_pegasus/__pycache__/__init__.cpython-312.pyc,,
transformers/models/bigbird_pegasus/__pycache__/configuration_bigbird_pegasus.cpython-312.pyc,,
transformers/models/bigbird_pegasus/__pycache__/modeling_bigbird_pegasus.cpython-312.pyc,,
transformers/models/bigbird_pegasus/configuration_bigbird_pegasus.py,sha256=7BzPOXq8tnl_F4NpElUo3RRoA8nKFS-V7KKbjzFrf3U,19280
transformers/models/bigbird_pegasus/modeling_bigbird_pegasus.py,sha256=PCzQN-hvJVAIYt95Ej773T1U1baAoCkMXAqWss12_j8,144795
transformers/models/biogpt/__init__.py,sha256=pZxVjmVzt7FXlkMO_5fMg01eyPvvHYXmDA33MKhp6Yk,1032
transformers/models/biogpt/__pycache__/__init__.cpython-312.pyc,,
transformers/models/biogpt/__pycache__/configuration_biogpt.cpython-312.pyc,,
transformers/models/biogpt/__pycache__/modeling_biogpt.cpython-312.pyc,,
transformers/models/biogpt/__pycache__/tokenization_biogpt.cpython-312.pyc,,
transformers/models/biogpt/configuration_biogpt.py,sha256=Kgvu5gVwfYih2d9UWbreENitXZoobGrXobqKq5zYVI0,6207
transformers/models/biogpt/modeling_biogpt.py,sha256=5Ca6X0On4JyO11FDye9ZGrNqIpUv-NzIfJ8YQ7GLnzo,47251
transformers/models/biogpt/tokenization_biogpt.py,sha256=GZvyhnQtqOEJCRk7yac2nu6aGRA8f7fpOXFFgzF4-Xk,13289
transformers/models/bit/__init__.py,sha256=q_q1ZLJq0Jzh1ahQSktKHMESHkXB-DPw4f9Z0YCFfAs,1027
transformers/models/bit/__pycache__/__init__.cpython-312.pyc,,
transformers/models/bit/__pycache__/configuration_bit.cpython-312.pyc,,
transformers/models/bit/__pycache__/image_processing_bit.cpython-312.pyc,,
transformers/models/bit/__pycache__/modeling_bit.cpython-312.pyc,,
transformers/models/bit/configuration_bit.py,sha256=W-9-GGA3bGMTLvOYI9qdqqBt1tUuxEIVw6LhsMcacKI,6295
transformers/models/bit/image_processing_bit.py,sha256=tHE_vmyw1b9xN4k9O36lSWlflYlPT0fuUooZNt00z-c,15824
transformers/models/bit/modeling_bit.py,sha256=wfk-7WfJ0rJS6r-w0-fT_aiKBHrl_snvRvruiw3_wRk,32289
transformers/models/blenderbot/__init__.py,sha256=kdNRND4x54J18VhDVLH6usun5IblSN_9NYaLZfvaysc,1178
transformers/models/blenderbot/__pycache__/__init__.cpython-312.pyc,,
transformers/models/blenderbot/__pycache__/configuration_blenderbot.cpython-312.pyc,,
transformers/models/blenderbot/__pycache__/modeling_blenderbot.cpython-312.pyc,,
transformers/models/blenderbot/__pycache__/modeling_flax_blenderbot.cpython-312.pyc,,
transformers/models/blenderbot/__pycache__/modeling_tf_blenderbot.cpython-312.pyc,,
transformers/models/blenderbot/__pycache__/tokenization_blenderbot.cpython-312.pyc,,
transformers/models/blenderbot/__pycache__/tokenization_blenderbot_fast.cpython-312.pyc,,
transformers/models/blenderbot/configuration_blenderbot.py,sha256=GydvRzNQyXl0A3PjvK-AFwX2WMO0Isf-toZRgxzlrIs,18838
transformers/models/blenderbot/modeling_blenderbot.py,sha256=qcMyfb8bH0YD7DBav41HYbAT61lttvUjLAf1asdZ7e0,74145
transformers/models/blenderbot/modeling_flax_blenderbot.py,sha256=2x9zUXQBozM1Uezutghd6DNO8pOkpRAHoZjWpPUo8bM,65095
transformers/models/blenderbot/modeling_tf_blenderbot.py,sha256=hi6rHin1N8aosd9Vdeg5dfG511vCWDNLN1NJDUBa1Qc,72799
transformers/models/blenderbot/tokenization_blenderbot.py,sha256=B_znhMwieYtuw13YiNMULh-wD6Pxjz7J0FuVNiWV804,18238
transformers/models/blenderbot/tokenization_blenderbot_fast.py,sha256=ggPHz4tIgPQ5lhuWljVgv3vjaCqXioR7wSrL4YFypG4,12461
transformers/models/blenderbot_small/__init__.py,sha256=QsmmBSPdTC43EIyYBwo-xTyJjLLVqm4Cx-KFJ9O2mfE,1214
transformers/models/blenderbot_small/__pycache__/__init__.cpython-312.pyc,,
transformers/models/blenderbot_small/__pycache__/configuration_blenderbot_small.cpython-312.pyc,,
transformers/models/blenderbot_small/__pycache__/modeling_blenderbot_small.cpython-312.pyc,,
transformers/models/blenderbot_small/__pycache__/modeling_flax_blenderbot_small.cpython-312.pyc,,
transformers/models/blenderbot_small/__pycache__/modeling_tf_blenderbot_small.cpython-312.pyc,,
transformers/models/blenderbot_small/__pycache__/tokenization_blenderbot_small.cpython-312.pyc,,
transformers/models/blenderbot_small/__pycache__/tokenization_blenderbot_small_fast.cpython-312.pyc,,
transformers/models/blenderbot_small/configuration_blenderbot_small.py,sha256=H-kA5YzspG0lpH3P3Gcjf3oVQG1VsFlh4jJsIFWwf04,18280
transformers/models/blenderbot_small/modeling_blenderbot_small.py,sha256=9Y6JQo_PwgJWbSQw90trAqYltBX0oug0kxaAk66x6gY,72212
transformers/models/blenderbot_small/modeling_flax_blenderbot_small.py,sha256=iJretreLSzUWtKtjBcl6tuELIPe2xLUHc9jJDBdXnxA,66085
transformers/models/blenderbot_small/modeling_tf_blenderbot_small.py,sha256=gq6dvrnakKHtQ9UaWiOcRy5hWi5INvcLcrlwEUISw9k,71726
transformers/models/blenderbot_small/tokenization_blenderbot_small.py,sha256=IiM31KtIzetAqyZ1wa835RqWUK0hDQpuuequqEvnv_4,7964
transformers/models/blenderbot_small/tokenization_blenderbot_small_fast.py,sha256=yAG-4jJSkeQ_UPpSZhAN-20564BHMVWaNyELOx-HvNc,3367
transformers/models/blip/__init__.py,sha256=2AlWHm-og-Xw5MbbO3apcZJHp8eNXE5MFZ5nkrGST2c,1147
transformers/models/blip/__pycache__/__init__.cpython-312.pyc,,
transformers/models/blip/__pycache__/configuration_blip.cpython-312.pyc,,
transformers/models/blip/__pycache__/image_processing_blip.cpython-312.pyc,,
transformers/models/blip/__pycache__/image_processing_blip_fast.cpython-312.pyc,,
transformers/models/blip/__pycache__/modeling_blip.cpython-312.pyc,,
transformers/models/blip/__pycache__/modeling_blip_text.cpython-312.pyc,,
transformers/models/blip/__pycache__/modeling_tf_blip.cpython-312.pyc,,
transformers/models/blip/__pycache__/modeling_tf_blip_text.cpython-312.pyc,,
transformers/models/blip/__pycache__/processing_blip.cpython-312.pyc,,
transformers/models/blip/configuration_blip.py,sha256=hUGGYwHl_oBTMlU8GiQJTPpuEHewx9fLSKzWdTRz7oI,14894
transformers/models/blip/image_processing_blip.py,sha256=YlkSKSiMjtnMdVJhBSljCRbfn2kad03ku3Zp2Y-NyrU,15269
transformers/models/blip/image_processing_blip_fast.py,sha256=S4JxWsQu--8oYnzGqGXPc29GmRc-dYLBJmvpaiOLpNg,1452
transformers/models/blip/modeling_blip.py,sha256=IBM8R4QDyy87E9XpYmpYWfug6JlqHo46XDOjqxyPYEk,68848
transformers/models/blip/modeling_blip_text.py,sha256=4eNmYG-mZBIZBbIsjKQrCgMzt2u-XuzP1E__MwPKdZ4,44175
transformers/models/blip/modeling_tf_blip.py,sha256=qC3slLAjZ04QzFdU7Jb0ZS0kCVWbB78Doy5_odfuJFg,71531
transformers/models/blip/modeling_tf_blip_text.py,sha256=iJiYcnZpqJhoNrfUcxPxtokT_qMJGgLyz1hAcAWZ-t4,49972
transformers/models/blip/processing_blip.py,sha256=kUE-NeeIu3K2XB349QXD868os29SshunVUFg0_QfuGg,5897
transformers/models/blip_2/__init__.py,sha256=kj_6H0rQ7dLoQk-COIb06LlDRnbORu3GLU3m4EdMkAM,1030
transformers/models/blip_2/__pycache__/__init__.cpython-312.pyc,,
transformers/models/blip_2/__pycache__/configuration_blip_2.cpython-312.pyc,,
transformers/models/blip_2/__pycache__/modeling_blip_2.cpython-312.pyc,,
transformers/models/blip_2/__pycache__/processing_blip_2.cpython-312.pyc,,
transformers/models/blip_2/configuration_blip_2.py,sha256=e3gM_SZBkZpQ9tsKEmD2cI7FiFPXzEzz0_K_K3TS2Js,16012
transformers/models/blip_2/modeling_blip_2.py,sha256=VTG8bzwQ6dv2OJo3_v_dVO_5JHLjlf9uuTzzl92o7Tw,114970
transformers/models/blip_2/processing_blip_2.py,sha256=3NlrZ6tBUOr76PLvYGS-OQGmpIViqPOx9HF6jGfEjnk,8901
transformers/models/bloom/__init__.py,sha256=lcq09Py2vSezUf26aaBG4yp2DpLZ-mAPt-fybvY_C-Q,1073
transformers/models/bloom/__pycache__/__init__.cpython-312.pyc,,
transformers/models/bloom/__pycache__/configuration_bloom.cpython-312.pyc,,
transformers/models/bloom/__pycache__/modeling_bloom.cpython-312.pyc,,
transformers/models/bloom/__pycache__/modeling_flax_bloom.cpython-312.pyc,,
transformers/models/bloom/__pycache__/tokenization_bloom_fast.cpython-312.pyc,,
transformers/models/bloom/configuration_bloom.py,sha256=owJZi9R6fqbCRNhKkQrhLRJaSwoV7hmUzgf6FMtsGIQ,10185
transformers/models/bloom/modeling_bloom.py,sha256=_PnRBbClGjFy-1OWnp9ihrCkOiCL1TuT0B7Zuas2fqU,62778
transformers/models/bloom/modeling_flax_bloom.py,sha256=Qch_LuXgy8nefmlUShUQqmDsEI3L6YlZwmLZYq7zKRg,30175
transformers/models/bloom/tokenization_bloom_fast.py,sha256=csCeZyW8locaAry8KnXq88_TDX9LXiJkDYyXq2vt_C4,6284
transformers/models/bridgetower/__init__.py,sha256=hZiDmqf2UAFUr0EY0b0ldkNXZvNx9YTLW64qTi2tIf8,1093
transformers/models/bridgetower/__pycache__/__init__.cpython-312.pyc,,
transformers/models/bridgetower/__pycache__/configuration_bridgetower.cpython-312.pyc,,
transformers/models/bridgetower/__pycache__/image_processing_bridgetower.cpython-312.pyc,,
transformers/models/bridgetower/__pycache__/modeling_bridgetower.cpython-312.pyc,,
transformers/models/bridgetower/__pycache__/processing_bridgetower.cpython-312.pyc,,
transformers/models/bridgetower/configuration_bridgetower.py,sha256=06gk7a1fWLsQgAC29FRmFypnRGq1dPHZm6cF2lU8hEE,14876
transformers/models/bridgetower/image_processing_bridgetower.py,sha256=u9uq96e0-CLXCEbXiXcR2V92xquNbKm4w1risyPXCcc,26333
transformers/models/bridgetower/modeling_bridgetower.py,sha256=sBQ0s08HA8K6fDFqFnPjoo4ojsFTCX_VAk34VBdp8Go,91575
transformers/models/bridgetower/processing_bridgetower.py,sha256=4vAYym1IHDu91HR_6wfwjhCjgQW0XAHGwnGoO9au5wo,4437
transformers/models/bros/__init__.py,sha256=wT0avJ_J50-WK6jOB-6UbgN5kjHiBwG-NNT_iefMXr8,1024
transformers/models/bros/__pycache__/__init__.cpython-312.pyc,,
transformers/models/bros/__pycache__/configuration_bros.cpython-312.pyc,,
transformers/models/bros/__pycache__/modeling_bros.cpython-312.pyc,,
transformers/models/bros/__pycache__/processing_bros.cpython-312.pyc,,
transformers/models/bros/configuration_bros.py,sha256=9Vgmvk3hZ-VccsOGhB8OlUPjM5ojPufSIBHa2oY4I5I,6418
transformers/models/bros/modeling_bros.py,sha256=ZkjTI0jW2EwEaP5Ifb3QCnvPol4KYk9daaMcOZ0LC60,58014
transformers/models/bros/processing_bros.py,sha256=QnQHmepnVnGsg4lsL681-uU6LOIV1R7gzCUE49_MmKY,4223
transformers/models/byt5/__init__.py,sha256=O7yXvHyqMZ7stkKX67knnddmJ81pPHoKrY_7NCAauU4,955
transformers/models/byt5/__pycache__/__init__.cpython-312.pyc,,
transformers/models/byt5/__pycache__/tokenization_byt5.cpython-312.pyc,,
transformers/models/byt5/tokenization_byt5.py,sha256=y8G5Y-aBmTHkXoEsRcUjtmcatuOqW8ekfXagoyGU9Jg,10059
transformers/models/camembert/__init__.py,sha256=hfxYgJYchvXLwio03yWsATGmrU2hgKOoiw7gaNoVgj8,1129
transformers/models/camembert/__pycache__/__init__.cpython-312.pyc,,
transformers/models/camembert/__pycache__/configuration_camembert.cpython-312.pyc,,
transformers/models/camembert/__pycache__/modeling_camembert.cpython-312.pyc,,
transformers/models/camembert/__pycache__/modeling_tf_camembert.cpython-312.pyc,,
transformers/models/camembert/__pycache__/tokenization_camembert.cpython-312.pyc,,
transformers/models/camembert/__pycache__/tokenization_camembert_fast.cpython-312.pyc,,
transformers/models/camembert/configuration_camembert.py,sha256=pOCkwK5-87daixnYS6NMfjWCcTXGvKDGdUE2rpJlrQ0,7404
transformers/models/camembert/modeling_camembert.py,sha256=zZJgRXFPS0cZk2tJ1FqqK9cl2WOygA8EjURYgYCM5CI,79278
transformers/models/camembert/modeling_tf_camembert.py,sha256=2_KMeWITCbmVZwwPGNEzHtwK5CA3UGZkiE18r5TUWpg,81824
transformers/models/camembert/tokenization_camembert.py,sha256=juhJAgm9-qC_ajVPIQ5EErOmx35Up2sTtacuPFrsFI0,14011
transformers/models/camembert/tokenization_camembert_fast.py,sha256=eYU24_qNnpU4cNGjHe6okOFAR_wDux3CTMNaSDXxybg,8311
transformers/models/canine/__init__.py,sha256=ThkEqO6wPzWCnAplx0EWCUqVaKKsNYQKXQhWfTblEBU,1032
transformers/models/canine/__pycache__/__init__.cpython-312.pyc,,
transformers/models/canine/__pycache__/configuration_canine.cpython-312.pyc,,
transformers/models/canine/__pycache__/modeling_canine.cpython-312.pyc,,
transformers/models/canine/__pycache__/tokenization_canine.cpython-312.pyc,,
transformers/models/canine/configuration_canine.py,sha256=8Rlt-y-lkY4Jwzi4Aa7NXN4TJtDoQylbogUOjt_q9IA,6584
transformers/models/canine/modeling_canine.py,sha256=sW1c8QHN_nTGNGXvnvpecZLisHKZ3KXqyV_Z5kWLz9E,73650
transformers/models/canine/tokenization_canine.py,sha256=pGRq1iGZxZxLfSibFmxa7sHqZlb8YGcS0UgiD3us9qc,9319
transformers/models/chameleon/__init__.py,sha256=5XR1fyLUHtxc-PLFlPnqT7pSsaihK9f4mBOJn-YhjY8,1085
transformers/models/chameleon/__pycache__/__init__.cpython-312.pyc,,
transformers/models/chameleon/__pycache__/configuration_chameleon.cpython-312.pyc,,
transformers/models/chameleon/__pycache__/image_processing_chameleon.cpython-312.pyc,,
transformers/models/chameleon/__pycache__/modeling_chameleon.cpython-312.pyc,,
transformers/models/chameleon/__pycache__/processing_chameleon.cpython-312.pyc,,
transformers/models/chameleon/configuration_chameleon.py,sha256=NYDmrkm3lunyWGuGEY7KkuGkhPRYgOgfCKdpOfECQNs,13293
transformers/models/chameleon/image_processing_chameleon.py,sha256=pwIZkhH1bEwNy9nDv3TCOzpFP-hcOUCIJtlLXrrdgEU,16813
transformers/models/chameleon/modeling_chameleon.py,sha256=NQ2fzSsBwE5MapXTnnvYsYMSq8RN9WVY94uhgPb6Srk,78832
transformers/models/chameleon/processing_chameleon.py,sha256=ooHM30q477U_IM5bqwboISA3UmE6ebue2UBBwbfYHaE,8497
transformers/models/chinese_clip/__init__.py,sha256=HGJ0ZhGZrwe9RNBwbe0R66zbZJ-XwT6swCYjpFSYtOQ,1148
transformers/models/chinese_clip/__pycache__/__init__.cpython-312.pyc,,
transformers/models/chinese_clip/__pycache__/configuration_chinese_clip.cpython-312.pyc,,
transformers/models/chinese_clip/__pycache__/feature_extraction_chinese_clip.cpython-312.pyc,,
transformers/models/chinese_clip/__pycache__/image_processing_chinese_clip.cpython-312.pyc,,
transformers/models/chinese_clip/__pycache__/modeling_chinese_clip.cpython-312.pyc,,
transformers/models/chinese_clip/__pycache__/processing_chinese_clip.cpython-312.pyc,,
transformers/models/chinese_clip/configuration_chinese_clip.py,sha256=CGTZWWAMK93lLOJ3VzhRk8uiN3xUso_BWY8aVqmAyCo,20796
transformers/models/chinese_clip/feature_extraction_chinese_clip.py,sha256=37XWUOby8N6UWwE-t5tMiasS7V2q7cKyc-Jk1YfGqQU,1291
transformers/models/chinese_clip/image_processing_chinese_clip.py,sha256=PJZgsxuxbhH0sXInjngESylhnf-IjRcT_mwKgzvFCxI,15383
transformers/models/chinese_clip/modeling_chinese_clip.py,sha256=juVnpn5avrepmqr311YBdP6NBK4JGDeB5jJd93lhY1A,76467
transformers/models/chinese_clip/processing_chinese_clip.py,sha256=__De516UXM9Mu2Yk2EvIvqVWTzYZ-Jy07psSl3cGDYw,7529
transformers/models/clap/__init__.py,sha256=751udHbsD7FBLGAByjx_8Z4XPLly1MaQQ4wKN_9vbOY,1067
transformers/models/clap/__pycache__/__init__.cpython-312.pyc,,
transformers/models/clap/__pycache__/configuration_clap.cpython-312.pyc,,
transformers/models/clap/__pycache__/feature_extraction_clap.cpython-312.pyc,,
transformers/models/clap/__pycache__/modeling_clap.cpython-312.pyc,,
transformers/models/clap/__pycache__/processing_clap.cpython-312.pyc,,
transformers/models/clap/configuration_clap.py,sha256=cdJWJmdCsuoPDy-adWEkUt0OQB1-9oECdumFJptkpxg,18801
transformers/models/clap/feature_extraction_clap.py,sha256=h6FhovQGYHyXRrQ8GQQpkNBnphtlapawb4Be7A1dG3w,18728
transformers/models/clap/modeling_clap.py,sha256=G21l3dtz78_mUM6gHlXgzKzwfDAvLMJvpb139J0TSfk,105025
transformers/models/clap/processing_clap.py,sha256=YrxLfhmLXQGtSuj97x4TcROlSKJFIx5stdsV2G8xJkw,5708
transformers/models/clip/__init__.py,sha256=bkfM4LH7u_ab8C6cctpvdgySHyQmUaSlWphG4CkcQtg,1307
transformers/models/clip/__pycache__/__init__.cpython-312.pyc,,
transformers/models/clip/__pycache__/configuration_clip.cpython-312.pyc,,
transformers/models/clip/__pycache__/feature_extraction_clip.cpython-312.pyc,,
transformers/models/clip/__pycache__/image_processing_clip.cpython-312.pyc,,
transformers/models/clip/__pycache__/image_processing_clip_fast.cpython-312.pyc,,
transformers/models/clip/__pycache__/modeling_clip.cpython-312.pyc,,
transformers/models/clip/__pycache__/modeling_flax_clip.cpython-312.pyc,,
transformers/models/clip/__pycache__/modeling_tf_clip.cpython-312.pyc,,
transformers/models/clip/__pycache__/processing_clip.cpython-312.pyc,,
transformers/models/clip/__pycache__/tokenization_clip.cpython-312.pyc,,
transformers/models/clip/__pycache__/tokenization_clip_fast.cpython-312.pyc,,
transformers/models/clip/configuration_clip.py,sha256=5Zk61KYs-OBHziSHh3IxVh6NY4GSCrIcmRXWlE85SgU,19353
transformers/models/clip/feature_extraction_clip.py,sha256=GoTgAxYidfx91DYnfLHHBPvx6oUt6LUNXwxlC5wkc8U,1209
transformers/models/clip/image_processing_clip.py,sha256=VGxHSx8ejv2LQnEkAR4xHsZVPyymKYN4arilOnobZzk,16813
transformers/models/clip/image_processing_clip_fast.py,sha256=j5E_9NG-uIZe6VMy4zbYeAlZEL6NktYZH5nrSWT-uHI,1547
transformers/models/clip/modeling_clip.py,sha256=OCrLIIvatF3X6Z1b4JsyXVaW_u36PFEbjHuVYaDAe_k,74256
transformers/models/clip/modeling_flax_clip.py,sha256=oAXX_FiZ4bFKRWBAeaR-oJrKoUC4HEeUatMQFEo81v8,50748
transformers/models/clip/modeling_tf_clip.py,sha256=GyQ_1ruFue6ygbJqXQR5SPKJoz_G86bVmlEnt8Bz7OA,60451
transformers/models/clip/processing_clip.py,sha256=6_9lBf31K-vvgfLuwyJDb713lkmkxJw-MAJCqlFu1bg,7178
transformers/models/clip/tokenization_clip.py,sha256=J1EFe-UhQPD7kgSfbDmjzfp-qaqc_pSr-hF0PsgQWR4,20606
transformers/models/clip/tokenization_clip_fast.py,sha256=gTYUkNU1cHb_rIZo1OT0LVycdzzQ5BZWgvEoS0UJIZM,6780
transformers/models/clipseg/__init__.py,sha256=12Y-b3sRDKM3Hy8-6rK4GUF2a91V1S3nLUF7559AALw,1033
transformers/models/clipseg/__pycache__/__init__.cpython-312.pyc,,
transformers/models/clipseg/__pycache__/configuration_clipseg.cpython-312.pyc,,
transformers/models/clipseg/__pycache__/modeling_clipseg.cpython-312.pyc,,
transformers/models/clipseg/__pycache__/processing_clipseg.cpython-312.pyc,,
transformers/models/clipseg/configuration_clipseg.py,sha256=leEXNq_YElCYMljja9STypU87jVv6plsvDM1seyGoqI,19353
transformers/models/clipseg/modeling_clipseg.py,sha256=BMEHEIkzckeEOJjWIUyu6kFQkYCLOBsR0QCjS2TKSAU,66924
transformers/models/clipseg/processing_clipseg.py,sha256=MHt8JRKlVNDm0f-LQ6OVpjl29BP-OmyYG-VC1SsYurA,7823
transformers/models/clvp/__init__.py,sha256=RRnPofxkr_llgSxCP9tcAhu3xCR7E_m1PkrHv7KLMzo,1104
transformers/models/clvp/__pycache__/__init__.cpython-312.pyc,,
transformers/models/clvp/__pycache__/configuration_clvp.cpython-312.pyc,,
transformers/models/clvp/__pycache__/feature_extraction_clvp.cpython-312.pyc,,
transformers/models/clvp/__pycache__/modeling_clvp.cpython-312.pyc,,
transformers/models/clvp/__pycache__/number_normalizer.cpython-312.pyc,,
transformers/models/clvp/__pycache__/processing_clvp.cpython-312.pyc,,
transformers/models/clvp/__pycache__/tokenization_clvp.cpython-312.pyc,,
transformers/models/clvp/configuration_clvp.py,sha256=EfqfAknfrnwO-zFl1GvVXKRfgInH1K3Sb15-GHwDQT0,20327
transformers/models/clvp/feature_extraction_clvp.py,sha256=vEVLOy3-m2GdU0nogQFKKjOhxnN5t-XWrq165_sLZ4c,10984
transformers/models/clvp/modeling_clvp.py,sha256=8h0O00Po_MUHv2Qf6cvV0JsPkvEqGCGfr7z7np5_34c,91466
transformers/models/clvp/number_normalizer.py,sha256=lW1MjRY8PDAWjWLA-S2Fk-LVWaqkmBVCACmF2765Vps,8856
transformers/models/clvp/processing_clvp.py,sha256=r1KiLMS0NBJiL7v9qV02oMAL-uj2xugGQBGDMRMhYk8,3634
transformers/models/clvp/tokenization_clvp.py,sha256=6cWue08VkLPT4Gi5BNHbMHB8IwZ1-17ApeDLa2QTi74,14830
transformers/models/code_llama/__init__.py,sha256=aZJA9qTifG-RGtJKMzfspfxuQkaBryVva7Ah_uGNMoM,1009
transformers/models/code_llama/__pycache__/__init__.cpython-312.pyc,,
transformers/models/code_llama/__pycache__/tokenization_code_llama.cpython-312.pyc,,
transformers/models/code_llama/__pycache__/tokenization_code_llama_fast.cpython-312.pyc,,
transformers/models/code_llama/tokenization_code_llama.py,sha256=O0UlJtd4021xMUo-dZ4PsXkjCKDeoddW_r5RKvhARa8,19251
transformers/models/code_llama/tokenization_code_llama_fast.py,sha256=AyExiCYeWK2A3stxBwN-fWltMH5V3auxCRN-Ha9MFmo,16054
transformers/models/codegen/__init__.py,sha256=NeUIbS8szfu5R9-7CX_G6730RHOODzTfmrapJH2ApMk,1080
transformers/models/codegen/__pycache__/__init__.cpython-312.pyc,,
transformers/models/codegen/__pycache__/configuration_codegen.cpython-312.pyc,,
transformers/models/codegen/__pycache__/modeling_codegen.cpython-312.pyc,,
transformers/models/codegen/__pycache__/tokenization_codegen.cpython-312.pyc,,
transformers/models/codegen/__pycache__/tokenization_codegen_fast.cpython-312.pyc,,
transformers/models/codegen/configuration_codegen.py,sha256=ntWOJE0XkxKxFY2eR95JCN5QlO-xVXbBksmLxx-FKiw,9543
transformers/models/codegen/modeling_codegen.py,sha256=RTyX2NuzAWT913tm0miv6qC1yO-QA7qD6dbY5hvLi0k,36693
transformers/models/codegen/tokenization_codegen.py,sha256=5w0lSfqPVuixyH3Z71hvToC2ueFb0MPloGW_nVl1QMM,16563
transformers/models/codegen/tokenization_codegen_fast.py,sha256=mthH0R-VI8IohKpVhHZNarxpoPnWKUwjY5HO4efWJ4o,10966
transformers/models/cohere/__init__.py,sha256=1Tg-6WGc5wgGduSR__N-jGZvPje9kNs92DW78vN0Auo,1037
transformers/models/cohere/__pycache__/__init__.cpython-312.pyc,,
transformers/models/cohere/__pycache__/configuration_cohere.cpython-312.pyc,,
transformers/models/cohere/__pycache__/modeling_cohere.cpython-312.pyc,,
transformers/models/cohere/__pycache__/modular_cohere.cpython-312.pyc,,
transformers/models/cohere/__pycache__/tokenization_cohere_fast.cpython-312.pyc,,
transformers/models/cohere/configuration_cohere.py,sha256=ny1pfoKmo0f0ptUg2etplGkfb27vQo4DjsVS4KQnEOQ,11156
transformers/models/cohere/modeling_cohere.py,sha256=SLSWDL8-fiV-yUj4J5Bzmr83HBfWNmYOnUN3hq-ZW58,42637
transformers/models/cohere/modular_cohere.py,sha256=O-s9mNuLqtzIOMZe4K6fMtTC2s_7ZNoU4Z0MlbCJdOM,18140
transformers/models/cohere/tokenization_cohere_fast.py,sha256=MZ6rH4mYvCcyjq-JkP9weuqMOiNn4t_xcdX6fkdwLzE,28901
transformers/models/cohere2/__init__.py,sha256=6Cx_c-uTSNopbO3NLWCgMmEB2-5hzkrunUWmMrb8YSU,1011
transformers/models/cohere2/__pycache__/__init__.cpython-312.pyc,,
transformers/models/cohere2/__pycache__/configuration_cohere2.cpython-312.pyc,,
transformers/models/cohere2/__pycache__/modeling_cohere2.cpython-312.pyc,,
transformers/models/cohere2/__pycache__/modular_cohere2.cpython-312.pyc,,
transformers/models/cohere2/configuration_cohere2.py,sha256=R-ntIkm6oIWH7e8Y7J4Tnx_ffwF9rvA-cjgoMU-qsFo,12211
transformers/models/cohere2/modeling_cohere2.py,sha256=sKffenx0qkW4FiLgUvqVFgYYivF_pphTZLODhvy06X4,47985
transformers/models/cohere2/modular_cohere2.py,sha256=SvGrCXbxfbAt7zKti6XUUEE7mlTaMZhzfPVuznBSmiI,31969
transformers/models/colpali/__init__.py,sha256=eG-nOojo-DPkgZJACn6hbJqqfnGE97uKmLkpWVin66A,1033
transformers/models/colpali/__pycache__/__init__.cpython-312.pyc,,
transformers/models/colpali/__pycache__/configuration_colpali.cpython-312.pyc,,
transformers/models/colpali/__pycache__/modeling_colpali.cpython-312.pyc,,
transformers/models/colpali/__pycache__/modular_colpali.cpython-312.pyc,,
transformers/models/colpali/__pycache__/processing_colpali.cpython-312.pyc,,
transformers/models/colpali/configuration_colpali.py,sha256=Opz4MSjq2v5n81qocw4zEdWsHFQky5zZAXbUu9g0ES8,4517
transformers/models/colpali/modeling_colpali.py,sha256=cLFKQ3Vyj_PSUZx1bAfogHnXJ63DmDR-StMzhDKJd3s,13501
transformers/models/colpali/modular_colpali.py,sha256=txqhaypCKbH9TPCQXwxomCi4raJ_JZ4R0kNpW9f9O0k,15893
transformers/models/colpali/processing_colpali.py,sha256=7WZpvPHafQAv79axCfzU_PO9EzbCQbf3Sj0fSUVzHY4,19684
transformers/models/conditional_detr/__init__.py,sha256=hJC-1k8x4oSJK6HtMlMOIubpqnRRg1LCUsrwW9ta61g,1121
transformers/models/conditional_detr/__pycache__/__init__.cpython-312.pyc,,
transformers/models/conditional_detr/__pycache__/configuration_conditional_detr.cpython-312.pyc,,
transformers/models/conditional_detr/__pycache__/feature_extraction_conditional_detr.cpython-312.pyc,,
transformers/models/conditional_detr/__pycache__/image_processing_conditional_detr.cpython-312.pyc,,
transformers/models/conditional_detr/__pycache__/modeling_conditional_detr.cpython-312.pyc,,
transformers/models/conditional_detr/configuration_conditional_detr.py,sha256=s9uAdVKzNPTdXxVGaqAQ-MXDHKJ2-uaMI_KFqhyIIVc,13499
transformers/models/conditional_detr/feature_extraction_conditional_detr.py,sha256=K0Zp0cIuPo_oL56ltJ22I7K8EMeM9TQIkYG189WeP4c,1601
transformers/models/conditional_detr/image_processing_conditional_detr.py,sha256=KI0bg7sZxlUpXteUpgeBflQkY4ffEiXsXsTpt1TqEnI,85712
transformers/models/conditional_detr/modeling_conditional_detr.py,sha256=ep-zwWz-t3RUPDj2DbXjBsYUL2U4n9ToHS2_GlVAQxc,104118
transformers/models/convbert/__init__.py,sha256=x1Rv5-rurTKFifp3w8N_CNcZ3sHvuFwqpw_Zn1BAenw,1124
transformers/models/convbert/__pycache__/__init__.cpython-312.pyc,,
transformers/models/convbert/__pycache__/configuration_convbert.cpython-312.pyc,,
transformers/models/convbert/__pycache__/modeling_convbert.cpython-312.pyc,,
transformers/models/convbert/__pycache__/modeling_tf_convbert.cpython-312.pyc,,
transformers/models/convbert/__pycache__/tokenization_convbert.cpython-312.pyc,,
transformers/models/convbert/__pycache__/tokenization_convbert_fast.cpython-312.pyc,,
transformers/models/convbert/configuration_convbert.py,sha256=hsAfVzzvceAyeDelaOZWOIF1yZqEWfVD_KzVXAXplWA,6886
transformers/models/convbert/modeling_convbert.py,sha256=sHzeIBomOQzgR9CWllf3S54YiiJnz6hom5c6wWvqNJc,58621
transformers/models/convbert/modeling_tf_convbert.py,sha256=8eSAaqs2q7nzaibq7ArjnCNhijbmADBlZ9ptUneNzqE,61643
transformers/models/convbert/tokenization_convbert.py,sha256=oFj7oW5yiGmxrIyA3T3ll4-eidb_mADIrTFZTCfWGBU,21323
transformers/models/convbert/tokenization_convbert_fast.py,sha256=z38ic66_ArTz-TRaBtp0UZbz9freg8d9bytqgGZBHuQ,7819
transformers/models/convnext/__init__.py,sha256=QAUm2k3PH0pqhHzPXIhkEmqzMWKYCs4bo0gNVnH_bBw,1179
transformers/models/convnext/__pycache__/__init__.cpython-312.pyc,,
transformers/models/convnext/__pycache__/configuration_convnext.cpython-312.pyc,,
transformers/models/convnext/__pycache__/feature_extraction_convnext.cpython-312.pyc,,
transformers/models/convnext/__pycache__/image_processing_convnext.cpython-312.pyc,,
transformers/models/convnext/__pycache__/image_processing_convnext_fast.cpython-312.pyc,,
transformers/models/convnext/__pycache__/modeling_convnext.cpython-312.pyc,,
transformers/models/convnext/__pycache__/modeling_tf_convnext.cpython-312.pyc,,
transformers/models/convnext/configuration_convnext.py,sha256=L57JCbIzUNBC8XbTMEmnlrcd7Y64lIK45X84cLRncYs,6183
transformers/models/convnext/feature_extraction_convnext.py,sha256=7C9es8Qn6F_Vx95pnKP7nCwNQeLqd9dxILAfV9pLA0g,1241
transformers/models/convnext/image_processing_convnext.py,sha256=mimUMQR-4zVpU22HO6CHv2UZ73_LeBtH-XaQphydX-8,15866
transformers/models/convnext/image_processing_convnext_fast.py,sha256=6QsShPgkeDIekM7v_mzP_bQ6A5_uYva3q9QI_Icb0IY,7891
transformers/models/convnext/modeling_convnext.py,sha256=Bspvf_MN5Ws9nx2BTgzdnTFdZ7dZ1J1xMMXArTlV0b8,21934
transformers/models/convnext/modeling_tf_convnext.py,sha256=kPoHoYN2P6O6aOOBXCz027B4k-7AakvdEBvA_84wfdQ,27290
transformers/models/convnextv2/__init__.py,sha256=kOl9JbYIk9ioImF_hd0BS_mGDC8SG2k5LvO0-7WroRo,1043
transformers/models/convnextv2/__pycache__/__init__.cpython-312.pyc,,
transformers/models/convnextv2/__pycache__/configuration_convnextv2.cpython-312.pyc,,
transformers/models/convnextv2/__pycache__/modeling_convnextv2.cpython-312.pyc,,
transformers/models/convnextv2/__pycache__/modeling_tf_convnextv2.cpython-312.pyc,,
transformers/models/convnextv2/configuration_convnextv2.py,sha256=wHvC-d6TiQR2v1D5bdRi7sNoRTN5IURCCcek64yVvIc,5564
transformers/models/convnextv2/modeling_convnextv2.py,sha256=n-JJKLyvQoC6mX0sCvCRFFTYv4ijVFNZVHXzpGEER-8,23714
transformers/models/convnextv2/modeling_tf_convnextv2.py,sha256=tP0OnIL0hrRCOLoxgP9QpdlcVKiBcNsGI0a8e7Sd3bI,27708
transformers/models/cpm/__init__.py,sha256=5Oz79wRruzXHciBLUAOGeo6PIH70Vs4ta8ffsMyT1Yg,995
transformers/models/cpm/__pycache__/__init__.cpython-312.pyc,,
transformers/models/cpm/__pycache__/tokenization_cpm.cpython-312.pyc,,
transformers/models/cpm/__pycache__/tokenization_cpm_fast.cpython-312.pyc,,
transformers/models/cpm/tokenization_cpm.py,sha256=IeKGjhlQ9EHDJGIVQDSg_c6Y4G4RKP2xqt_4Mnyl9c4,15056
transformers/models/cpm/tokenization_cpm_fast.py,sha256=aqUUhXsYaqB7yglDU7NaaJp0HqvbdoSU9Uacp-Ye-dc,10459
transformers/models/cpmant/__init__.py,sha256=RfkbbhNqdbioJ5XVaTtxBLnZRt1GFnXugS3UFXHYV0c,1032
transformers/models/cpmant/__pycache__/__init__.cpython-312.pyc,,
transformers/models/cpmant/__pycache__/configuration_cpmant.cpython-312.pyc,,
transformers/models/cpmant/__pycache__/modeling_cpmant.cpython-312.pyc,,
transformers/models/cpmant/__pycache__/tokenization_cpmant.cpython-312.pyc,,
transformers/models/cpmant/configuration_cpmant.py,sha256=RvgmQH8lQazRopzpfK5-Hf4eePtXXfvMJ3ar1VQC2vE,5145
transformers/models/cpmant/modeling_cpmant.py,sha256=CbnELAjgIeS773YJDYlZW-RZJgbZi8o-bnIaB7ALMPQ,37112
transformers/models/cpmant/tokenization_cpmant.py,sha256=3q3TlMkAMfVook26V--_0QO1IYjvEeVWzDEbNNxQDqw,9736
transformers/models/ctrl/__init__.py,sha256=bVtGijL4n9ewNyhcJt7lpsRhXU8yo4nY0xIlRbpismk,1062
transformers/models/ctrl/__pycache__/__init__.cpython-312.pyc,,
transformers/models/ctrl/__pycache__/configuration_ctrl.cpython-312.pyc,,
transformers/models/ctrl/__pycache__/modeling_ctrl.cpython-312.pyc,,
transformers/models/ctrl/__pycache__/modeling_tf_ctrl.cpython-312.pyc,,
transformers/models/ctrl/__pycache__/tokenization_ctrl.cpython-312.pyc,,
transformers/models/ctrl/configuration_ctrl.py,sha256=Vg6ZFqal5MCr-t2K5pp5mtN2TJSeojgKL8IgbZkd81k,4684
transformers/models/ctrl/modeling_ctrl.py,sha256=CBzf_GIr6-7L-X3JU8oj9xT38TPvLYzV-RXMQJjYgkY,35746
transformers/models/ctrl/modeling_tf_ctrl.py,sha256=faymrUQNyYJKHnmCH6Bz06qVldlPfhk96XK36rMGfcE,39457
transformers/models/ctrl/tokenization_ctrl.py,sha256=4kWB5UEE197eRrbKqqnFkmPweDb1QiF_WY3z2Vl4y3s,8087
transformers/models/cvt/__init__.py,sha256=i1847SsjrXEIbrXsDEAiUlrtgLZRHtCSVG0rvCPXE9I,1022
transformers/models/cvt/__pycache__/__init__.cpython-312.pyc,,
transformers/models/cvt/__pycache__/configuration_cvt.cpython-312.pyc,,
transformers/models/cvt/__pycache__/modeling_cvt.cpython-312.pyc,,
transformers/models/cvt/__pycache__/modeling_tf_cvt.cpython-312.pyc,,
transformers/models/cvt/configuration_cvt.py,sha256=OdBupwTQpaCO1R-0anjvmPWvEjo1R7fl3lhNlrKJMz0,6684
transformers/models/cvt/modeling_cvt.py,sha256=lSUZ-JsNP-1HauKLW_m_lZuLS4xB4TkGB_s9C6zCuOA,28780
transformers/models/cvt/modeling_tf_cvt.py,sha256=ce4UP4iJ0AHH_Lih8J-uYn8uvUB_yHDQx8GiwEn8-ms,43545
transformers/models/dab_detr/__init__.py,sha256=ZvNYPQyXWplaRQIxFR8CURcsnu_HRPXrwojF5nTmGd4,998
transformers/models/dab_detr/__pycache__/__init__.cpython-312.pyc,,
transformers/models/dab_detr/__pycache__/configuration_dab_detr.cpython-312.pyc,,
transformers/models/dab_detr/__pycache__/modeling_dab_detr.cpython-312.pyc,,
transformers/models/dab_detr/configuration_dab_detr.py,sha256=gZIUyFghJqquJnfH9p8zJIJ9Az23iPOl21hy5hRHk18,13541
transformers/models/dab_detr/modeling_dab_detr.py,sha256=F3qbnPusUz5CBbR2MJtZvaR69Mk1mXIKCCuxzXIYqxw,83899
transformers/models/dac/__init__.py,sha256=UpwXPmSOQOwvbIvklM21-y5HKY7MEIInmTt65xMX6Hw,1029
transformers/models/dac/__pycache__/__init__.cpython-312.pyc,,
transformers/models/dac/__pycache__/configuration_dac.cpython-312.pyc,,
transformers/models/dac/__pycache__/feature_extraction_dac.cpython-312.pyc,,
transformers/models/dac/__pycache__/modeling_dac.cpython-312.pyc,,
transformers/models/dac/configuration_dac.py,sha256=B-m2cUJBQe3AvbMH4hWxLpY2HdLIXrxXpJb6CEvA7XA,4581
transformers/models/dac/feature_extraction_dac.py,sha256=_2tp_1K2ZGX4gdYtP-LwIwgHUQxD1XnwTSs1OjrMxVU,7947
transformers/models/dac/modeling_dac.py,sha256=nTu7ghFPAQFASaJMlSYWGfDLBAgwfJ_tuRYu9W03tiU,30313
transformers/models/data2vec/__init__.py,sha256=-2iFF1Rb8eF9cccBNLA29zgeFV1ADYaSLoQgf6K6KB8,1238
transformers/models/data2vec/__pycache__/__init__.cpython-312.pyc,,
transformers/models/data2vec/__pycache__/configuration_data2vec_audio.cpython-312.pyc,,
transformers/models/data2vec/__pycache__/configuration_data2vec_text.cpython-312.pyc,,
transformers/models/data2vec/__pycache__/configuration_data2vec_vision.cpython-312.pyc,,
transformers/models/data2vec/__pycache__/modeling_data2vec_audio.cpython-312.pyc,,
transformers/models/data2vec/__pycache__/modeling_data2vec_text.cpython-312.pyc,,
transformers/models/data2vec/__pycache__/modeling_data2vec_vision.cpython-312.pyc,,
transformers/models/data2vec/__pycache__/modeling_tf_data2vec_vision.cpython-312.pyc,,
transformers/models/data2vec/configuration_data2vec_audio.py,sha256=S2S_uz3AgSPJLOSXEOoP6a_GxDPzMGLGRMGENJPdK5Q,16357
transformers/models/data2vec/configuration_data2vec_text.py,sha256=Ylj-Vb1EoeJ2_N7UhYl5nY1ynCFDUwT6u0HjFzaHP-o,7336
transformers/models/data2vec/configuration_data2vec_vision.py,sha256=knPaO-3WZ78J6m6oM-IzP2UIIpd1OxcMZ_-qmuZwTHI,9305
transformers/models/data2vec/modeling_data2vec_audio.py,sha256=9FNetgqH89wIgtxU3t2_qxfiymkSGxozqdVgsU5ELNo,79045
transformers/models/data2vec/modeling_data2vec_text.py,sha256=0wcm4NJo4shIZXtGbkuYIr3nnqml3yNXsibHx4u0CLY,70465
transformers/models/data2vec/modeling_data2vec_vision.py,sha256=_16GMTnlQcFebMRTUK3vZ6coZjoND2uGVkIKmy6_9nk,63268
transformers/models/data2vec/modeling_tf_data2vec_vision.py,sha256=oPTndzxpOq1tgaFq7hn5ItUFotXv8XVdLGYrhlBY-i4,73525
transformers/models/dbrx/__init__.py,sha256=Kzn3gm0QHW9RKEmog_IfdCGam5TXSCzkOs_WHC43sgM,989
transformers/models/dbrx/__pycache__/__init__.cpython-312.pyc,,
transformers/models/dbrx/__pycache__/configuration_dbrx.cpython-312.pyc,,
transformers/models/dbrx/__pycache__/modeling_dbrx.cpython-312.pyc,,
transformers/models/dbrx/configuration_dbrx.py,sha256=IUcNdexw8yuxp4_bKj3j7n1-V-ZlcxneNAFCbhRj4wA,9918
transformers/models/dbrx/modeling_dbrx.py,sha256=pZCeStM8KnRgyU1JGuEp-gXhWySa5c0wBeFaZ8bEcN8,63174
transformers/models/deberta/__init__.py,sha256=diL764eL8gu80XkBDQU9nI6Zy39ArO0d85MtcZ4_NPw,1119
transformers/models/deberta/__pycache__/__init__.cpython-312.pyc,,
transformers/models/deberta/__pycache__/configuration_deberta.cpython-312.pyc,,
transformers/models/deberta/__pycache__/modeling_deberta.cpython-312.pyc,,
transformers/models/deberta/__pycache__/modeling_tf_deberta.cpython-312.pyc,,
transformers/models/deberta/__pycache__/tokenization_deberta.cpython-312.pyc,,
transformers/models/deberta/__pycache__/tokenization_deberta_fast.cpython-312.pyc,,
transformers/models/deberta/configuration_deberta.py,sha256=dld6vou_Gs_Ap_AW4YlpY7RHx_NneY-TEtwTkBf5KaU,8997
transformers/models/deberta/modeling_deberta.py,sha256=RViL2_2CtEhDUIVM41NWM6XZzVBzWHCF1EZVziyRSS4,55930
transformers/models/deberta/modeling_tf_deberta.py,sha256=fgNzpzCP-eXy2qpf8Sn1qRdVJEccnl4zxJxOem652IM,69243
transformers/models/deberta/tokenization_deberta.py,sha256=V0VwuI-q1GH-1f1HhiuC_KmCcR-HQdwRibcmsPcvXcw,17084
transformers/models/deberta/tokenization_deberta_fast.py,sha256=18QhuVxccQUZbP5-D51-hAcNRGuiIiTXOi5LxC4GGv8,10254
transformers/models/deberta_v2/__init__.py,sha256=N6wcSGakSmmHDW_QelFsn58zuDFTuvbctgkyC0OfQ5Y,1134
transformers/models/deberta_v2/__pycache__/__init__.cpython-312.pyc,,
transformers/models/deberta_v2/__pycache__/configuration_deberta_v2.cpython-312.pyc,,
transformers/models/deberta_v2/__pycache__/modeling_deberta_v2.cpython-312.pyc,,
transformers/models/deberta_v2/__pycache__/modeling_tf_deberta_v2.cpython-312.pyc,,
transformers/models/deberta_v2/__pycache__/tokenization_deberta_v2.cpython-312.pyc,,
transformers/models/deberta_v2/__pycache__/tokenization_deberta_v2_fast.cpython-312.pyc,,
transformers/models/deberta_v2/configuration_deberta_v2.py,sha256=t2xyEazeOSgC_3wzv1PHeumpMWrX9bx6pzyQOdFdVQc,8937
transformers/models/deberta_v2/modeling_deberta_v2.py,sha256=IZLUB7xudu7orfCGY5mUFvR8Vli87e_-5xUVBLAEF0I,64104
transformers/models/deberta_v2/modeling_tf_deberta_v2.py,sha256=2ApxmWBRP2jY7A2FLny8Jv_88blDpQfPUK7Y-6P8j14,81668
transformers/models/deberta_v2/tokenization_deberta_v2.py,sha256=ULTMnycvccuaijJoh0i87-TiYUonQc23SgewVtlp1ns,20737
transformers/models/deberta_v2/tokenization_deberta_v2_fast.py,sha256=fKuOjE2SxFSvXwNe1m9oLO_BblM3v4c3r4gzIbywteU,9797
transformers/models/decision_transformer/__init__.py,sha256=8XAHnFrFv8IFz495cQLTeaAk2G1AVRT7roauVHCGoJs,1021
transformers/models/decision_transformer/__pycache__/__init__.cpython-312.pyc,,
transformers/models/decision_transformer/__pycache__/configuration_decision_transformer.cpython-312.pyc,,
transformers/models/decision_transformer/__pycache__/modeling_decision_transformer.cpython-312.pyc,,
transformers/models/decision_transformer/configuration_decision_transformer.py,sha256=2Dsh1_qQB9oZ_cSs6W-MGfiBFRyaNzgXVXvHiLE3f1Q,7028
transformers/models/decision_transformer/modeling_decision_transformer.py,sha256=rzwqcU8nbVqJPFmbIrBrYW_A79bvajc83Wc442231Jk,44414
transformers/models/deformable_detr/__init__.py,sha256=_ae-sABBY17hOT28SN_d0GLeRVjya0W4aqniH8u8Bcw,1176
transformers/models/deformable_detr/__pycache__/__init__.cpython-312.pyc,,
transformers/models/deformable_detr/__pycache__/configuration_deformable_detr.cpython-312.pyc,,
transformers/models/deformable_detr/__pycache__/feature_extraction_deformable_detr.cpython-312.pyc,,
transformers/models/deformable_detr/__pycache__/image_processing_deformable_detr.cpython-312.pyc,,
transformers/models/deformable_detr/__pycache__/image_processing_deformable_detr_fast.cpython-312.pyc,,
transformers/models/deformable_detr/__pycache__/load_custom.cpython-312.pyc,,
transformers/models/deformable_detr/__pycache__/modeling_deformable_detr.cpython-312.pyc,,
transformers/models/deformable_detr/__pycache__/modular_deformable_detr.cpython-312.pyc,,
transformers/models/deformable_detr/configuration_deformable_detr.py,sha256=cNBfcSAQgMas4J6kIaAVm3WnWCdJNIhr9O7ZXomQ6mQ,14571
transformers/models/deformable_detr/feature_extraction_deformable_detr.py,sha256=G398FdbrUYFlnOCmSF1yie1yKOvz0tlCjxxLblqvRqc,1593
transformers/models/deformable_detr/image_processing_deformable_detr.py,sha256=fWhqUT9MkNswP97gEqwkmM2XLLz9oHYWJKX8rzYwx6Y,73170
transformers/models/deformable_detr/image_processing_deformable_detr_fast.py,sha256=YOjYI7t5cnOxS7AEhSCV0DOs2tCnZbfwUGWZaFEfrKQ,38370
transformers/models/deformable_detr/load_custom.py,sha256=GvDeH883HST8-vH5Xl5jcR9VS_e0GSzbDoImSLug9rA,1559
transformers/models/deformable_detr/modeling_deformable_detr.py,sha256=8EOGUKghEgdtwndXv7WM_rGnqA1ukIrP__VS6x6xUBs,100763
transformers/models/deformable_detr/modular_deformable_detr.py,sha256=wMvZzwSeNzGezxYzLPYWVy3mQQB0tymXaryEl060I1U,6586
transformers/models/deit/__init__.py,sha256=8S1h-sIvhRy1EiQ7DKXHqqNEgR0_juhrAyQZ2AU1rVw,1155
transformers/models/deit/__pycache__/__init__.cpython-312.pyc,,
transformers/models/deit/__pycache__/configuration_deit.cpython-312.pyc,,
transformers/models/deit/__pycache__/feature_extraction_deit.cpython-312.pyc,,
transformers/models/deit/__pycache__/image_processing_deit.cpython-312.pyc,,
transformers/models/deit/__pycache__/image_processing_deit_fast.cpython-312.pyc,,
transformers/models/deit/__pycache__/modeling_deit.cpython-312.pyc,,
transformers/models/deit/__pycache__/modeling_tf_deit.cpython-312.pyc,,
transformers/models/deit/configuration_deit.py,sha256=jiP2Ah3GbSLrtdmdaAXcv_o2akAd4LFZgRsuXddGJQA,5740
transformers/models/deit/feature_extraction_deit.py,sha256=D6sKbJwtaJphyzpoXiDbbU3eanJeA8WbuPkvOZdTySg,1209
transformers/models/deit/image_processing_deit.py,sha256=0eqlPgfKXZqZUCIwlSNuHGTsdAZVoiI5dmHnlESp2hQ,15179
transformers/models/deit/image_processing_deit_fast.py,sha256=XM4Hp9fCbvEzNZpOjlsNs6J9FsO-EtrGK6XVipaO4fg,1539
transformers/models/deit/modeling_deit.py,sha256=sZ8LsDmD1W_gozwPiTGYJbSmZKdlCbeUZkPcGv-uD5g,43386
transformers/models/deit/modeling_tf_deit.py,sha256=KF8t1-VfvdN2qZsz5dV0R5_KPa1P-ZVzK434rqrOi9o,51752
transformers/models/deprecated/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
transformers/models/deprecated/__pycache__/__init__.cpython-312.pyc,,
transformers/models/deprecated/bort/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
transformers/models/deprecated/bort/__pycache__/__init__.cpython-312.pyc,,
transformers/models/deprecated/deta/__init__.py,sha256=sRdhN6pSfT1G8VY04s6jNnZBKgyZrB4DsrBsAPs8Rw8,2038
transformers/models/deprecated/deta/__pycache__/__init__.cpython-312.pyc,,
transformers/models/deprecated/deta/__pycache__/configuration_deta.cpython-312.pyc,,
transformers/models/deprecated/deta/__pycache__/image_processing_deta.cpython-312.pyc,,
transformers/models/deprecated/deta/__pycache__/modeling_deta.cpython-312.pyc,,
transformers/models/deprecated/deta/configuration_deta.py,sha256=GTfTPOaP2JzdNKQa9zg3CQ52QPuQOkoxU9VLc-uZt2s,13948
transformers/models/deprecated/deta/image_processing_deta.py,sha256=0_ZAQjb4G-5XUFsXmDW61zf-TOg5-xUqmxnPb8R8WGw,54891
transformers/models/deprecated/deta/modeling_deta.py,sha256=6Rd6LUDmEB5N1T-KnIL6fuIj5f0MpqH8fuUjK47V3VI,135663
transformers/models/deprecated/efficientformer/__init__.py,sha256=u4KA4byDgoRkQ9uuNGgkA2PtpIh0BpZVTObA0Vgil-E,3188
transformers/models/deprecated/efficientformer/__pycache__/__init__.cpython-312.pyc,,
transformers/models/deprecated/efficientformer/__pycache__/configuration_efficientformer.cpython-312.pyc,,
transformers/models/deprecated/efficientformer/__pycache__/image_processing_efficientformer.cpython-312.pyc,,
transformers/models/deprecated/efficientformer/__pycache__/modeling_efficientformer.cpython-312.pyc,,
transformers/models/deprecated/efficientformer/__pycache__/modeling_tf_efficientformer.cpython-312.pyc,,
transformers/models/deprecated/efficientformer/configuration_efficientformer.py,sha256=QFiBTmFQU6P8VllghZ5jQpR1Dthnm9uylTgf7z3uHMc,7719
transformers/models/deprecated/efficientformer/image_processing_efficientformer.py,sha256=Pd7PjkkXF4uQk0fJBku18OKRi7hMoYsPmf3uXhcOc3M,15698
transformers/models/deprecated/efficientformer/modeling_efficientformer.py,sha256=029SXmbp69FUn13m9OdoTHivIAe-x-RkFUtk62yYLWk,33580
transformers/models/deprecated/efficientformer/modeling_tf_efficientformer.py,sha256=tRs9Ljxf8bf1B-6MwpUPuUinzWik24kzQH50Ke58cjw,49194
transformers/models/deprecated/ernie_m/__init__.py,sha256=V6C21iE8AKYSpDNW4Ffn3IGiKp69T4ro2LFcRXc0mq4,2458
transformers/models/deprecated/ernie_m/__pycache__/__init__.cpython-312.pyc,,
transformers/models/deprecated/ernie_m/__pycache__/configuration_ernie_m.cpython-312.pyc,,
transformers/models/deprecated/ernie_m/__pycache__/modeling_ernie_m.cpython-312.pyc,,
transformers/models/deprecated/ernie_m/__pycache__/tokenization_ernie_m.cpython-312.pyc,,
transformers/models/deprecated/ernie_m/configuration_ernie_m.py,sha256=bGRUXTL8NdJEevZNBmDBh_aB_RwRNL8G1rfdNYMW69s,5885
transformers/models/deprecated/ernie_m/modeling_ernie_m.py,sha256=flc9_1HuMrChGoHcx4au3IlEbt9VaWDYY29yaSmG3Dc,47028
transformers/models/deprecated/ernie_m/tokenization_ernie_m.py,sha256=oGKdPntR5sjU3XrxbaRNySX76bQaSLlFWNTgLJfmXBI,16169
transformers/models/deprecated/gptsan_japanese/__init__.py,sha256=8a1T_PBkN2MKzJDSTVJan5kSknwon3cRtUicoKVt2SY,2083
transformers/models/deprecated/gptsan_japanese/__pycache__/__init__.cpython-312.pyc,,
transformers/models/deprecated/gptsan_japanese/__pycache__/configuration_gptsan_japanese.cpython-312.pyc,,
transformers/models/deprecated/gptsan_japanese/__pycache__/modeling_gptsan_japanese.cpython-312.pyc,,
transformers/models/deprecated/gptsan_japanese/__pycache__/tokenization_gptsan_japanese.cpython-312.pyc,,
transformers/models/deprecated/gptsan_japanese/configuration_gptsan_japanese.py,sha256=T8buHMjH3XFnx7BXXis6M5aTvWLwwnleTf-YDyySwNM,7124
transformers/models/deprecated/gptsan_japanese/modeling_gptsan_japanese.py,sha256=flceh7ota13xrP0Kxs3cjZhAGeW0kNhw6oqGtCJBkug,65010
transformers/models/deprecated/gptsan_japanese/tokenization_gptsan_japanese.py,sha256=zI356SLqne5ZLBkp1sZBjp8MCOP3VZ__zVVsl5iyDbU,22619
transformers/models/deprecated/graphormer/__init__.py,sha256=ltRElMWou0jRd50T50NHoJoSUbvM5IrcT41EcFQ7mV0,1682
transformers/models/deprecated/graphormer/__pycache__/__init__.cpython-312.pyc,,
transformers/models/deprecated/graphormer/__pycache__/collating_graphormer.cpython-312.pyc,,
transformers/models/deprecated/graphormer/__pycache__/configuration_graphormer.cpython-312.pyc,,
transformers/models/deprecated/graphormer/__pycache__/modeling_graphormer.cpython-312.pyc,,
transformers/models/deprecated/graphormer/algos_graphormer.pyx,sha256=b_Qlm1hKCHnAqx6oOLGC9LkivAV0K_AZRGgXT9MmBas,3635
transformers/models/deprecated/graphormer/collating_graphormer.py,sha256=KRew-2p9_7heLTflAYA6dObor_Hxy47yIP8HFEgaj1U,6087
transformers/models/deprecated/graphormer/configuration_graphormer.py,sha256=ZzNCBEZj_G1S1lg3MouwutiSeO9G47yFob14WGXXN9g,10380
transformers/models/deprecated/graphormer/modeling_graphormer.py,sha256=Y3aYbgX5vIYB7FfM8jRkv2xZRLdoHZuS5aBtesLXqX8,37006
transformers/models/deprecated/jukebox/__init__.py,sha256=96yLuu-yOBcAHaz1zhvc4RWwIvqkjycikMa-GXFcWm8,1889
transformers/models/deprecated/jukebox/__pycache__/__init__.cpython-312.pyc,,
transformers/models/deprecated/jukebox/__pycache__/configuration_jukebox.cpython-312.pyc,,
transformers/models/deprecated/jukebox/__pycache__/modeling_jukebox.cpython-312.pyc,,
transformers/models/deprecated/jukebox/__pycache__/tokenization_jukebox.cpython-312.pyc,,
transformers/models/deprecated/jukebox/configuration_jukebox.py,sha256=-gLq4uKdqdjCWuV9ZbChsUiFGEI0a58st5oapPTixGI,26749
transformers/models/deprecated/jukebox/modeling_jukebox.py,sha256=O0xBJi3UyMF8Aj0TyXYbN_2wtouHjy9pIdMbUUdkiZQ,119471
transformers/models/deprecated/jukebox/tokenization_jukebox.py,sha256=r1YcKG2OkPWAKdriQ2BXgX-MBsQHbeyccoc5aKLCpac,17352
transformers/models/deprecated/mctct/__init__.py,sha256=aaM-CVsMyEUWqGHH5xAgnqUu6B5D730X_lTo7CMpo7o,1732
transformers/models/deprecated/mctct/__pycache__/__init__.cpython-312.pyc,,
transformers/models/deprecated/mctct/__pycache__/configuration_mctct.cpython-312.pyc,,
transformers/models/deprecated/mctct/__pycache__/feature_extraction_mctct.cpython-312.pyc,,
transformers/models/deprecated/mctct/__pycache__/modeling_mctct.cpython-312.pyc,,
transformers/models/deprecated/mctct/__pycache__/processing_mctct.cpython-312.pyc,,
transformers/models/deprecated/mctct/configuration_mctct.py,sha256=OmrxkatPuycQORmuIQWznAHsi20nF9CM-HHtHWyh1gM,9073
transformers/models/deprecated/mctct/feature_extraction_mctct.py,sha256=JsaSE20NeqBX8Uw-07Y5HdUcQtbYZqCrTN18Wu2B4rI,13460
transformers/models/deprecated/mctct/modeling_mctct.py,sha256=YiUE1VOTKMH6oGk9vlqKf4Q8YxkQFJBQud9bJXgF6ug,32874
transformers/models/deprecated/mctct/processing_mctct.py,sha256=EkokdjeJPgzsSxriPNmAthZ6WgO_iQyFMpQKXDeS7Uo,5931
transformers/models/deprecated/mega/__init__.py,sha256=i_9dHqDl6RZJ1zebhj8pn3zPlgxRqEynwsM_mj9eWMs,1973
transformers/models/deprecated/mega/__pycache__/__init__.cpython-312.pyc,,
transformers/models/deprecated/mega/__pycache__/configuration_mega.cpython-312.pyc,,
transformers/models/deprecated/mega/__pycache__/modeling_mega.cpython-312.pyc,,
transformers/models/deprecated/mega/configuration_mega.py,sha256=0m3Fsv9KqcZECi7Dbgjdz7nidKqf8MQbdfMsYMlMF_4,12588
transformers/models/deprecated/mega/modeling_mega.py,sha256=-fLQbigFtoljvUS0VisbT_Y7_q9qiyYEkXsM-mdFQbc,109519
transformers/models/deprecated/mmbt/__init__.py,sha256=0CCmesCwGIMNFlf2oDsL0gYaCSpsfAC1_bMOXRcAgF4,1480
transformers/models/deprecated/mmbt/__pycache__/__init__.cpython-312.pyc,,
transformers/models/deprecated/mmbt/__pycache__/configuration_mmbt.cpython-312.pyc,,
transformers/models/deprecated/mmbt/__pycache__/modeling_mmbt.cpython-312.pyc,,
transformers/models/deprecated/mmbt/configuration_mmbt.py,sha256=mVkSYHpXNnKbvGiJ_0MOF8V_lqwu0l4rdhwIDTWFu7o,1597
transformers/models/deprecated/mmbt/modeling_mmbt.py,sha256=ms_fa8G6Ww3kyk7jqLeAdba6k2E6VMBq82zMz5GvFKQ,18913
transformers/models/deprecated/nat/__init__.py,sha256=1KgeUYAs8Ypq1rZgA1tS_cq0GNjTINvjycdQR-m0P7s,1613
transformers/models/deprecated/nat/__pycache__/__init__.cpython-312.pyc,,
transformers/models/deprecated/nat/__pycache__/configuration_nat.cpython-312.pyc,,
transformers/models/deprecated/nat/__pycache__/modeling_nat.cpython-312.pyc,,
transformers/models/deprecated/nat/configuration_nat.py,sha256=o-nifDP9IvftvMzVeoXGGUycwUJ-wox1K6QVd4kpaik,6975
transformers/models/deprecated/nat/modeling_nat.py,sha256=G2ggfJ_RJYBIIAbjvJu2DaAdgEzf74chqVvaNenvcSQ,39728
transformers/models/deprecated/nezha/__init__.py,sha256=p4YuR6FmvGSeCniAaJaTWtL_9kqzMfnGeKAYskaWyeM,2062
transformers/models/deprecated/nezha/__pycache__/__init__.cpython-312.pyc,,
transformers/models/deprecated/nezha/__pycache__/configuration_nezha.cpython-312.pyc,,
transformers/models/deprecated/nezha/__pycache__/modeling_nezha.cpython-312.pyc,,
transformers/models/deprecated/nezha/configuration_nezha.py,sha256=hfpG7tYqEHfddMFZ4Ni6h0DRAuG6UwNEsmlxmK562ew,4817
transformers/models/deprecated/nezha/modeling_nezha.py,sha256=37Yuhy26qkonrj7djUw9tgJTaQp5yF1fVx8ctByXSo4,73924
transformers/models/deprecated/open_llama/__init__.py,sha256=KJ11JLm0-ytx2jZjEVggJMC-BxjklPJqLVF2Fm1Okjw,2702
transformers/models/deprecated/open_llama/__pycache__/__init__.cpython-312.pyc,,
transformers/models/deprecated/open_llama/__pycache__/configuration_open_llama.cpython-312.pyc,,
transformers/models/deprecated/open_llama/__pycache__/modeling_open_llama.cpython-312.pyc,,
transformers/models/deprecated/open_llama/configuration_open_llama.py,sha256=5O8r3FXbzYE4gHGqnhYj7LtSYVujAXKR4ZKkAsTGKLM,7771
transformers/models/deprecated/open_llama/modeling_open_llama.py,sha256=ULLDJbRArLXssjlf_NZO4dtSKClF87u4fm082KpRW0U,43380
transformers/models/deprecated/qdqbert/__init__.py,sha256=xDttpfygkbkttNuKo3pW6e-Z0_MwTbj5uICeiXWgppw,2223
transformers/models/deprecated/qdqbert/__pycache__/__init__.cpython-312.pyc,,
transformers/models/deprecated/qdqbert/__pycache__/configuration_qdqbert.cpython-312.pyc,,
transformers/models/deprecated/qdqbert/__pycache__/modeling_qdqbert.cpython-312.pyc,,
transformers/models/deprecated/qdqbert/configuration_qdqbert.py,sha256=qYx_V85qk4g_o3L-OxiLObqndD834k0j0bDjQUNcfT8,5689
transformers/models/deprecated/qdqbert/modeling_qdqbert.py,sha256=XjGtF8iB8I6lfvL0PQK6cVWRbY2-76Lp_RLi8F-y7Z8,77002
transformers/models/deprecated/realm/__init__.py,sha256=_xkblqSgmTTryPK_c0N_ugcMVYc871UxI2hOskvo_pw,2504
transformers/models/deprecated/realm/__pycache__/__init__.cpython-312.pyc,,
transformers/models/deprecated/realm/__pycache__/configuration_realm.cpython-312.pyc,,
transformers/models/deprecated/realm/__pycache__/modeling_realm.cpython-312.pyc,,
transformers/models/deprecated/realm/__pycache__/retrieval_realm.cpython-312.pyc,,
transformers/models/deprecated/realm/__pycache__/tokenization_realm.cpython-312.pyc,,
transformers/models/deprecated/realm/__pycache__/tokenization_realm_fast.cpython-312.pyc,,
transformers/models/deprecated/realm/configuration_realm.py,sha256=kUxwVQ0A99hr2wEFALWfvgoDJKp0OpxGjls42Q-yVZU,7557
transformers/models/deprecated/realm/modeling_realm.py,sha256=NtHppLE8iK6rMwGjqeFRRMmGFLkm-Kc-54h1ijrvuGk,83476
transformers/models/deprecated/realm/retrieval_realm.py,sha256=cebNTe43Mb5VN1xUzR13ewbvkGnlZ5nlJjGSj0ewoWc,6372
transformers/models/deprecated/realm/tokenization_realm.py,sha256=sHOR4tnLFrZaiZXhXhqnibiZAaAQvtpwL18bJvWrj-c,23114
transformers/models/deprecated/realm/tokenization_realm_fast.py,sha256=eZ76_VPhjsqdEEHRr0_4Yt3HK25y0AfOlDohgPf7790,10953
transformers/models/deprecated/retribert/__init__.py,sha256=I5aMwp2FJw4zoL69qlsk2krQjpJMYIh436X47YxUMu8,2163
transformers/models/deprecated/retribert/__pycache__/__init__.cpython-312.pyc,,
transformers/models/deprecated/retribert/__pycache__/configuration_retribert.cpython-312.pyc,,
transformers/models/deprecated/retribert/__pycache__/modeling_retribert.cpython-312.pyc,,
transformers/models/deprecated/retribert/__pycache__/tokenization_retribert.cpython-312.pyc,,
transformers/models/deprecated/retribert/__pycache__/tokenization_retribert_fast.cpython-312.pyc,,
transformers/models/deprecated/retribert/configuration_retribert.py,sha256=7liSa4MonQVeLEz2VlxuapZglk6Z_CzyG5i8Nxi2MTM,5200
transformers/models/deprecated/retribert/modeling_retribert.py,sha256=tF4Sd2lY3_h0-DNqWQoiFXKxxbjfTqqsxPtJnKLEnd0,9297
transformers/models/deprecated/retribert/tokenization_retribert.py,sha256=NnQiqNw0brmkZojd8gc6uudCYSMjsDaiTni-PUXhsd8,20650
transformers/models/deprecated/retribert/tokenization_retribert_fast.py,sha256=hIkbxCjKbkfblfYAyEE6VOf-l7aEmgXO3myRQ917gho,7820
transformers/models/deprecated/speech_to_text_2/__init__.py,sha256=FrO5Wtn6Uznx5DzVHyfLpdo7iDkeBpFgXC4bHXXgxxo,1951
transformers/models/deprecated/speech_to_text_2/__pycache__/__init__.cpython-312.pyc,,
transformers/models/deprecated/speech_to_text_2/__pycache__/configuration_speech_to_text_2.cpython-312.pyc,,
transformers/models/deprecated/speech_to_text_2/__pycache__/modeling_speech_to_text_2.cpython-312.pyc,,
transformers/models/deprecated/speech_to_text_2/__pycache__/processing_speech_to_text_2.cpython-312.pyc,,
transformers/models/deprecated/speech_to_text_2/__pycache__/tokenization_speech_to_text_2.cpython-312.pyc,,
transformers/models/deprecated/speech_to_text_2/configuration_speech_to_text_2.py,sha256=HpJVunbFUp23kPZBCr9K5sIXKVp238icRyv8_YGcmCI,6001
transformers/models/deprecated/speech_to_text_2/modeling_speech_to_text_2.py,sha256=asJ2UlO6N-HsLjO1eg6rFCWtf2UbH-5NgB7lsPGB0u4,43880
transformers/models/deprecated/speech_to_text_2/processing_speech_to_text_2.py,sha256=7AWU3_OegyHwNxluEMSHjLzBGfYcg3m-TNHq9VHYJTo,4792
transformers/models/deprecated/speech_to_text_2/tokenization_speech_to_text_2.py,sha256=S7biDmProh43S6iAbA0cIJyCVqb6fG6itYkbsI2Ccfc,8405
transformers/models/deprecated/tapex/__init__.py,sha256=lQutKYtwbU8ztPva0tyRnnV-zOWw6rxkGyoOUSuvnUo,926
transformers/models/deprecated/tapex/__pycache__/__init__.cpython-312.pyc,,
transformers/models/deprecated/tapex/__pycache__/tokenization_tapex.cpython-312.pyc,,
transformers/models/deprecated/tapex/tokenization_tapex.py,sha256=MPuB1JknrO9WY_j-Hgy8JWGNKvcowBDrjhFi-bCGALw,64347
transformers/models/deprecated/trajectory_transformer/__init__.py,sha256=XnXDCm4ePannQqnQnMn1Fpqvmq9-1L0_mTeoqObM8-0,1806
transformers/models/deprecated/trajectory_transformer/__pycache__/__init__.cpython-312.pyc,,
transformers/models/deprecated/trajectory_transformer/__pycache__/configuration_trajectory_transformer.cpython-312.pyc,,
transformers/models/deprecated/trajectory_transformer/__pycache__/modeling_trajectory_transformer.cpython-312.pyc,,
transformers/models/deprecated/trajectory_transformer/configuration_trajectory_transformer.py,sha256=qH3gf0InhrlutKUQNA4-OrqWp72n_Ha4B6jA_kZy55U,7061
transformers/models/deprecated/trajectory_transformer/modeling_trajectory_transformer.py,sha256=ts1LBqIjnC9gB53or9STcRJTAE7UaSdZnYHoh4jdtq4,25593
transformers/models/deprecated/transfo_xl/__init__.py,sha256=5IURzrZTTTFlLaPUn2R1ErF8SvQ9nF9QcNM8ENdntyg,2879
transformers/models/deprecated/transfo_xl/__pycache__/__init__.cpython-312.pyc,,
transformers/models/deprecated/transfo_xl/__pycache__/configuration_transfo_xl.cpython-312.pyc,,
transformers/models/deprecated/transfo_xl/__pycache__/modeling_tf_transfo_xl.cpython-312.pyc,,
transformers/models/deprecated/transfo_xl/__pycache__/modeling_tf_transfo_xl_utilities.cpython-312.pyc,,
transformers/models/deprecated/transfo_xl/__pycache__/modeling_transfo_xl.cpython-312.pyc,,
transformers/models/deprecated/transfo_xl/__pycache__/modeling_transfo_xl_utilities.cpython-312.pyc,,
transformers/models/deprecated/transfo_xl/__pycache__/tokenization_transfo_xl.cpython-312.pyc,,
transformers/models/deprecated/transfo_xl/configuration_transfo_xl.py,sha256=U3zrDVAkNbmSdcyLRnGVvHEo6BCZMONZKYHhrKIMGi0,7874
transformers/models/deprecated/transfo_xl/modeling_tf_transfo_xl.py,sha256=ZTxGgzubBKUieotTK-Z71Tvt7KKO9S-CF5pmOtAWl_U,45905
transformers/models/deprecated/transfo_xl/modeling_tf_transfo_xl_utilities.py,sha256=Dlv3ZzRduWFBnZZHn8RegbW45XeCecuYCzzzZC3bDXs,7633
transformers/models/deprecated/transfo_xl/modeling_transfo_xl.py,sha256=tqUEFzTSvwGe55FfIeQ3X5dJovQUyG03XhuA5PWArlw,55892
transformers/models/deprecated/transfo_xl/modeling_transfo_xl_utilities.py,sha256=L1l4K7sj8rwXzvhn7_-RK2UbOnYtfDUF0VdFr4L8nxA,10859
transformers/models/deprecated/transfo_xl/tokenization_transfo_xl.py,sha256=d0gQf4hb0F_so24jKSJ3rLip1tVdwEYDXta-icyKuVs,32004
transformers/models/deprecated/tvlt/__init__.py,sha256=Ryp_kcJdg3sqjFKgyRVZjXAMUp_Epg9CL2GLQAHD0k0,2520
transformers/models/deprecated/tvlt/__pycache__/__init__.cpython-312.pyc,,
transformers/models/deprecated/tvlt/__pycache__/configuration_tvlt.cpython-312.pyc,,
transformers/models/deprecated/tvlt/__pycache__/feature_extraction_tvlt.cpython-312.pyc,,
transformers/models/deprecated/tvlt/__pycache__/image_processing_tvlt.cpython-312.pyc,,
transformers/models/deprecated/tvlt/__pycache__/modeling_tvlt.cpython-312.pyc,,
transformers/models/deprecated/tvlt/__pycache__/processing_tvlt.cpython-312.pyc,,
transformers/models/deprecated/tvlt/configuration_tvlt.py,sha256=uGh6Ie-Nu-uf5987LLtFBvpEqd8rLEjFzzkMKIol6b4,8623
transformers/models/deprecated/tvlt/feature_extraction_tvlt.py,sha256=Mx7tuGJvK-1YnS7ggYL6j_emzolu8L8Hrce5ATPtPR0,10558
transformers/models/deprecated/tvlt/image_processing_tvlt.py,sha256=--v_ekqBZ3NK9LrWxzAHYi8dvL8tHK_rKX_oenp3seU,20090
transformers/models/deprecated/tvlt/modeling_tvlt.py,sha256=hQ0PyxS8oOjiokuLtMAxY1jNSL9WoKmH7k6RBrm6scs,56698
transformers/models/deprecated/tvlt/processing_tvlt.py,sha256=pC3zQjapxdhkqrl1QdJ7mXkEOSGNooP7kTUEWKUr_nE,3507
transformers/models/deprecated/van/__init__.py,sha256=UGKlepGOpOuVmsb6Mmess6kPa2qP4rLzfzJ0dsdQDno,1564
transformers/models/deprecated/van/__pycache__/__init__.cpython-312.pyc,,
transformers/models/deprecated/van/__pycache__/configuration_van.cpython-312.pyc,,
transformers/models/deprecated/van/__pycache__/modeling_van.cpython-312.pyc,,
transformers/models/deprecated/van/configuration_van.py,sha256=QpN-p2Hg0C59if2JSEG47j_zepx1f4KpCgIBYgLhCOY,4657
transformers/models/deprecated/van/modeling_van.py,sha256=YwNP7YVzKpyURSQ3taijiOBDTlF086hEcT2fEGqQaTE,21130
transformers/models/deprecated/vit_hybrid/__init__.py,sha256=Ld0UOl3F4y-YaI42jk7ym_wvTFswFGJj_M6jpSeXU_E,2125
transformers/models/deprecated/vit_hybrid/__pycache__/__init__.cpython-312.pyc,,
transformers/models/deprecated/vit_hybrid/__pycache__/configuration_vit_hybrid.cpython-312.pyc,,
transformers/models/deprecated/vit_hybrid/__pycache__/image_processing_vit_hybrid.cpython-312.pyc,,
transformers/models/deprecated/vit_hybrid/__pycache__/modeling_vit_hybrid.cpython-312.pyc,,
transformers/models/deprecated/vit_hybrid/configuration_vit_hybrid.py,sha256=VwJpgMa1l9rL-Rx9jF-POC_mz69y88tnHqgS431Qa10,8230
transformers/models/deprecated/vit_hybrid/image_processing_vit_hybrid.py,sha256=4Sw6X-fDYE1ySXQXk-AecKf5yHYrzmNihgUOgrCaegc,16219
transformers/models/deprecated/vit_hybrid/modeling_vit_hybrid.py,sha256=22N1IODYpHHj1cNaqZ-aCukh9bViJHt3bMNo2qJQ9VQ,32563
transformers/models/deprecated/xlm_prophetnet/__init__.py,sha256=OYkcL5jhbHEaLLvOAd0v-CppyQxbuRzNEzaSDBhXxKA,2408
transformers/models/deprecated/xlm_prophetnet/__pycache__/__init__.cpython-312.pyc,,
transformers/models/deprecated/xlm_prophetnet/__pycache__/configuration_xlm_prophetnet.cpython-312.pyc,,
transformers/models/deprecated/xlm_prophetnet/__pycache__/modeling_xlm_prophetnet.cpython-312.pyc,,
transformers/models/deprecated/xlm_prophetnet/__pycache__/tokenization_xlm_prophetnet.cpython-312.pyc,,
transformers/models/deprecated/xlm_prophetnet/configuration_xlm_prophetnet.py,sha256=dfa6w3RvOEZLxS9GAdQOKaJF1vT6Gc-w06a8Eujg5Sk,8916
transformers/models/deprecated/xlm_prophetnet/modeling_xlm_prophetnet.py,sha256=EX5mzNPUYfWqcN5B-YquCXvM_SE7TfZcbtzslv1WPBI,115593
transformers/models/deprecated/xlm_prophetnet/tokenization_xlm_prophetnet.py,sha256=HpD7sKmAaelMUUDwNrAyHgBLhiWI2rnDaxn-yFFjdEY,13272
transformers/models/depth_anything/__init__.py,sha256=Jbd8LXt-fU3_cTF7jBrkBBw-Kzscv6o7O0YiZy0R8-A,1009
transformers/models/depth_anything/__pycache__/__init__.cpython-312.pyc,,
transformers/models/depth_anything/__pycache__/configuration_depth_anything.cpython-312.pyc,,
transformers/models/depth_anything/__pycache__/modeling_depth_anything.cpython-312.pyc,,
transformers/models/depth_anything/configuration_depth_anything.py,sha256=hROtoRA46y2aGhvVebFlgFP926bDuwR6Bi7sIQBgsmE,7974
transformers/models/depth_anything/modeling_depth_anything.py,sha256=CD1cRmNVA4eNIL38TRAxZb4JzCuVJ2pDHmzL20715B8,18578
transformers/models/depth_pro/__init__.py,sha256=5R4N4IVUQuK8bCFtg9qGvJFceJaHXNj4HdCWkcsyELc,1096
transformers/models/depth_pro/__pycache__/__init__.cpython-312.pyc,,
transformers/models/depth_pro/__pycache__/configuration_depth_pro.cpython-312.pyc,,
transformers/models/depth_pro/__pycache__/image_processing_depth_pro.cpython-312.pyc,,
transformers/models/depth_pro/__pycache__/image_processing_depth_pro_fast.cpython-312.pyc,,
transformers/models/depth_pro/__pycache__/modeling_depth_pro.cpython-312.pyc,,
transformers/models/depth_pro/configuration_depth_pro.py,sha256=EqO_bUCMBHYXyy1792ObohXWrzEN_3fSVj_qb76Wb0o,10725
transformers/models/depth_pro/image_processing_depth_pro.py,sha256=3aKNTqqbnHMiLDmnsHLYYUcs34QunpQFaTyzVLy5qb8,18819
transformers/models/depth_pro/image_processing_depth_pro_fast.py,sha256=A7I3YxOOt2iEt2Up_g2xMjR2KnTJkYdxYXCI627cfFk,6987
transformers/models/depth_pro/modeling_depth_pro.py,sha256=N8woVgBsSxN4B9B3OelrKunkMmpjE3AOuMtq8kUj7QI,48380
transformers/models/detr/__init__.py,sha256=YEWZnoCCgWt4KZNfbSi-v4KNDOJT2-ii2sxanyVDkvY,1120
transformers/models/detr/__pycache__/__init__.cpython-312.pyc,,
transformers/models/detr/__pycache__/configuration_detr.cpython-312.pyc,,
transformers/models/detr/__pycache__/feature_extraction_detr.cpython-312.pyc,,
transformers/models/detr/__pycache__/image_processing_detr.cpython-312.pyc,,
transformers/models/detr/__pycache__/image_processing_detr_fast.cpython-312.pyc,,
transformers/models/detr/__pycache__/modeling_detr.cpython-312.pyc,,
transformers/models/detr/configuration_detr.py,sha256=eTZFAP6ftOOLrByaUwGPakruhbvTIHYp-NBm_bf6Bcs,13678
transformers/models/detr/feature_extraction_detr.py,sha256=HTGOQ_7RlxFfmu6PfXwDB18yMnUblwi9lDWXql0So0M,1511
transformers/models/detr/image_processing_detr.py,sha256=rEONHRbwv_qAkWuHPykibXONFExEuwHXSGvsoXvI9Ks,93972
transformers/models/detr/image_processing_detr_fast.py,sha256=1Q4reZGeSWAs8nIIn9Fb0clf9QUCnztmhUDTzrrdrrQ,61847
transformers/models/detr/modeling_detr.py,sha256=uVVu9OL4NamvQQQJK69LxwoHmmuDQ-8WsT4Rq--HGyU,88299
transformers/models/dialogpt/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
transformers/models/dialogpt/__pycache__/__init__.cpython-312.pyc,,
transformers/models/diffllama/__init__.py,sha256=Yosk5eQ82PblntLff-bL3pfJZ-AVKp5jbQK5R2SLVc8,1004
transformers/models/diffllama/__pycache__/__init__.cpython-312.pyc,,
transformers/models/diffllama/__pycache__/configuration_diffllama.cpython-312.pyc,,
transformers/models/diffllama/__pycache__/modeling_diffllama.cpython-312.pyc,,
transformers/models/diffllama/__pycache__/modular_diffllama.cpython-312.pyc,,
transformers/models/diffllama/configuration_diffllama.py,sha256=Z53H3hXOScYSjNPWslS6899Xi_WdC6gxKdbuZ0LBbV8,10682
transformers/models/diffllama/modeling_diffllama.py,sha256=urOujXJV6TLOcg504X0tN3h7zcZoLV39fsvefQrUCLc,66478
transformers/models/diffllama/modular_diffllama.py,sha256=bB5tZNcfECYkRLSikLp1swyjH4XxkXCxZj-tjcmMCQE,21129
transformers/models/dinat/__init__.py,sha256=N0HykajUSY5KsvPQNUxc8jAuuJntmDJ-Dz8Qa8_sJ9E,991
transformers/models/dinat/__pycache__/__init__.cpython-312.pyc,,
transformers/models/dinat/__pycache__/configuration_dinat.cpython-312.pyc,,
transformers/models/dinat/__pycache__/modeling_dinat.cpython-312.pyc,,
transformers/models/dinat/configuration_dinat.py,sha256=-RhmPxqGfoTz4-snZUO7PGhetovRg6CrwZSlVtKn2mE,7356
transformers/models/dinat/modeling_dinat.py,sha256=ioU0HtLrbY8BYJGnNnvgoSP4FtQEuLT9Z6-OteSzBZk,40423
transformers/models/dinov2/__init__.py,sha256=fDyp5N-KcJzO-vUeT3fZA8UbC21FfGEhDOlYNvXHHDc,1033
transformers/models/dinov2/__pycache__/__init__.cpython-312.pyc,,
transformers/models/dinov2/__pycache__/configuration_dinov2.cpython-312.pyc,,
transformers/models/dinov2/__pycache__/modeling_dinov2.cpython-312.pyc,,
transformers/models/dinov2/__pycache__/modeling_flax_dinov2.cpython-312.pyc,,
transformers/models/dinov2/configuration_dinov2.py,sha256=POPOfoHUbnExfhDCLkEHHN_wesix84OiOGugk1rXLW4,8282
transformers/models/dinov2/modeling_dinov2.py,sha256=yGIe6TciMBdapG_Qnj3JTrC1ZzcGUrn-1_NlFdM-WZc,38901
transformers/models/dinov2/modeling_flax_dinov2.py,sha256=Y9_43wVzybDQYLc3SIfpx3TiJ8wI3PBzbddnN986sHI,30844
transformers/models/dinov2_with_registers/__init__.py,sha256=s0cefgSRnlIVcdZYV0qz3Q9X3IEChU7mkGbbnr2IH6E,1023
transformers/models/dinov2_with_registers/__pycache__/__init__.cpython-312.pyc,,
transformers/models/dinov2_with_registers/__pycache__/configuration_dinov2_with_registers.cpython-312.pyc,,
transformers/models/dinov2_with_registers/__pycache__/modeling_dinov2_with_registers.cpython-312.pyc,,
transformers/models/dinov2_with_registers/__pycache__/modular_dinov2_with_registers.cpython-312.pyc,,
transformers/models/dinov2_with_registers/configuration_dinov2_with_registers.py,sha256=snup7E3s7-WBPv47EhCQhJfdfjkXZSi4yOQf1R9ot10,8633
transformers/models/dinov2_with_registers/modeling_dinov2_with_registers.py,sha256=OyiPXXBVkoDaqeo_re9jrruYTrLrQot801xUg3zq_IY,41338
transformers/models/dinov2_with_registers/modular_dinov2_with_registers.py,sha256=_RtypPdWkgnhvadM_7p8Q5endhHJTpfa_VwqpEGGdj0,17147
transformers/models/distilbert/__init__.py,sha256=dKwCe9QsyAaNsdUJFMUa-vcuHPSQSuLKFoFBvK3cLEY,1178
transformers/models/distilbert/__pycache__/__init__.cpython-312.pyc,,
transformers/models/distilbert/__pycache__/configuration_distilbert.cpython-312.pyc,,
transformers/models/distilbert/__pycache__/modeling_distilbert.cpython-312.pyc,,
transformers/models/distilbert/__pycache__/modeling_flax_distilbert.cpython-312.pyc,,
transformers/models/distilbert/__pycache__/modeling_tf_distilbert.cpython-312.pyc,,
transformers/models/distilbert/__pycache__/tokenization_distilbert.cpython-312.pyc,,
transformers/models/distilbert/__pycache__/tokenization_distilbert_fast.cpython-312.pyc,,
transformers/models/distilbert/configuration_distilbert.py,sha256=PTN973hHHX1YBCy3gT1goLLnzzDOSKnd99zg_ENFRKs,6046
transformers/models/distilbert/modeling_distilbert.py,sha256=eqJGvwpLRfkBuDqauZPn3SwPuR9Pu4dARbrsiudCmm4,60370
transformers/models/distilbert/modeling_flax_distilbert.py,sha256=NNdjM2UOvN_PC4O_OUVugBByL6RYxfCUyjv7gwU0o-k,32914
transformers/models/distilbert/modeling_tf_distilbert.py,sha256=smsNlkXC7ujXHDrKcOcsk2tQt6rLAzS703kseCtM6vw,49145
transformers/models/distilbert/tokenization_distilbert.py,sha256=tRbn7lfNADq37XnMa5ST06c9ObNb9uC6fxveb2qTMbk,22260
transformers/models/distilbert/tokenization_distilbert_fast.py,sha256=Mof7pf-IhB9iRwct1fK9fIUWfT5cGF2-ds5aFOOlMRY,8077
transformers/models/dit/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
transformers/models/dit/__pycache__/__init__.cpython-312.pyc,,
transformers/models/donut/__init__.py,sha256=ZlVffHEdFxl7gEozYS2EhRLmsSlqgnx8lXbhz60l28E,1123
transformers/models/donut/__pycache__/__init__.cpython-312.pyc,,
transformers/models/donut/__pycache__/configuration_donut_swin.cpython-312.pyc,,
transformers/models/donut/__pycache__/feature_extraction_donut.cpython-312.pyc,,
transformers/models/donut/__pycache__/image_processing_donut.cpython-312.pyc,,
transformers/models/donut/__pycache__/modeling_donut_swin.cpython-312.pyc,,
transformers/models/donut/__pycache__/processing_donut.cpython-312.pyc,,
transformers/models/donut/configuration_donut_swin.py,sha256=mHg0P4MRxMOw_IsHKFtBIuSuuY0tINGN3FmUImMqST8,5785
transformers/models/donut/feature_extraction_donut.py,sha256=KvL5oyQoe6T_2zwoqvFTMTzKBHk1heN3yFeIjTEePmU,1217
transformers/models/donut/image_processing_donut.py,sha256=3EMXoGSMlIYSURml9R58yhDGj0YdSwVfbL1hZAP6yTg,21795
transformers/models/donut/modeling_donut_swin.py,sha256=eqbV6pVHAufb-lK70x54AHUfARue9hRQQluNWhlTlvw,45951
transformers/models/donut/processing_donut.py,sha256=0GtsiOV33rwotqpzoTe3CNSgqz4kdlJb-3t3qd_19Bk,9695
transformers/models/dpr/__init__.py,sha256=z4FocLkQ_ckWtBZctTh-aeV1haJJY-lXF0ZRKuVbVkc,1099
transformers/models/dpr/__pycache__/__init__.cpython-312.pyc,,
transformers/models/dpr/__pycache__/configuration_dpr.cpython-312.pyc,,
transformers/models/dpr/__pycache__/modeling_dpr.cpython-312.pyc,,
transformers/models/dpr/__pycache__/modeling_tf_dpr.cpython-312.pyc,,
transformers/models/dpr/__pycache__/tokenization_dpr.cpython-312.pyc,,
transformers/models/dpr/__pycache__/tokenization_dpr_fast.cpython-312.pyc,,
transformers/models/dpr/configuration_dpr.py,sha256=7lGBYx2IC_PGrt-6FIKW_pxJCMNTv3rWLqmNKCXMw8M,6416
transformers/models/dpr/modeling_dpr.py,sha256=cu-E9YFD__7WfuWuEZ34T90m-q8Y_yE7opLV5nf5mdo,28542
transformers/models/dpr/modeling_tf_dpr.py,sha256=g_fxgbstNg0kNKfDjBDVvXdgRchwMBfROcmBI3cr5Jw,33829
transformers/models/dpr/tokenization_dpr.py,sha256=X5F-34vnP8Y14hh4uWUCB8DtMI01oNP3KGrYkhjlyPg,15840
transformers/models/dpr/tokenization_dpr_fast.py,sha256=X4fogc0iNFnvhVY6BML3-Lr0bLDSIO2f8nEHFllngks,16219
transformers/models/dpt/__init__.py,sha256=zucFovHWc15Nyx-7PHrOvECmBOCXf5uYoFCWpmM9Nd4,1069
transformers/models/dpt/__pycache__/__init__.cpython-312.pyc,,
transformers/models/dpt/__pycache__/configuration_dpt.cpython-312.pyc,,
transformers/models/dpt/__pycache__/feature_extraction_dpt.cpython-312.pyc,,
transformers/models/dpt/__pycache__/image_processing_dpt.cpython-312.pyc,,
transformers/models/dpt/__pycache__/modeling_dpt.cpython-312.pyc,,
transformers/models/dpt/configuration_dpt.py,sha256=I6cn7DErZHQ-p1Ad6k3C25wPcpnN8qGVqQekkfgz9T4,14068
transformers/models/dpt/feature_extraction_dpt.py,sha256=oMO44qP7soEwEUlpmu2NsXG_g7ip9pnh9j9MiAjpqpo,1201
transformers/models/dpt/image_processing_dpt.py,sha256=nDthmE3Kg2FSYPwGLODkXVIIAcUmvTdMKJBY5NRfYUI,31422
transformers/models/dpt/modeling_dpt.py,sha256=2l87Q2cv9kyFiylXkVxxvyOlNqpahH9OcffXH8jAkMA,57296
transformers/models/efficientnet/__init__.py,sha256=HJYCtGAWyIgt7AXI4xdDvRBY3Ya7_yZVB9XEcqTHk8A,1054
transformers/models/efficientnet/__pycache__/__init__.cpython-312.pyc,,
transformers/models/efficientnet/__pycache__/configuration_efficientnet.cpython-312.pyc,,
transformers/models/efficientnet/__pycache__/image_processing_efficientnet.cpython-312.pyc,,
transformers/models/efficientnet/__pycache__/modeling_efficientnet.cpython-312.pyc,,
transformers/models/efficientnet/configuration_efficientnet.py,sha256=VeFBfwCoxtbd5M2c-KVrPs0y83XAaX1SGvfaFro65LE,7657
transformers/models/efficientnet/image_processing_efficientnet.py,sha256=5Gvo0abaPeZIzjS45zPgs_A7b8jYaa4NIcULgGxzvx8,18342
transformers/models/efficientnet/modeling_efficientnet.py,sha256=HXw35WXlyL8K658mmsxKf1Iw5A6JSdDd-wvFOkPRjtw,24049
transformers/models/electra/__init__.py,sha256=e6DkZL6cjtWVsTx7tamR-zsyv0tuRYLbuYn-r-04P84,1160
transformers/models/electra/__pycache__/__init__.cpython-312.pyc,,
transformers/models/electra/__pycache__/configuration_electra.cpython-312.pyc,,
transformers/models/electra/__pycache__/modeling_electra.cpython-312.pyc,,
transformers/models/electra/__pycache__/modeling_flax_electra.cpython-312.pyc,,
transformers/models/electra/__pycache__/modeling_tf_electra.cpython-312.pyc,,
transformers/models/electra/__pycache__/tokenization_electra.cpython-312.pyc,,
transformers/models/electra/__pycache__/tokenization_electra_fast.cpython-312.pyc,,
transformers/models/electra/configuration_electra.py,sha256=xJWhtwgiJeGHQ1b3iXGaduC_m_h8yG5fFgQx2MQCR-o,9145
transformers/models/electra/modeling_electra.py,sha256=eJQI4tvzs3PsSu9TD9UltSE-mwV6EsSVvYAIKCshemA,74981
transformers/models/electra/modeling_flax_electra.py,sha256=mOacTelJvmP-61PF4ZJl1X3CFYqJwaqbPbNAJSft9wI,62595
transformers/models/electra/modeling_tf_electra.py,sha256=JGK-UXBGqS8U-BGQhFWZI6BoqRUxUkZuLicSHZQzrXo,78614
transformers/models/electra/tokenization_electra.py,sha256=u_tk2_VroyR1msCEEX_yX45hQp4p7hG_q_WrqD18m58,21260
transformers/models/electra/tokenization_electra_fast.py,sha256=yZ4f6x_EP4MqTZ0DJ1Y_X3OjsvN5Tof_pT6RG3-smV8,7722
transformers/models/emu3/__init__.py,sha256=VEBLADqeToacty2xd3Zu0F_fLQRxvhfiKPkuB9jwcFM,1070
transformers/models/emu3/__pycache__/__init__.cpython-312.pyc,,
transformers/models/emu3/__pycache__/configuration_emu3.cpython-312.pyc,,
transformers/models/emu3/__pycache__/image_processing_emu3.cpython-312.pyc,,
transformers/models/emu3/__pycache__/modeling_emu3.cpython-312.pyc,,
transformers/models/emu3/__pycache__/modular_emu3.cpython-312.pyc,,
transformers/models/emu3/__pycache__/processing_emu3.cpython-312.pyc,,
transformers/models/emu3/configuration_emu3.py,sha256=hgj0gFq8h6e_FOVD2bF5TkMeN_KLtLfoBFKx4aVwja8,16071
transformers/models/emu3/image_processing_emu3.py,sha256=qz8WR0T2pTRX2i3FVkLBu2UdMI-FbFq6HMLZs96e7-0,27870
transformers/models/emu3/modeling_emu3.py,sha256=r_34YCy5Fe6QWZHAavUeK3Wd5MzI7WsSJeMri7AwolE,86296
transformers/models/emu3/modular_emu3.py,sha256=vGsDTzaXkeuMeksnyWiplldhPudCIYc_H8vg18zKUM8,54490
transformers/models/emu3/processing_emu3.py,sha256=Rbzg8DF0TIrFtELJxsWrshsqOmE0ZGL8u7cq6JLvmHk,10462
transformers/models/encodec/__init__.py,sha256=QbO9yEfCaRwYKbK0vvmwKMbqRAToyos-HTHhRmf7n5s,1041
transformers/models/encodec/__pycache__/__init__.cpython-312.pyc,,
transformers/models/encodec/__pycache__/configuration_encodec.cpython-312.pyc,,
transformers/models/encodec/__pycache__/feature_extraction_encodec.cpython-312.pyc,,
transformers/models/encodec/__pycache__/modeling_encodec.cpython-312.pyc,,
transformers/models/encodec/configuration_encodec.py,sha256=Gc1MglGDHO8Yhqo2zy73X_lLyFI7BVBFZuk701jDwI8,8525
transformers/models/encodec/feature_extraction_encodec.py,sha256=OwPRjcZanBWGoX-HmQo-NLEv-uRPK-tTWqfEWsjERIQ,9913
transformers/models/encodec/modeling_encodec.py,sha256=BPiOslfKi7vMBkyhU15JPfsa8ZfsTtPQXvSd4F8ntCw,33842
transformers/models/encoder_decoder/__init__.py,sha256=wxXN9-4nCvYICfq8pE592rdRiQXK7S69V2cWGVQyIkw,1107
transformers/models/encoder_decoder/__pycache__/__init__.cpython-312.pyc,,
transformers/models/encoder_decoder/__pycache__/configuration_encoder_decoder.cpython-312.pyc,,
transformers/models/encoder_decoder/__pycache__/modeling_encoder_decoder.cpython-312.pyc,,
transformers/models/encoder_decoder/__pycache__/modeling_flax_encoder_decoder.cpython-312.pyc,,
transformers/models/encoder_decoder/__pycache__/modeling_tf_encoder_decoder.cpython-312.pyc,,
transformers/models/encoder_decoder/configuration_encoder_decoder.py,sha256=YpO0_JNh13uQI6WCv5mMf81bw-r2j0S_zZLMwuEfeSM,4586
transformers/models/encoder_decoder/modeling_encoder_decoder.py,sha256=45y9NGKEnrCBGlYRLghxYya_Z03qUgONvZDDQd3Ujqw,35509
transformers/models/encoder_decoder/modeling_flax_encoder_decoder.py,sha256=hMDet9z998RL2MRZG35qSXJ0PM0I21EqT73Zx_Hjs9U,43567
transformers/models/encoder_decoder/modeling_tf_encoder_decoder.py,sha256=1lRgrNX6Y6yCIBkX2AtS-eNaE3hmCgWAmf2eQ0OsqAg,34344
transformers/models/ernie/__init__.py,sha256=TyzaXpzGwu-WqsIn1tavDqa7BCV9X-mPho4JDa9gk0I,991
transformers/models/ernie/__pycache__/__init__.cpython-312.pyc,,
transformers/models/ernie/__pycache__/configuration_ernie.cpython-312.pyc,,
transformers/models/ernie/__pycache__/modeling_ernie.cpython-312.pyc,,
transformers/models/ernie/configuration_ernie.py,sha256=7DStIkig-TYcDsneQwKwz2DZ2XGK9EjHYeJeZ7TbgaU,7694
transformers/models/ernie/modeling_ernie.py,sha256=uxoa1vr9BxP8ghgfpu7O3nbrfZT5a6gqmNTcdCCSua4,83480
transformers/models/esm/__init__.py,sha256=muSqvVMt6mySkoAm7MjweiFHJVBSj70LlakjHmZ6PEE,1094
transformers/models/esm/__pycache__/__init__.cpython-312.pyc,,
transformers/models/esm/__pycache__/configuration_esm.cpython-312.pyc,,
transformers/models/esm/__pycache__/modeling_esm.cpython-312.pyc,,
transformers/models/esm/__pycache__/modeling_esmfold.cpython-312.pyc,,
transformers/models/esm/__pycache__/modeling_tf_esm.cpython-312.pyc,,
transformers/models/esm/__pycache__/tokenization_esm.cpython-312.pyc,,
transformers/models/esm/configuration_esm.py,sha256=3CtwlRuK-R8d6rBHlmTOui7_cNx1ZvDeKnTQZ2wr0xY,14410
transformers/models/esm/modeling_esm.py,sha256=b4Z1ca7uD7n0RMsjrF6MoYmqSwVCSq2YbJx0ts5BLLg,55718
transformers/models/esm/modeling_esmfold.py,sha256=NRq9A_z2tkEOzz3kwRMHbOupuMT057VFiB9q7SY5skk,86971
transformers/models/esm/modeling_tf_esm.py,sha256=RbaZNctSONOdCx0i4_HE7F2psPvKWf3MOsOE2OiPS6A,69124
transformers/models/esm/openfold_utils/__init__.py,sha256=Xy2uqvFsLC8Ax-OOce5PgoBDiZgEJgJPqs__p5SBWUY,446
transformers/models/esm/openfold_utils/__pycache__/__init__.cpython-312.pyc,,
transformers/models/esm/openfold_utils/__pycache__/chunk_utils.cpython-312.pyc,,
transformers/models/esm/openfold_utils/__pycache__/data_transforms.cpython-312.pyc,,
transformers/models/esm/openfold_utils/__pycache__/feats.cpython-312.pyc,,
transformers/models/esm/openfold_utils/__pycache__/loss.cpython-312.pyc,,
transformers/models/esm/openfold_utils/__pycache__/protein.cpython-312.pyc,,
transformers/models/esm/openfold_utils/__pycache__/residue_constants.cpython-312.pyc,,
transformers/models/esm/openfold_utils/__pycache__/rigid_utils.cpython-312.pyc,,
transformers/models/esm/openfold_utils/__pycache__/tensor_utils.cpython-312.pyc,,
transformers/models/esm/openfold_utils/chunk_utils.py,sha256=co29vXYCaTh3g6PPSsvb_5GyePXVudMkISVHkARDT38,14390
transformers/models/esm/openfold_utils/data_transforms.py,sha256=F4wGANRhKLd6MLHrwg2IxpqCxCJEx8aFSxqAdsXsBMo,3764
transformers/models/esm/openfold_utils/feats.py,sha256=RHH65TclSlcI-fuGP16f6xr_QolV0aGRXEWUq-0boIU,8368
transformers/models/esm/openfold_utils/loss.py,sha256=wY2ONqbuRvWMomjkpfPwfoa7dqCO2vFkM-kmNfhjivo,3705
transformers/models/esm/openfold_utils/protein.py,sha256=R7diEvvIOtJY28B-_6TSMZdWmLFY4NOwaMzQmAg0x_w,11491
transformers/models/esm/openfold_utils/residue_constants.py,sha256=FtPlWVweacknPfmi4XCrR66kFr4EuYXywvx0IEY8KAs,37992
transformers/models/esm/openfold_utils/rigid_utils.py,sha256=J-xQV4KrkBNwHR4TSHBwT85pOYKf-nJ78Os4JtiJbxE,41130
transformers/models/esm/openfold_utils/tensor_utils.py,sha256=cySnhhaYbdq4SqyWyAF3qGeUWPfWKsuTYWRnX-h21sE,4781
transformers/models/esm/tokenization_esm.py,sha256=8A5P1nkmAFSiW2LTtBHwikMrA767SYUSLW9fNTE2knI,5385
transformers/models/falcon/__init__.py,sha256=qmBlF_xusyrueKMfriC2ldVrHzeLIT7ruSdduMODuE4,993
transformers/models/falcon/__pycache__/__init__.cpython-312.pyc,,
transformers/models/falcon/__pycache__/configuration_falcon.cpython-312.pyc,,
transformers/models/falcon/__pycache__/modeling_falcon.cpython-312.pyc,,
transformers/models/falcon/configuration_falcon.py,sha256=aPqHUHDkM-SeeoHw-qgtBa9y-ec4oQhaP3LYMQU3-sc,10917
transformers/models/falcon/modeling_falcon.py,sha256=0tUtusjeC1IAujOusN0ZSEJ1-a-FdXJNoy7Hr_QUoyc,74400
transformers/models/falcon_mamba/__init__.py,sha256=Czo-T_Nt73nvRbK-yJEZAYsU3Bxu4i1fOxFuPosiFPw,1005
transformers/models/falcon_mamba/__pycache__/__init__.cpython-312.pyc,,
transformers/models/falcon_mamba/__pycache__/configuration_falcon_mamba.cpython-312.pyc,,
transformers/models/falcon_mamba/__pycache__/modeling_falcon_mamba.cpython-312.pyc,,
transformers/models/falcon_mamba/configuration_falcon_mamba.py,sha256=S6EfKK6HVnT9bNZ_tqSuOoaeBtvYpsOAX6Zb0yVAGX4,7762
transformers/models/falcon_mamba/modeling_falcon_mamba.py,sha256=o7dNXM2ze9g55LN_gyCFnkBEOGTmpba91sUC1ArozwE,40716
transformers/models/fastspeech2_conformer/__init__.py,sha256=pILmX51CcqSiFGtl_dsX1yW2S_QugA3UHAT8f4psOtA,1077
transformers/models/fastspeech2_conformer/__pycache__/__init__.cpython-312.pyc,,
transformers/models/fastspeech2_conformer/__pycache__/configuration_fastspeech2_conformer.cpython-312.pyc,,
transformers/models/fastspeech2_conformer/__pycache__/modeling_fastspeech2_conformer.cpython-312.pyc,,
transformers/models/fastspeech2_conformer/__pycache__/tokenization_fastspeech2_conformer.cpython-312.pyc,,
transformers/models/fastspeech2_conformer/configuration_fastspeech2_conformer.py,sha256=Sff6-HrwSc_WQzF3YwWjTJJ_Xfe5m3hN_WL7OKXbAXQ,24631
transformers/models/fastspeech2_conformer/modeling_fastspeech2_conformer.py,sha256=WV5sEofZpghpK9e1DytzlnmMiDprSp-TC4WPCFuubao,78023
transformers/models/fastspeech2_conformer/tokenization_fastspeech2_conformer.py,sha256=XZumdFJwNj9wfU_ijmZSUWKY2fn8RTuB8zSzyOza328,6265
transformers/models/flaubert/__init__.py,sha256=LdGmxq7pcDPVcvqO1ol7VYtpjKKCAQuiJ1ISrNT9nEs,1078
transformers/models/flaubert/__pycache__/__init__.cpython-312.pyc,,
transformers/models/flaubert/__pycache__/configuration_flaubert.cpython-312.pyc,,
transformers/models/flaubert/__pycache__/modeling_flaubert.cpython-312.pyc,,
transformers/models/flaubert/__pycache__/modeling_tf_flaubert.cpython-312.pyc,,
transformers/models/flaubert/__pycache__/tokenization_flaubert.cpython-312.pyc,,
transformers/models/flaubert/configuration_flaubert.py,sha256=bd7BCSDeNq6Q2hu79BXPjOkYqQL9RjkeWPtzAaSp_SM,11241
transformers/models/flaubert/modeling_flaubert.py,sha256=UXQ9oVdgyu1zLzsG1wMKaC3j2qLr518QNsYL50wMDTw,57891
transformers/models/flaubert/modeling_tf_flaubert.py,sha256=4hGU3PUC_IswNagCpGqfrzLu27F8OYSGmFOm4_XSh9A,57346
transformers/models/flaubert/tokenization_flaubert.py,sha256=SNK5TKNn2tctqVQUWSSwDuLLe_v9d6Ozw68OXk6dv40,22208
transformers/models/flava/__init__.py,sha256=u1s081HA6nko-0YymDOaaHoK5uRqO_bLdJCTgdX0vcA,1113
transformers/models/flava/__pycache__/__init__.cpython-312.pyc,,
transformers/models/flava/__pycache__/configuration_flava.cpython-312.pyc,,
transformers/models/flava/__pycache__/feature_extraction_flava.cpython-312.pyc,,
transformers/models/flava/__pycache__/image_processing_flava.cpython-312.pyc,,
transformers/models/flava/__pycache__/modeling_flava.cpython-312.pyc,,
transformers/models/flava/__pycache__/processing_flava.cpython-312.pyc,,
transformers/models/flava/configuration_flava.py,sha256=sjJJklCE6Lr_Gxlx71jOyd8CuVh_4EZkAbeyaGhsEag,34065
transformers/models/flava/feature_extraction_flava.py,sha256=2KMUGwnka1QJbCmDYmiqETaSNDCWQheR_zWWWvcIk9w,1239
transformers/models/flava/image_processing_flava.py,sha256=cci87P-2ZE_CyNKQTDA4BLEBCOn2KrJQD2IxeTFzWyk,37437
transformers/models/flava/modeling_flava.py,sha256=PE0T1Y9Zw9GFnRGM5Ayo6FdW9spXEa5sihXMDIYTZ6Y,96700
transformers/models/flava/processing_flava.py,sha256=jruLqgFkfdRXWRyP-QI4FlbqRz8xS-cVBRvSAwD4Okw,6863
transformers/models/fnet/__init__.py,sha256=V3nuz_DsD_K5-RuL-Gt4hr5FVtNz12s46O_Vtx_xvCY,1068
transformers/models/fnet/__pycache__/__init__.cpython-312.pyc,,
transformers/models/fnet/__pycache__/configuration_fnet.cpython-312.pyc,,
transformers/models/fnet/__pycache__/modeling_fnet.cpython-312.pyc,,
transformers/models/fnet/__pycache__/tokenization_fnet.cpython-312.pyc,,
transformers/models/fnet/__pycache__/tokenization_fnet_fast.cpython-312.pyc,,
transformers/models/fnet/configuration_fnet.py,sha256=oZVGszdEYsE-nJnpSlmU3r4tENCfwHnNKaL4NmrD7N4,5567
transformers/models/fnet/modeling_fnet.py,sha256=TwvNEgouigYEV2tDOUD2yNZgfnZlrkLfdk3HYNT7LuI,49558
transformers/models/fnet/tokenization_fnet.py,sha256=jUkuFNfelVVWVhPHNVBl2kbx5k9IfVE26xnqHnyF7B4,14578
transformers/models/fnet/tokenization_fnet_fast.py,sha256=6t6PuMKkgdlQ2BUpA-67KSfu7fEB0Ubk-Voxqq9SGJo,8096
transformers/models/focalnet/__init__.py,sha256=kFk7pYv4troBIWdCYosHMKh8PAnpXqjlxaRRQ5adkG0,997
transformers/models/focalnet/__pycache__/__init__.cpython-312.pyc,,
transformers/models/focalnet/__pycache__/configuration_focalnet.cpython-312.pyc,,
transformers/models/focalnet/__pycache__/modeling_focalnet.cpython-312.pyc,,
transformers/models/focalnet/configuration_focalnet.py,sha256=y2d2fA5dtonpX0OtCNY9gVKmz0xITrylfbQYWlwuyM4,8057
transformers/models/focalnet/modeling_focalnet.py,sha256=Urn4P56RWAfX2eWZxllor9LtcZVx1bYwr4GPYfv4mC8,43269
transformers/models/fsmt/__init__.py,sha256=u_Xx7d3qDicqwR_W0js1h2wPiLKWM1RlMu7fsBdIHy4,1026
transformers/models/fsmt/__pycache__/__init__.cpython-312.pyc,,
transformers/models/fsmt/__pycache__/configuration_fsmt.cpython-312.pyc,,
transformers/models/fsmt/__pycache__/modeling_fsmt.cpython-312.pyc,,
transformers/models/fsmt/__pycache__/tokenization_fsmt.cpython-312.pyc,,
transformers/models/fsmt/configuration_fsmt.py,sha256=IOCuyx1-F-_Nrp1bnHCUxmf75OtArzQf-w0HfcWDJHo,10090
transformers/models/fsmt/modeling_fsmt.py,sha256=gmWfAOfZtcd48SjWR8tB1j_JYysEx_22_QUtlTRu56w,57798
transformers/models/fsmt/tokenization_fsmt.py,sha256=vSDmTbQxJRS1-nfPCW8JglxwhOW8ius4TCekcbtVTrc,19263
transformers/models/funnel/__init__.py,sha256=087Y3Xz6y0HA5SgKe-s2z-ZzUIq1u_axxCRh2__gVro,1182
transformers/models/funnel/__pycache__/__init__.cpython-312.pyc,,
transformers/models/funnel/__pycache__/configuration_funnel.cpython-312.pyc,,
transformers/models/funnel/__pycache__/modeling_funnel.cpython-312.pyc,,
transformers/models/funnel/__pycache__/modeling_tf_funnel.cpython-312.pyc,,
transformers/models/funnel/__pycache__/tokenization_funnel.cpython-312.pyc,,
transformers/models/funnel/__pycache__/tokenization_funnel_fast.cpython-312.pyc,,
transformers/models/funnel/configuration_funnel.py,sha256=cyXfyi1BlvtD683UockXRWIgmV1clSEuwj1euzsw36Q,7680
transformers/models/funnel/modeling_funnel.py,sha256=ra6KVVAtBparReiOl1sb2D2wn_8B-OcmBYhJhdum-aA,69763
transformers/models/funnel/modeling_tf_funnel.py,sha256=sQ8kdJvvWpjY2Ghodmzff5xuGtgdV-NW3kXDBddlRdw,80462
transformers/models/funnel/tokenization_funnel.py,sha256=FJ93eN1Pn2MlGC43HgyqKb2YjDGPWQr73Ekqnk-AaL4,22718
transformers/models/funnel/tokenization_funnel_fast.py,sha256=UeGB97RlM-XJ_e22EZLFjFyL5bf6uZ3ejAbucN3cdzw,8679
transformers/models/fuyu/__init__.py,sha256=NcygIhTFvIZzXPZUReC1WYReGAVINSpG0xW7KqEmd8c,1065
transformers/models/fuyu/__pycache__/__init__.cpython-312.pyc,,
transformers/models/fuyu/__pycache__/configuration_fuyu.cpython-312.pyc,,
transformers/models/fuyu/__pycache__/image_processing_fuyu.cpython-312.pyc,,
transformers/models/fuyu/__pycache__/modeling_fuyu.cpython-312.pyc,,
transformers/models/fuyu/__pycache__/processing_fuyu.cpython-312.pyc,,
transformers/models/fuyu/configuration_fuyu.py,sha256=9YDe0lk2Ipss7hxKLK2TnvadXiA7G5Pphky2FiCUvQo,9985
transformers/models/fuyu/image_processing_fuyu.py,sha256=iWiavvkAdnFT7pd2xJVYu9QDs9NNGgGyOb5vG18FjoQ,33511
transformers/models/fuyu/modeling_fuyu.py,sha256=2CyeYCg2cangKyJNs--CrwVCkzXwT-zOF72UgP4DpPc,18932
transformers/models/fuyu/processing_fuyu.py,sha256=rLG3RdORZcRvoBz3pfBc_Dp6GmGoqNNSLxqe7NQ5UwQ,33176
transformers/models/gemma/__init__.py,sha256=xXoIfeCXNQOEnARxU3QucfH5mn-a_AE4wp69YkykT50,1111
transformers/models/gemma/__pycache__/__init__.cpython-312.pyc,,
transformers/models/gemma/__pycache__/configuration_gemma.cpython-312.pyc,,
transformers/models/gemma/__pycache__/modeling_flax_gemma.cpython-312.pyc,,
transformers/models/gemma/__pycache__/modeling_gemma.cpython-312.pyc,,
transformers/models/gemma/__pycache__/modular_gemma.cpython-312.pyc,,
transformers/models/gemma/__pycache__/tokenization_gemma.cpython-312.pyc,,
transformers/models/gemma/__pycache__/tokenization_gemma_fast.cpython-312.pyc,,
transformers/models/gemma/configuration_gemma.py,sha256=NutpaPy-FlgPcNt3XToZhKjUlybm3yEFtCMIyC6pcWo,8370
transformers/models/gemma/modeling_flax_gemma.py,sha256=9hIEDz0Y8Ld1nk6s5spdv1tZmwsq5Dv12AwkKOYP0LY,32416
transformers/models/gemma/modeling_gemma.py,sha256=_kLogkYPKnPpvc8TpPeMjsD-b0g3pgCUPEP3RhZHdEU,48500
transformers/models/gemma/modular_gemma.py,sha256=JO2XZ8bXhPJb1qmdto7yN2jrqvnrUGnmtP66AxTVwGE,23392
transformers/models/gemma/tokenization_gemma.py,sha256=uNYpw60r1EYnNaHZF-mm19inSbaxlHw_VStjGyU3jl4,14166
transformers/models/gemma/tokenization_gemma_fast.py,sha256=7JHnl4sHyWUdaFv_j7__HxauwKNnkyqXI37dmFUyhUs,8314
transformers/models/gemma2/__init__.py,sha256=H0jWJX-AcGRTjdzkGJagKnjB6GnpqVUG4ODFhMF9OWM,993
transformers/models/gemma2/__pycache__/__init__.cpython-312.pyc,,
transformers/models/gemma2/__pycache__/configuration_gemma2.cpython-312.pyc,,
transformers/models/gemma2/__pycache__/modeling_gemma2.cpython-312.pyc,,
transformers/models/gemma2/__pycache__/modular_gemma2.cpython-312.pyc,,
transformers/models/gemma2/configuration_gemma2.py,sha256=0fOT58Fi96XQzSVow2grkCvOoDp8Yl_Ix-TDAqWzfcU,9322
transformers/models/gemma2/modeling_gemma2.py,sha256=6eFxIkay8-QZRpkbijSfOidf4MKJM9d1cHP9TwqW5wE,56300
transformers/models/gemma2/modular_gemma2.py,sha256=c3opjEqeYphpP2dCNNoLMO1hmlqy6eBx6q3RE4PScHc,34932
transformers/models/git/__init__.py,sha256=jY1iLd7UMOmcCfrKgzoUJawLa0DQ55wHN26L09YSwhc,1021
transformers/models/git/__pycache__/__init__.cpython-312.pyc,,
transformers/models/git/__pycache__/configuration_git.cpython-312.pyc,,
transformers/models/git/__pycache__/modeling_git.cpython-312.pyc,,
transformers/models/git/__pycache__/processing_git.cpython-312.pyc,,
transformers/models/git/configuration_git.py,sha256=avdu5gPUzKcJfphFgf5d-QIu4RFjUtQZSKYIFDzADEQ,10431
transformers/models/git/modeling_git.py,sha256=ypi5xDeKJfom1wLqjZXJZmrunZzHJSEzt6zs9gtrQKI,73582
transformers/models/git/processing_git.py,sha256=1uGrwlR0D2-Z-8TslVcrSyCuwVrb2uk-chdG0yJDW48,6876
transformers/models/glm/__init__.py,sha256=fIafw6FAflbbeG_nEM_VPJyMJHnu_NbWHTHjECIAvIs,987
transformers/models/glm/__pycache__/__init__.cpython-312.pyc,,
transformers/models/glm/__pycache__/configuration_glm.cpython-312.pyc,,
transformers/models/glm/__pycache__/modeling_glm.cpython-312.pyc,,
transformers/models/glm/__pycache__/modular_glm.cpython-312.pyc,,
transformers/models/glm/configuration_glm.py,sha256=IO_5O35BHXTvGL_W3mmXjsMFdSiO80QwJ2f5U_m8MTs,7529
transformers/models/glm/modeling_glm.py,sha256=ByyUq5rpVWoFAPChMLL1h-OLnrZKY0zzH6VgoD2zLRE,48754
transformers/models/glm/modular_glm.py,sha256=rOSXBsyECZhENwaJ8M9bBxJ1NK2Lwv7POZu67Uu3xgQ,4093
transformers/models/glpn/__init__.py,sha256=YYoaugUj0un_FnfusrkzFfT_UtvUJEjMDaRDS8IcYAE,1073
transformers/models/glpn/__pycache__/__init__.cpython-312.pyc,,
transformers/models/glpn/__pycache__/configuration_glpn.cpython-312.pyc,,
transformers/models/glpn/__pycache__/feature_extraction_glpn.cpython-312.pyc,,
transformers/models/glpn/__pycache__/image_processing_glpn.cpython-312.pyc,,
transformers/models/glpn/__pycache__/modeling_glpn.cpython-312.pyc,,
transformers/models/glpn/configuration_glpn.py,sha256=FmxBJ1zoC4IDy68CV6eiHrhbzzI9bt0e0lscsZNShFM,5998
transformers/models/glpn/feature_extraction_glpn.py,sha256=-LIWcn0jmu6Wgk05foZGFGZLrS8dnCknF8uENaHFzfE,1209
transformers/models/glpn/image_processing_glpn.py,sha256=h_0tMBDMLGmM8QLKkYC6G3sSTiCjIDhGG25tZFUuiZo,12691
transformers/models/glpn/modeling_glpn.py,sha256=ifTKy0R9ztGbcm0PKyxsene9t4mesTM4oJ_ULBsdHf8,31490
transformers/models/got_ocr2/__init__.py,sha256=ECwCR2s-uCWOmtAQaqTbuc1qBE-8Lctbu508j1miD2Q,1088
transformers/models/got_ocr2/__pycache__/__init__.cpython-312.pyc,,
transformers/models/got_ocr2/__pycache__/configuration_got_ocr2.cpython-312.pyc,,
transformers/models/got_ocr2/__pycache__/image_processing_got_ocr2.cpython-312.pyc,,
transformers/models/got_ocr2/__pycache__/modeling_got_ocr2.cpython-312.pyc,,
transformers/models/got_ocr2/__pycache__/modular_got_ocr2.cpython-312.pyc,,
transformers/models/got_ocr2/__pycache__/processing_got_ocr2.cpython-312.pyc,,
transformers/models/got_ocr2/configuration_got_ocr2.py,sha256=-eGF_BppVzXVgH5-sqm0i-TP-mL2HDIQwwHC6ZBDOVg,9411
transformers/models/got_ocr2/image_processing_got_ocr2.py,sha256=OvRp9BLPMSCv2OV3iCXoIvgEDyN61pkrLZOQSjPQngU,24032
transformers/models/got_ocr2/modeling_got_ocr2.py,sha256=VbewN9vcLlwcEmuFQNlHJnoNpy_qcg2SvmnZV6fs2O8,43826
transformers/models/got_ocr2/modular_got_ocr2.py,sha256=3N2EFHEgBZC2OD5bwl9uUwlmxQC3gT5xREAG1Vx-rKI,45414
transformers/models/got_ocr2/processing_got_ocr2.py,sha256=3RjZsz7-9Ok09MvyYBEpuVno9JUhKVhCwhOsPZcxXew,14473
transformers/models/gpt2/__init__.py,sha256=NRi7aYu3gezDPsiXiiG6dgSpCMHSIvFpC3iI0w-JMA0,1182
transformers/models/gpt2/__pycache__/__init__.cpython-312.pyc,,
transformers/models/gpt2/__pycache__/configuration_gpt2.cpython-312.pyc,,
transformers/models/gpt2/__pycache__/modeling_flax_gpt2.cpython-312.pyc,,
transformers/models/gpt2/__pycache__/modeling_gpt2.cpython-312.pyc,,
transformers/models/gpt2/__pycache__/modeling_tf_gpt2.cpython-312.pyc,,
transformers/models/gpt2/__pycache__/tokenization_gpt2.cpython-312.pyc,,
transformers/models/gpt2/__pycache__/tokenization_gpt2_fast.cpython-312.pyc,,
transformers/models/gpt2/__pycache__/tokenization_gpt2_tf.cpython-312.pyc,,
transformers/models/gpt2/configuration_gpt2.py,sha256=kfBMy7KVmNF7RPyLnUnVyBSsN1pAP5pZdLO3UqF01zw,12028
transformers/models/gpt2/modeling_flax_gpt2.py,sha256=vKZO4-Suzi-gQqK3QK_LQhCaXAwsTl13C7Hm_83ODPo,32094
transformers/models/gpt2/modeling_gpt2.py,sha256=ISRC013M2uQIq--FNKSWv0WuFcRXkybrGisb3Eqnk5U,75868
transformers/models/gpt2/modeling_tf_gpt2.py,sha256=mzAkjgfiKgfZn07xpFCnnSTW5GepfFtI_mqhL6pEUTI,56639
transformers/models/gpt2/tokenization_gpt2.py,sha256=ktid1ESF1ddwlv6JznDnocuESJMl-knLrqlaIa_NHW8,13169
transformers/models/gpt2/tokenization_gpt2_fast.py,sha256=_9MdILprCNEYRzHFM9wWTt_3frnvxOYkZ45xxLVslmw,5281
transformers/models/gpt2/tokenization_gpt2_tf.py,sha256=u7aLICmUtkhZhe9THkS0qq8hBVbHmyuktWmUEUXF5W0,3865
transformers/models/gpt_bigcode/__init__.py,sha256=KQNb7PO57eZpP345wSbe_C3iL-N4VPscw1GY2mv81uE,1003
transformers/models/gpt_bigcode/__pycache__/__init__.cpython-312.pyc,,
transformers/models/gpt_bigcode/__pycache__/configuration_gpt_bigcode.cpython-312.pyc,,
transformers/models/gpt_bigcode/__pycache__/modeling_gpt_bigcode.cpython-312.pyc,,
transformers/models/gpt_bigcode/configuration_gpt_bigcode.py,sha256=HRmAGutvqlrQWtmsfGCsixHxhla7465UrgcFBCDt9hU,6311
transformers/models/gpt_bigcode/modeling_gpt_bigcode.py,sha256=6TkpmBFMZLrCi0FZdkUf6EMoeoee6SRMBlxCTeS-KDQ,65811
transformers/models/gpt_neo/__init__.py,sha256=b25qxianvucgAd3OxuI00Rr5324o-CRes0zrcEIOCZI,1036
transformers/models/gpt_neo/__pycache__/__init__.cpython-312.pyc,,
transformers/models/gpt_neo/__pycache__/configuration_gpt_neo.cpython-312.pyc,,
transformers/models/gpt_neo/__pycache__/modeling_flax_gpt_neo.cpython-312.pyc,,
transformers/models/gpt_neo/__pycache__/modeling_gpt_neo.cpython-312.pyc,,
transformers/models/gpt_neo/configuration_gpt_neo.py,sha256=5te0JrZ9pGYDXQzEgOc6nrgMUYXZaI-Q3lVCDIdN1aQ,11880
transformers/models/gpt_neo/modeling_flax_gpt_neo.py,sha256=MPxNyFQD0T7jQfOZgzVo9l-ipRVILoL0E48B4Bc0kdg,28160
transformers/models/gpt_neo/modeling_gpt_neo.py,sha256=Vzb9X9k3cUUnPnKjfaWxS__S-F8j9KyMrY_cBjAXqH0,59151
transformers/models/gpt_neox/__init__.py,sha256=6CL92CuqBTIDJ-YH_doFwb-oRylAffw7pwxedv3a-40,1043
transformers/models/gpt_neox/__pycache__/__init__.cpython-312.pyc,,
transformers/models/gpt_neox/__pycache__/configuration_gpt_neox.cpython-312.pyc,,
transformers/models/gpt_neox/__pycache__/modeling_gpt_neox.cpython-312.pyc,,
transformers/models/gpt_neox/__pycache__/modular_gpt_neox.cpython-312.pyc,,
transformers/models/gpt_neox/__pycache__/tokenization_gpt_neox_fast.cpython-312.pyc,,
transformers/models/gpt_neox/configuration_gpt_neox.py,sha256=U8a1aoDHP1Ng4GDyRRnVgf3iM2crkV8fqJzkfMPc1QU,10981
transformers/models/gpt_neox/modeling_gpt_neox.py,sha256=hhRL-Q6SL271tAroQPh6fwUSU_uaCmCEW9IWGGbhXUE,50993
transformers/models/gpt_neox/modular_gpt_neox.py,sha256=Amyn0rjVAn9R62GRoiJi7RrrxjFKpBpTGRIQx35B1to,34738
transformers/models/gpt_neox/tokenization_gpt_neox_fast.py,sha256=d3-37M7gx1M-gRMtRC7FRUReD4pF8AAuioITt8ZEy98,8985
transformers/models/gpt_neox_japanese/__init__.py,sha256=z4kbUmZSjE-Hs9ba8ul3Yncc9ZJy7ePufbwwRlfqWqw,1065
transformers/models/gpt_neox_japanese/__pycache__/__init__.cpython-312.pyc,,
transformers/models/gpt_neox_japanese/__pycache__/configuration_gpt_neox_japanese.cpython-312.pyc,,
transformers/models/gpt_neox_japanese/__pycache__/modeling_gpt_neox_japanese.cpython-312.pyc,,
transformers/models/gpt_neox_japanese/__pycache__/tokenization_gpt_neox_japanese.cpython-312.pyc,,
transformers/models/gpt_neox_japanese/configuration_gpt_neox_japanese.py,sha256=rugQmK2-6yU1q97fGoDiADoqvgQthnWWVvIEPDba-nI,9122
transformers/models/gpt_neox_japanese/modeling_gpt_neox_japanese.py,sha256=lkA7NAPLlvzCsg3rh9mekt-i_p_sdkBYB0SJ-xnSfj0,40982
transformers/models/gpt_neox_japanese/tokenization_gpt_neox_japanese.py,sha256=hD_GTFt0ONJAmi_hez8VeHhlALPO5tgVUgHASFADrgs,16250
transformers/models/gpt_sw3/__init__.py,sha256=-g6WlJ6EhhrJKCCsPf78cgvGD7oWvfeW9GBGBpW6wcM,958
transformers/models/gpt_sw3/__pycache__/__init__.cpython-312.pyc,,
transformers/models/gpt_sw3/__pycache__/tokenization_gpt_sw3.cpython-312.pyc,,
transformers/models/gpt_sw3/tokenization_gpt_sw3.py,sha256=un1AuiD5lYaO9alUHo2O3OuIA5l6BF2AzKrIsRbajvc,12502
transformers/models/gptj/__init__.py,sha256=rgFDJcsxcq1ytl7BTZthr7sSmaxqggSbvrIseycmE-s,1063
transformers/models/gptj/__pycache__/__init__.cpython-312.pyc,,
transformers/models/gptj/__pycache__/configuration_gptj.cpython-312.pyc,,
transformers/models/gptj/__pycache__/modeling_flax_gptj.cpython-312.pyc,,
transformers/models/gptj/__pycache__/modeling_gptj.cpython-312.pyc,,
transformers/models/gptj/__pycache__/modeling_tf_gptj.cpython-312.pyc,,
transformers/models/gptj/configuration_gptj.py,sha256=eqtAfhaoNMwytXXkhVWmO9KxhFZEo-jOAVBIvM7hs9s,8829
transformers/models/gptj/modeling_flax_gptj.py,sha256=llNypW7hkw-afhxaQeT5UZbxE6LZOeppoEofLT2yHQ8,28605
transformers/models/gptj/modeling_gptj.py,sha256=ND7p2Ns5v-cmEh-ZmD1R5ueWyrqpT-IUovcF2LhW5sI,62436
transformers/models/gptj/modeling_tf_gptj.py,sha256=Xo14eJ4V-8Sv5JIkOjt8OHIDU9UPGlu9OyVTY5a3e2A,48172
transformers/models/granite/__init__.py,sha256=GJDr9klPSZcxXbXjAdwKMM8nB96VAvxVGIP2N_FeTJQ,1633
transformers/models/granite/__pycache__/__init__.cpython-312.pyc,,
transformers/models/granite/__pycache__/configuration_granite.cpython-312.pyc,,
transformers/models/granite/__pycache__/modeling_granite.cpython-312.pyc,,
transformers/models/granite/__pycache__/modular_granite.cpython-312.pyc,,
transformers/models/granite/configuration_granite.py,sha256=DePkpV3S3B7w4MaRk4J_9q_94x_qwMy4lFinUvcdusA,9312
transformers/models/granite/modeling_granite.py,sha256=-dyEz8IVVhOmxmzT3_VISIUk1LX-DjsrxF4n3p-xLIk,41771
transformers/models/granite/modular_granite.py,sha256=8r2mu8anky4iSZSP3SMimcLmXpMjNgUPJIWzTevbvsI,12585
transformers/models/granitemoe/__init__.py,sha256=e4KKtNT7YFkYkPBfcS0VyhpT_1vF0JkR2qdYKPqRUcE,1001
transformers/models/granitemoe/__pycache__/__init__.cpython-312.pyc,,
transformers/models/granitemoe/__pycache__/configuration_granitemoe.cpython-312.pyc,,
transformers/models/granitemoe/__pycache__/modeling_granitemoe.cpython-312.pyc,,
transformers/models/granitemoe/configuration_granitemoe.py,sha256=YBrDyqDjMYoHfK33-MXFR9LsqTl-UhkPfwuTuYimp6A,9400
transformers/models/granitemoe/modeling_granitemoe.py,sha256=-7LA4Sse8Ubp_91kAb0LTuqxeWXgoE0USbOjNBH4_yg,64307
transformers/models/granitemoeshared/__init__.py,sha256=vmY98tLts1c_yvkLn9X-xk6CFtXIKskzYvFGMqQAskc,1013
transformers/models/granitemoeshared/__pycache__/__init__.cpython-312.pyc,,
transformers/models/granitemoeshared/__pycache__/configuration_granitemoeshared.cpython-312.pyc,,
transformers/models/granitemoeshared/__pycache__/modeling_granitemoeshared.cpython-312.pyc,,
transformers/models/granitemoeshared/__pycache__/modular_granitemoeshared.cpython-312.pyc,,
transformers/models/granitemoeshared/configuration_granitemoeshared.py,sha256=wh--8xO_eq3AwpaLUKnGPgmCEfKEn6UkVXDNoFYObhA,9829
transformers/models/granitemoeshared/modeling_granitemoeshared.py,sha256=SXd4PINs6Ang4Uqf0ou9lq9kZndkXuUYWJKg24r0Gk4,65786
transformers/models/granitemoeshared/modular_granitemoeshared.py,sha256=LZiVYveon9cwZ4QC5GW4-EoCGqbLEWajouMd8Duceug,13224
transformers/models/grounding_dino/__init__.py,sha256=hrTbNDHz5rgQfwPqtLzd8BGLCOgG1azEiWXR1ottlGk,1105
transformers/models/grounding_dino/__pycache__/__init__.cpython-312.pyc,,
transformers/models/grounding_dino/__pycache__/configuration_grounding_dino.cpython-312.pyc,,
transformers/models/grounding_dino/__pycache__/image_processing_grounding_dino.cpython-312.pyc,,
transformers/models/grounding_dino/__pycache__/modeling_grounding_dino.cpython-312.pyc,,
transformers/models/grounding_dino/__pycache__/processing_grounding_dino.cpython-312.pyc,,
transformers/models/grounding_dino/configuration_grounding_dino.py,sha256=PZ-enTohyIN1dsu3nr5CKPmkrpNEq4fdgJJdnoycRnA,14818
transformers/models/grounding_dino/image_processing_grounding_dino.py,sha256=tYOLe0G9BorNOsqAahZndTXcYEATsusHllY2Lzr9HsE,72223
transformers/models/grounding_dino/modeling_grounding_dino.py,sha256=0iWBsG5UqkSpZUb9o_UCFmYv6c-wpyA2oybIBaZtncU,134219
transformers/models/grounding_dino/processing_grounding_dino.py,sha256=TRB8iiJhDXOOYEt2eZ4MCZvqOypBHnmEDujnsasxiMk,14205
transformers/models/groupvit/__init__.py,sha256=vrJ-tBa1XOd1CloHhXKMCIlggMxOS4M7jCcqlLQxMo4,1037
transformers/models/groupvit/__pycache__/__init__.cpython-312.pyc,,
transformers/models/groupvit/__pycache__/configuration_groupvit.cpython-312.pyc,,
transformers/models/groupvit/__pycache__/modeling_groupvit.cpython-312.pyc,,
transformers/models/groupvit/__pycache__/modeling_tf_groupvit.cpython-312.pyc,,
transformers/models/groupvit/configuration_groupvit.py,sha256=78MBQzraYHdisuhkdgBndBOxcHiDd_Vs_2hu6ahPl2c,19161
transformers/models/groupvit/modeling_groupvit.py,sha256=vMyIH0B67NqEw7PMnTgBKJ3XdIav8F59jwHSsV04Ens,68290
transformers/models/groupvit/modeling_tf_groupvit.py,sha256=ykaNj-Xmqc7OH95-SSlexolku7ni6cA3xOhkpsMTCAE,90178
transformers/models/helium/__init__.py,sha256=b1Senw5Mr129rzZSd1sW6-Ies2kIAUHfplpzgGeuTFE,993
transformers/models/helium/__pycache__/__init__.cpython-312.pyc,,
transformers/models/helium/__pycache__/configuration_helium.cpython-312.pyc,,
transformers/models/helium/__pycache__/modeling_helium.cpython-312.pyc,,
transformers/models/helium/__pycache__/modular_helium.cpython-312.pyc,,
transformers/models/helium/configuration_helium.py,sha256=jpzl5Hj_oMIM-lx_LF4-xJUvmbH2NSyaMhL39G1-vuo,7374
transformers/models/helium/modeling_helium.py,sha256=vuQTyuOQ_VO0MzpD2KuWyFQi4cWVHsRWPdwWO44jGKM,48475
transformers/models/helium/modular_helium.py,sha256=wYe0RXeNnhhKaLoqntZbWT5mOIwPD6B3-JwCalAWSFM,5943
transformers/models/herbert/__init__.py,sha256=3i5hlRANc-OFP86y2qzb_OCWVjJQ9XQswiglh5KbU7Y,1003
transformers/models/herbert/__pycache__/__init__.cpython-312.pyc,,
transformers/models/herbert/__pycache__/tokenization_herbert.cpython-312.pyc,,
transformers/models/herbert/__pycache__/tokenization_herbert_fast.cpython-312.pyc,,
transformers/models/herbert/tokenization_herbert.py,sha256=rBagn0nnG4HO81Met6tYGlKQbdebCCNq6jrtEiWnD54,25067
transformers/models/herbert/tokenization_herbert_fast.py,sha256=oG2IA_5OGqVFgpJBWI1banP3v2R4db1YMSzDVrW0j5A,5963
transformers/models/hiera/__init__.py,sha256=b1kwKtpZVISJZ5Pri421uvH2v3IoRQ6XXHzxFOPHN-g,991
transformers/models/hiera/__pycache__/__init__.cpython-312.pyc,,
transformers/models/hiera/__pycache__/configuration_hiera.cpython-312.pyc,,
transformers/models/hiera/__pycache__/modeling_hiera.cpython-312.pyc,,
transformers/models/hiera/configuration_hiera.py,sha256=N2aU73lEM1cILh0EMY-NKdkkbRWMC-evWuE1x8dBOaU,9319
transformers/models/hiera/modeling_hiera.py,sha256=Xu3f6bNxcNZMgvKCU7hdFJFLfvOlqYBBmWwUTeWtucY,69784
transformers/models/hubert/__init__.py,sha256=ai560JtgkksShocy0zcDejelkRZnK4IZPVKaTHCOxPQ,1031
transformers/models/hubert/__pycache__/__init__.cpython-312.pyc,,
transformers/models/hubert/__pycache__/configuration_hubert.cpython-312.pyc,,
transformers/models/hubert/__pycache__/modeling_hubert.cpython-312.pyc,,
transformers/models/hubert/__pycache__/modeling_tf_hubert.cpython-312.pyc,,
transformers/models/hubert/configuration_hubert.py,sha256=QWOZSdSe0z3S0bbuY4VTAJli1_1JTjy30rZ5fAmG2YA,14938
transformers/models/hubert/modeling_hubert.py,sha256=M6oAW0fCeK1fWU9zspHjaZdgDQZdHBCcEJWIIw13ZS0,74469
transformers/models/hubert/modeling_tf_hubert.py,sha256=NvS59tLaZiFItSWBofz7oPqY_jhGHWYPeTQ5s2Z9MDw,70775
transformers/models/ibert/__init__.py,sha256=UMTcE54y6O9UNF8l9VV2rrTlJSAHooxeNeHNzPSgr_E,991
transformers/models/ibert/__pycache__/__init__.cpython-312.pyc,,
transformers/models/ibert/__pycache__/configuration_ibert.cpython-312.pyc,,
transformers/models/ibert/__pycache__/modeling_ibert.cpython-312.pyc,,
transformers/models/ibert/__pycache__/quant_modules.cpython-312.pyc,,
transformers/models/ibert/configuration_ibert.py,sha256=w0Do79Q-0Cpr-pqu4YjO2MYQgaqlCkBbBu1z11m7kBQ,7094
transformers/models/ibert/modeling_ibert.py,sha256=26mBZllMhlnKwa3OpcCwJykvocahMFr9LN8x7iwleoo,57105
transformers/models/ibert/quant_modules.py,sha256=ItU76CIx0XcZCPOR21dz99J9k5rK2fzffQz0jJCuNmM,30072
transformers/models/idefics/__init__.py,sha256=zc4m1Vd6-Szs7Urt0Ry6eUScpza8iD-QPG4cq4xX34g,1116
transformers/models/idefics/__pycache__/__init__.cpython-312.pyc,,
transformers/models/idefics/__pycache__/configuration_idefics.cpython-312.pyc,,
transformers/models/idefics/__pycache__/image_processing_idefics.cpython-312.pyc,,
transformers/models/idefics/__pycache__/modeling_idefics.cpython-312.pyc,,
transformers/models/idefics/__pycache__/modeling_tf_idefics.cpython-312.pyc,,
transformers/models/idefics/__pycache__/perceiver.cpython-312.pyc,,
transformers/models/idefics/__pycache__/perceiver_tf.cpython-312.pyc,,
transformers/models/idefics/__pycache__/processing_idefics.cpython-312.pyc,,
transformers/models/idefics/__pycache__/vision.cpython-312.pyc,,
transformers/models/idefics/__pycache__/vision_tf.cpython-312.pyc,,
transformers/models/idefics/configuration_idefics.py,sha256=4j7sAul74adsu3fXPiq34FePCqJJaafCg2dmHU9h_GU,15304
transformers/models/idefics/image_processing_idefics.py,sha256=z0TVQSuCYmxZwR5uC06vrrabhxdPaOzrpHfM7lgsZuM,7802
transformers/models/idefics/modeling_idefics.py,sha256=2fqMxjt67QG-aqx1Canz40B_LvW6QCOcAsQUB3kLVSk,82045
transformers/models/idefics/modeling_tf_idefics.py,sha256=r-J4WxqoV5dBwLIiH48jJDHewRAUwxsc8TZmjKqbPuA,80311
transformers/models/idefics/perceiver.py,sha256=uGv8FH2wZ-NO1EIaFclI1nkwUqaTA7i0PS9XxY7ivn0,9433
transformers/models/idefics/perceiver_tf.py,sha256=rYqXv9j6bmr4NyZLAV1MhVMiiIMV7RZ9CafybPtYc9I,10006
transformers/models/idefics/processing_idefics.py,sha256=7mpmmEN6XDJC1aanPLCb2V8wtupKdVQfJy1PZYq0xl4,23867
transformers/models/idefics/vision.py,sha256=EVQ5lOtdV00gK_3TAuLI4zUeHbw4zV1RdZNXZqUXXiQ,22493
transformers/models/idefics/vision_tf.py,sha256=Kf_PenRY1vhlBA62PvjdvUDyQTKIi30XqB_bMBN1Mrw,26010
transformers/models/idefics2/__init__.py,sha256=_nHEhSqYWOAwg_SKrfxmkyYAVQK-29zRoohqGI-rfbk,1081
transformers/models/idefics2/__pycache__/__init__.cpython-312.pyc,,
transformers/models/idefics2/__pycache__/configuration_idefics2.cpython-312.pyc,,
transformers/models/idefics2/__pycache__/image_processing_idefics2.cpython-312.pyc,,
transformers/models/idefics2/__pycache__/modeling_idefics2.cpython-312.pyc,,
transformers/models/idefics2/__pycache__/processing_idefics2.cpython-312.pyc,,
transformers/models/idefics2/configuration_idefics2.py,sha256=M4Oe8-smrRQQI4cI4_hIysHyR0tlHWZC-PPc8nwAgLQ,12018
transformers/models/idefics2/image_processing_idefics2.py,sha256=Tc-7riX5qlhWp1MOuWcvPHYH2uc8VmAfpgkBUVCGhnE,26451
transformers/models/idefics2/modeling_idefics2.py,sha256=hFNMPZ67O0Jc777sdsGq0byu3DmelQi3UYYB2uhjjs0,83339
transformers/models/idefics2/processing_idefics2.py,sha256=hCFlryHGDJVh4V1Q9jicYyoyg44WCADagCtq4wv0B4E,12728
transformers/models/idefics3/__init__.py,sha256=PT5AUxZiOzqyl7GIlwykcoRLTT0EvqX2UFACoyGUres,1081
transformers/models/idefics3/__pycache__/__init__.cpython-312.pyc,,
transformers/models/idefics3/__pycache__/configuration_idefics3.cpython-312.pyc,,
transformers/models/idefics3/__pycache__/image_processing_idefics3.cpython-312.pyc,,
transformers/models/idefics3/__pycache__/modeling_idefics3.cpython-312.pyc,,
transformers/models/idefics3/__pycache__/processing_idefics3.cpython-312.pyc,,
transformers/models/idefics3/configuration_idefics3.py,sha256=DVwzNwatzIql6aI6qw2eaeJlyzVYD08hbHDXfOu05Ag,8597
transformers/models/idefics3/image_processing_idefics3.py,sha256=9Dauo-8yxQh60eCue3LQxk7wTXQdfQ0aMbnrxmyKE9c,41317
transformers/models/idefics3/modeling_idefics3.py,sha256=ppN4WJV5TU3xfUFLlmF0sJNhXl3BmeQJNB0bSGbResA,64255
transformers/models/idefics3/processing_idefics3.py,sha256=ed0VrfZlV-86hJ6UUe9_R4bIoAcxJA0GdkBEEAT5so8,16708
transformers/models/ijepa/__init__.py,sha256=O0_Jqpy8kmorYC-x0QsoMYSHdqQt3E1j-UZGLQ9aCv0,991
transformers/models/ijepa/__pycache__/__init__.cpython-312.pyc,,
transformers/models/ijepa/__pycache__/configuration_ijepa.cpython-312.pyc,,
transformers/models/ijepa/__pycache__/modeling_ijepa.cpython-312.pyc,,
transformers/models/ijepa/__pycache__/modular_ijepa.cpython-312.pyc,,
transformers/models/ijepa/configuration_ijepa.py,sha256=fIhjoESR-XnZy-6gjk2IzQEU4Bji2fcB2kBYV9ircMQ,4819
transformers/models/ijepa/modeling_ijepa.py,sha256=-qehnHBbRfyzcAVS0HW5idrycjdnVgueyGBLRco9qhE,32081
transformers/models/ijepa/modular_ijepa.py,sha256=tFZY-SyewSjc0jGxvBk48oR1zngq_p8lfUNGig-4s2Y,10201
transformers/models/imagegpt/__init__.py,sha256=XxwI4UaVyyvTcGuJQGruvLi-dHHl8MdOvhAum3FXaGo,1089
transformers/models/imagegpt/__pycache__/__init__.cpython-312.pyc,,
transformers/models/imagegpt/__pycache__/configuration_imagegpt.cpython-312.pyc,,
transformers/models/imagegpt/__pycache__/feature_extraction_imagegpt.cpython-312.pyc,,
transformers/models/imagegpt/__pycache__/image_processing_imagegpt.cpython-312.pyc,,
transformers/models/imagegpt/__pycache__/modeling_imagegpt.cpython-312.pyc,,
transformers/models/imagegpt/configuration_imagegpt.py,sha256=e6ks4KifsBoGYsE-0mo5RF3mMcbWkoRH4EImnHrKPYo,8772
transformers/models/imagegpt/feature_extraction_imagegpt.py,sha256=w66K-BFee-magI7KhFo60l6DUNOD10Wa1bQlnjKka3w,1241
transformers/models/imagegpt/image_processing_imagegpt.py,sha256=ijrartcKJxQmKtGpTb-mxUdKvhky8ukbpDk84qAe1EM,14344
transformers/models/imagegpt/modeling_imagegpt.py,sha256=dKRD8rHg-vt3pN2Mi_WzMwTI7uS8chvRBRZcxS2rpGs,52204
transformers/models/informer/__init__.py,sha256=L-BwVQfdq5ve06VJJ-OnTh-m_YqSMNcpDQ1z6sbDtNI,997
transformers/models/informer/__pycache__/__init__.cpython-312.pyc,,
transformers/models/informer/__pycache__/configuration_informer.cpython-312.pyc,,
transformers/models/informer/__pycache__/modeling_informer.cpython-312.pyc,,
transformers/models/informer/configuration_informer.py,sha256=k-Zyf1sZFC_YmUWABPkEV5fv6qtNjKfECm5AWJqz9Q4,12443
transformers/models/informer/modeling_informer.py,sha256=T9V7oLXeq0U4B0_OU7plncjeCaWS86JY-bvu_1m9W84,101578
transformers/models/instructblip/__init__.py,sha256=gI7F0N1dRSYdZtTumtuoPcIJcuBI8PO4DEOQS4_nWuc,1048
transformers/models/instructblip/__pycache__/__init__.cpython-312.pyc,,
transformers/models/instructblip/__pycache__/configuration_instructblip.cpython-312.pyc,,
transformers/models/instructblip/__pycache__/modeling_instructblip.cpython-312.pyc,,
transformers/models/instructblip/__pycache__/processing_instructblip.cpython-312.pyc,,
transformers/models/instructblip/configuration_instructblip.py,sha256=f60ByFWdStqZJqxoJU6hO_mTK63Pdi-82grQWKwGDgo,15763
transformers/models/instructblip/modeling_instructblip.py,sha256=3080tA-hc-6BRhIwp3cZK8uObHMMBO5nB1l-vpmBtu8,75884
transformers/models/instructblip/processing_instructblip.py,sha256=54A9fcN9s8B9zobUyUtetnZiUMLCcEemi0aTLvqeHkA,10441
transformers/models/instructblipvideo/__init__.py,sha256=sTOPrPaq8f-igvxw5Bd9Tu9_bPMDDg2fmHk9sjjLpw0,2688
transformers/models/instructblipvideo/__pycache__/__init__.cpython-312.pyc,,
transformers/models/instructblipvideo/__pycache__/configuration_instructblipvideo.cpython-312.pyc,,
transformers/models/instructblipvideo/__pycache__/image_processing_instructblipvideo.cpython-312.pyc,,
transformers/models/instructblipvideo/__pycache__/modeling_instructblipvideo.cpython-312.pyc,,
transformers/models/instructblipvideo/__pycache__/modular_instructblipvideo.cpython-312.pyc,,
transformers/models/instructblipvideo/__pycache__/processing_instructblipvideo.cpython-312.pyc,,
transformers/models/instructblipvideo/configuration_instructblipvideo.py,sha256=GAufg0z0_cfHtdwaUpigSfH6Cn2ZKi8mpg3Y1u-tP68,16764
transformers/models/instructblipvideo/image_processing_instructblipvideo.py,sha256=riaa5KgJVBH7sCbW_lplNBSRR6K5Jz4mJTCvwA_JBvc,16610
transformers/models/instructblipvideo/modeling_instructblipvideo.py,sha256=fFHg9NqQpK0xmhbJk2q9FXo6It76N5hhFsJeCw2fbys,77574
transformers/models/instructblipvideo/modular_instructblipvideo.py,sha256=3L2FSVO1B0-3QNfnEqRjuBvxV6xrkuo6IzXW31gkLQY,23005
transformers/models/instructblipvideo/processing_instructblipvideo.py,sha256=DPpL4g4Mw1TGB_DoUPiqGSaN3nNo8ri0qWgRvSq27bY,11108
transformers/models/jamba/__init__.py,sha256=zN7Rmr--d5GCEJzMA7gxIz-BYFydPN3cyuif85YU0Fk,991
transformers/models/jamba/__pycache__/__init__.cpython-312.pyc,,
transformers/models/jamba/__pycache__/configuration_jamba.cpython-312.pyc,,
transformers/models/jamba/__pycache__/modeling_jamba.cpython-312.pyc,,
transformers/models/jamba/configuration_jamba.py,sha256=ZRM9HpnSRP8lupWix3yq_VtMVl-u8C20EPAFNXgIWwc,11739
transformers/models/jamba/modeling_jamba.py,sha256=mI9FgqeFXe3SXBUBM4KHx6Xr9YDFuox0VisHnSXrUjw,81483
transformers/models/jetmoe/__init__.py,sha256=zhqtP2ZDCCl3Fp3VBnnuaA044Ztbh7fsUKogAKABOt0,993
transformers/models/jetmoe/__pycache__/__init__.cpython-312.pyc,,
transformers/models/jetmoe/__pycache__/configuration_jetmoe.cpython-312.pyc,,
transformers/models/jetmoe/__pycache__/modeling_jetmoe.cpython-312.pyc,,
transformers/models/jetmoe/configuration_jetmoe.py,sha256=xp-1vQy18spKEumF7RJgpDrv1dkbWwTiKDlWDIBf2KM,6804
transformers/models/jetmoe/modeling_jetmoe.py,sha256=cV8gzv_LP-9NlEsi-HhyFnd4vjZ2XZ4XlF1dPTIpnek,68375
transformers/models/kosmos2/__init__.py,sha256=Ow8cLelhxl6fm5XvXzNQtPLt1xjIdVmGUwz5NoVVVto,1033
transformers/models/kosmos2/__pycache__/__init__.cpython-312.pyc,,
transformers/models/kosmos2/__pycache__/configuration_kosmos2.cpython-312.pyc,,
transformers/models/kosmos2/__pycache__/modeling_kosmos2.cpython-312.pyc,,
transformers/models/kosmos2/__pycache__/processing_kosmos2.cpython-312.pyc,,
transformers/models/kosmos2/configuration_kosmos2.py,sha256=E4I_iIyhD0dgA0vsI4uqEGifNGFd1YbfJu_GO2pmnEE,11880
transformers/models/kosmos2/modeling_kosmos2.py,sha256=I3uEWiLUXQ65D0sKmnJYQZwRLsmPdZOQxRq-05E3Wjs,98418
transformers/models/kosmos2/processing_kosmos2.py,sha256=jIYEs55absoFBqedLfjd6Tp7t8g4lX78v-r5c7GOPFI,31494
transformers/models/layoutlm/__init__.py,sha256=Mv-k01_9_SxbADuSx2pWoNGBxgUe4IH15Kcg-vc_0OI,1124
transformers/models/layoutlm/__pycache__/__init__.cpython-312.pyc,,
transformers/models/layoutlm/__pycache__/configuration_layoutlm.cpython-312.pyc,,
transformers/models/layoutlm/__pycache__/modeling_layoutlm.cpython-312.pyc,,
transformers/models/layoutlm/__pycache__/modeling_tf_layoutlm.cpython-312.pyc,,
transformers/models/layoutlm/__pycache__/tokenization_layoutlm.cpython-312.pyc,,
transformers/models/layoutlm/__pycache__/tokenization_layoutlm_fast.cpython-312.pyc,,
transformers/models/layoutlm/configuration_layoutlm.py,sha256=xxDGkuaOKRhkkqlmME687WUHTTMlkpzh-8hVqIxrN04,9134
transformers/models/layoutlm/modeling_layoutlm.py,sha256=HQYodTM7NF6KmqbWPb1LmopwAGFTnAp23WFjMwyOGgU,61223
transformers/models/layoutlm/modeling_tf_layoutlm.py,sha256=P5lttgAuGDEVmDchmoYfFSG55FcFzlISQBpXnSb1Lq0,73365
transformers/models/layoutlm/tokenization_layoutlm.py,sha256=VmytJNpv_8tgHL6eoSnlAA_CuZKNVCP0YVeiH7XhFRE,21294
transformers/models/layoutlm/tokenization_layoutlm_fast.py,sha256=w98iEAqvfjQKbs_mke052pYTn2yIBe8MGe5SYJ0M0Ks,7824
transformers/models/layoutlmv2/__init__.py,sha256=1w_91TbhMawClC5t7Bzc43MTFfHykZktH6WI3xPAOSQ,1229
transformers/models/layoutlmv2/__pycache__/__init__.cpython-312.pyc,,
transformers/models/layoutlmv2/__pycache__/configuration_layoutlmv2.cpython-312.pyc,,
transformers/models/layoutlmv2/__pycache__/feature_extraction_layoutlmv2.cpython-312.pyc,,
transformers/models/layoutlmv2/__pycache__/image_processing_layoutlmv2.cpython-312.pyc,,
transformers/models/layoutlmv2/__pycache__/modeling_layoutlmv2.cpython-312.pyc,,
transformers/models/layoutlmv2/__pycache__/processing_layoutlmv2.cpython-312.pyc,,
transformers/models/layoutlmv2/__pycache__/tokenization_layoutlmv2.cpython-312.pyc,,
transformers/models/layoutlmv2/__pycache__/tokenization_layoutlmv2_fast.cpython-312.pyc,,
transformers/models/layoutlmv2/configuration_layoutlmv2.py,sha256=E9lC45QK-thRX8thYV3oqncBAqlE62QIG4boZ5XPKKw,10914
transformers/models/layoutlmv2/feature_extraction_layoutlmv2.py,sha256=F0wTQ0y6NVCgr3539d9BvmxY_cAaCTGgzmlJpRyw54o,1238
transformers/models/layoutlmv2/image_processing_layoutlmv2.py,sha256=ore7EoSB64mFlJ5uy9zA6FtSb28QbX9_ymz8yLUnjp8,13496
transformers/models/layoutlmv2/modeling_layoutlmv2.py,sha256=706aQE-mPMxGsHKbMHiJ_VGPrJWXPt0T0CIj6qkaRTw,62127
transformers/models/layoutlmv2/processing_layoutlmv2.py,sha256=r0cXp_U_SgfqFGvVklwV_65pYJdbP1oZgD9qfho_vU4,9328
transformers/models/layoutlmv2/tokenization_layoutlmv2.py,sha256=3zu1gnR70B_LGh44eHmS5giPSsEzFb6An8dtmsrb59Q,73219
transformers/models/layoutlmv2/tokenization_layoutlmv2_fast.py,sha256=SMekhJaPdJogdBEoP_64FLaiG9PtwZQ0rYA0TSHxvtA,38140
transformers/models/layoutlmv3/__init__.py,sha256=2iZqJqrr9RPk4_l3L-UMihFqDD0sdY-YYFIEUAHud3w,1271
transformers/models/layoutlmv3/__pycache__/__init__.cpython-312.pyc,,
transformers/models/layoutlmv3/__pycache__/configuration_layoutlmv3.cpython-312.pyc,,
transformers/models/layoutlmv3/__pycache__/feature_extraction_layoutlmv3.cpython-312.pyc,,
transformers/models/layoutlmv3/__pycache__/image_processing_layoutlmv3.cpython-312.pyc,,
transformers/models/layoutlmv3/__pycache__/modeling_layoutlmv3.cpython-312.pyc,,
transformers/models/layoutlmv3/__pycache__/modeling_tf_layoutlmv3.cpython-312.pyc,,
transformers/models/layoutlmv3/__pycache__/processing_layoutlmv3.cpython-312.pyc,,
transformers/models/layoutlmv3/__pycache__/tokenization_layoutlmv3.cpython-312.pyc,,
transformers/models/layoutlmv3/__pycache__/tokenization_layoutlmv3_fast.cpython-312.pyc,,
transformers/models/layoutlmv3/configuration_layoutlmv3.py,sha256=7GaoEBAjg3vJaj-j6OxDCaKOpLpcgJVK5nXIPoZXzcE,13261
transformers/models/layoutlmv3/feature_extraction_layoutlmv3.py,sha256=pETM5HZm6mKClv1_S5I032mMmyQ3c55p2izYTgx7glY,1238
transformers/models/layoutlmv3/image_processing_layoutlmv3.py,sha256=Tanssi6TwE-1aChEnL7JdaNaDqU7496csm0_icY89Z0,18365
transformers/models/layoutlmv3/modeling_layoutlmv3.py,sha256=Ucn5-mzOaTZu9DSsbKcfHUt1T-GNiBxytpLYiXrJXVA,60584
transformers/models/layoutlmv3/modeling_tf_layoutlmv3.py,sha256=LN8SP7lOW-NPLyf2WydOha4ghqNqIyrBSUI9ezvqW1g,76978
transformers/models/layoutlmv3/processing_layoutlmv3.py,sha256=-9MWqD-yhk6MI6CCQVJ0pyVa-cNgI_Ut7lLy8reyND0,9179
transformers/models/layoutlmv3/tokenization_layoutlmv3.py,sha256=7kqYcH_Qwt53fsd1exkTCsPdwbdHuy4aZbyOOvccyo0,73227
transformers/models/layoutlmv3/tokenization_layoutlmv3_fast.py,sha256=TpfwrMfvTMZmN3Zsws_3lgr3aOXehMfFek_tXnBLaCs,39919
transformers/models/layoutxlm/__init__.py,sha256=djfI2YGJISwww_XDfyf4kCj3a_HiC6Hld1rlaHRtHPg,1047
transformers/models/layoutxlm/__pycache__/__init__.cpython-312.pyc,,
transformers/models/layoutxlm/__pycache__/processing_layoutxlm.cpython-312.pyc,,
transformers/models/layoutxlm/__pycache__/tokenization_layoutxlm.cpython-312.pyc,,
transformers/models/layoutxlm/__pycache__/tokenization_layoutxlm_fast.cpython-312.pyc,,
transformers/models/layoutxlm/processing_layoutxlm.py,sha256=4rk4UxnW0UaWAi931JoVCA6xTRQRWB2InoSswv7AepA,9278
transformers/models/layoutxlm/tokenization_layoutxlm.py,sha256=jm7sRc5e-578_yV3ufZby-Z_kyoe9yINBv6vwVdNcHE,58242
transformers/models/layoutxlm/tokenization_layoutxlm_fast.py,sha256=AyHviK71YozaXOwhp6jkq_Mjp4HA_woqRk4dVytFbtE,40620
transformers/models/led/__init__.py,sha256=KaOht9jIet9WQrPRli8DwD7q5fzTWsffxf7LK-sQuw4,1099
transformers/models/led/__pycache__/__init__.cpython-312.pyc,,
transformers/models/led/__pycache__/configuration_led.cpython-312.pyc,,
transformers/models/led/__pycache__/modeling_led.cpython-312.pyc,,
transformers/models/led/__pycache__/modeling_tf_led.cpython-312.pyc,,
transformers/models/led/__pycache__/tokenization_led.cpython-312.pyc,,
transformers/models/led/__pycache__/tokenization_led_fast.cpython-312.pyc,,
transformers/models/led/configuration_led.py,sha256=0L6BqCbtr1jaUDQCY5NW3AArfHO6Ccw5d1IoPN8dKdM,7445
transformers/models/led/modeling_led.py,sha256=r-xICO2LK4Rm-Q3S6Tuym2I9h-Y-tJr8b-KHxzucs_U,138234
transformers/models/led/modeling_tf_led.py,sha256=YG_yIkbczt_94ST4ZthmW02wrx4eg-pb2jphCHA-kps,123154
transformers/models/led/tokenization_led.py,sha256=ggi2e6cabWsfvQdGcbnkBxV35-4PT3OMIw6D90d6Gvg,19865
transformers/models/led/tokenization_led_fast.py,sha256=0dmDbm6pwKbJBkl8YFU4ZtqQObmRN6s5cG9NG9WThgs,14190
transformers/models/levit/__init__.py,sha256=ZY6VTbq_W51UT3b2ZHY8Odf4YyUQzt_QB0eHsHtPWAw,1077
transformers/models/levit/__pycache__/__init__.cpython-312.pyc,,
transformers/models/levit/__pycache__/configuration_levit.cpython-312.pyc,,
transformers/models/levit/__pycache__/feature_extraction_levit.cpython-312.pyc,,
transformers/models/levit/__pycache__/image_processing_levit.cpython-312.pyc,,
transformers/models/levit/__pycache__/modeling_levit.cpython-312.pyc,,
transformers/models/levit/configuration_levit.py,sha256=asZmZ6gnETFkfVHpHaQjI1y0n3iqNIqNjfsWwObyGQQ,5763
transformers/models/levit/feature_extraction_levit.py,sha256=SQOki6yb9tPoHOCi--xlFaUPdCA2GH3lAQ7HPklGzPI,1242
transformers/models/levit/image_processing_levit.py,sha256=CTM6y0V6jdirUeEybMesPXE6sal94EGSxcmSVoHFHeU,16602
transformers/models/levit/modeling_levit.py,sha256=d7hIISKEkpFbjhb14WbD6AV538_x5LYqXdUIK6Wn8Zk,29508
transformers/models/lilt/__init__.py,sha256=9XEq7kJwN0mKO469mR0mtlRUdljjq7V80gejpqb59K0,989
transformers/models/lilt/__pycache__/__init__.cpython-312.pyc,,
transformers/models/lilt/__pycache__/configuration_lilt.cpython-312.pyc,,
transformers/models/lilt/__pycache__/modeling_lilt.cpython-312.pyc,,
transformers/models/lilt/configuration_lilt.py,sha256=vbN5535Vj5EprsxbZONYIZiK7LaxRdQlO-roFLvv0pM,6721
transformers/models/lilt/modeling_lilt.py,sha256=w0KuPhiH9MHZaItjq7zlmmjrlqAPcTXHm0VVF1iD58I,52867
transformers/models/llama/__init__.py,sha256=k1HnOc4-BwvgSizE8E0IlrkCh_TVgv1XX8G-xozfgLo,1111
transformers/models/llama/__pycache__/__init__.cpython-312.pyc,,
transformers/models/llama/__pycache__/configuration_llama.cpython-312.pyc,,
transformers/models/llama/__pycache__/modeling_flax_llama.cpython-312.pyc,,
transformers/models/llama/__pycache__/modeling_llama.cpython-312.pyc,,
transformers/models/llama/__pycache__/tokenization_llama.cpython-312.pyc,,
transformers/models/llama/__pycache__/tokenization_llama_fast.cpython-312.pyc,,
transformers/models/llama/configuration_llama.py,sha256=wTRpxi3CxP52zFvFDm2y3iHjAtriWZRe8DMI8KxCn_Y,11986
transformers/models/llama/modeling_flax_llama.py,sha256=iGRuks9vOKsPC633ersl5iOR_fbrwE18sg6qZH-a0z8,30652
transformers/models/llama/modeling_llama.py,sha256=0xO9m5VlY48HgPvYFY3faf93M0s4Ri9QlF791kFAu_s,51822
transformers/models/llama/tokenization_llama.py,sha256=Tu78jFgHHansqXOf6Fvu6AkDD2RYnvUu4VnysR0cQq4,18666
transformers/models/llama/tokenization_llama_fast.py,sha256=zEuLIx-1wQTReiKzPclwSOC4dKwDEBwJCEDBrGbXaTs,11182
transformers/models/llava/__init__.py,sha256=h7TDiwhtiqDQbay9v760sbmBGM6yWs3J1tmnIr3PCys,1074
transformers/models/llava/__pycache__/__init__.cpython-312.pyc,,
transformers/models/llava/__pycache__/configuration_llava.cpython-312.pyc,,
transformers/models/llava/__pycache__/image_processing_llava.cpython-312.pyc,,
transformers/models/llava/__pycache__/image_processing_llava_fast.cpython-312.pyc,,
transformers/models/llava/__pycache__/modeling_llava.cpython-312.pyc,,
transformers/models/llava/__pycache__/processing_llava.cpython-312.pyc,,
transformers/models/llava/configuration_llava.py,sha256=pVdYSFxtERvYykJTVR8Wv2j-9ZbUInh0Zpnm7VhqY40,5807
transformers/models/llava/image_processing_llava.py,sha256=uR-R5lslWEWeiLKPF3NTaNlX3KF4JYxaV35hckUeVCk,21191
transformers/models/llava/image_processing_llava_fast.py,sha256=6kevDi1PFac0AmTnECYb2Py9wfB-qRVjDcIGzWrjL4M,7966
transformers/models/llava/modeling_llava.py,sha256=q06n617hJP0NrOXgnrCpPc8zJpDUz7S0jYcL7nmkRIk,25597
transformers/models/llava/processing_llava.py,sha256=iNg9qAekrYEt2vqyLo4DMm7QBzQ9odd1RoGCEKb1d8M,9305
transformers/models/llava_next/__init__.py,sha256=gyT3qcEjuxecgCiFoQoz-tf10ShqzfOL8IzPOhpjfto,1141
transformers/models/llava_next/__pycache__/__init__.cpython-312.pyc,,
transformers/models/llava_next/__pycache__/configuration_llava_next.cpython-312.pyc,,
transformers/models/llava_next/__pycache__/image_processing_llava_next.cpython-312.pyc,,
transformers/models/llava_next/__pycache__/image_processing_llava_next_fast.cpython-312.pyc,,
transformers/models/llava_next/__pycache__/modeling_llava_next.cpython-312.pyc,,
transformers/models/llava_next/__pycache__/processing_llava_next.cpython-312.pyc,,
transformers/models/llava_next/configuration_llava_next.py,sha256=PST2LDKPrvxdx8nJwJLRrjOCc7u1ci6PTq0tqUx_s5g,6797
transformers/models/llava_next/image_processing_llava_next.py,sha256=u4JgM-Xk39o0bOSrHQKr5E8TZPlnZ5lwFkkKLLsn8Xo,35810
transformers/models/llava_next/image_processing_llava_next_fast.py,sha256=Ls5x-YYEGLQXwYbWwMM0bUfuabXf_tGfyHV6M7afIjs,12376
transformers/models/llava_next/modeling_llava_next.py,sha256=4hjr7MRwnXZcxPVfYP9VjHEVv4a_5jbZ6m0zAlGTv4Y,36920
transformers/models/llava_next/processing_llava_next.py,sha256=svpfFJm_NdO3i2YY8DLAwlvFs1wQXWCtejYNig3CvlA,11549
transformers/models/llava_next_video/__init__.py,sha256=OGiUL7X9x0bzmsnZi0KA6Sl2ycalLQHkTgOpISYu3q8,1113
transformers/models/llava_next_video/__pycache__/__init__.cpython-312.pyc,,
transformers/models/llava_next_video/__pycache__/configuration_llava_next_video.cpython-312.pyc,,
transformers/models/llava_next_video/__pycache__/image_processing_llava_next_video.cpython-312.pyc,,
transformers/models/llava_next_video/__pycache__/modeling_llava_next_video.cpython-312.pyc,,
transformers/models/llava_next_video/__pycache__/modular_llava_next_video.cpython-312.pyc,,
transformers/models/llava_next_video/__pycache__/processing_llava_next_video.cpython-312.pyc,,
transformers/models/llava_next_video/configuration_llava_next_video.py,sha256=MpEibNJLFJctSNi_UUdNZ_HrWlu2cIfm0e8bhTBejKU,8241
transformers/models/llava_next_video/image_processing_llava_next_video.py,sha256=WzyPXHHzuvloL95LopXQ8Zmo2Lz6ree5qitDFn_RzgQ,20809
transformers/models/llava_next_video/modeling_llava_next_video.py,sha256=omJGEdtG81GzxpQjzuome9ItoobB33S8GtXhEXzhACk,45680
transformers/models/llava_next_video/modular_llava_next_video.py,sha256=z_YjkC0EpwmKCcb-vLvyyLeNKlyYCxBlaicWn_gd0Vs,29336
transformers/models/llava_next_video/processing_llava_next_video.py,sha256=FAjFMPX10l1iK2hgrQcaOfohj-f4gXUjiMtImiTc01E,15326
transformers/models/llava_onevision/__init__.py,sha256=Eeg8yGcfdjCxwjSCg_zoXG48JG6gSYH8_aXBcOxQvnA,1218
transformers/models/llava_onevision/__pycache__/__init__.cpython-312.pyc,,
transformers/models/llava_onevision/__pycache__/configuration_llava_onevision.cpython-312.pyc,,
transformers/models/llava_onevision/__pycache__/image_processing_llava_onevision.cpython-312.pyc,,
transformers/models/llava_onevision/__pycache__/image_processing_llava_onevision_fast.cpython-312.pyc,,
transformers/models/llava_onevision/__pycache__/modeling_llava_onevision.cpython-312.pyc,,
transformers/models/llava_onevision/__pycache__/modular_llava_onevision.cpython-312.pyc,,
transformers/models/llava_onevision/__pycache__/processing_llava_onevision.cpython-312.pyc,,
transformers/models/llava_onevision/__pycache__/video_processing_llava_onevision.cpython-312.pyc,,
transformers/models/llava_onevision/configuration_llava_onevision.py,sha256=RlM7FIc9aHxTVzxs46SU8dRmoVCNNa1WcrIyWd3-H-s,8035
transformers/models/llava_onevision/image_processing_llava_onevision.py,sha256=_Omg50KGOfqfoHBMJGxemS6hXkJ4ogjNeNh7TfSzt-c,33413
transformers/models/llava_onevision/image_processing_llava_onevision_fast.py,sha256=fIcAHnHb8x70C29c4mq_USkNV1tAqSb0MxTLHdnD2_w,12958
transformers/models/llava_onevision/modeling_llava_onevision.py,sha256=QkJCISMzEJT9TZqvFVHYTSiUnQt7I0gbDX41sf7WF2g,43900
transformers/models/llava_onevision/modular_llava_onevision.py,sha256=jxJK82b0kVJja20yK039D3WHicIforgJ1pWOF-GX_dY,2231
transformers/models/llava_onevision/processing_llava_onevision.py,sha256=5No3E02JCV0iiZRhi2C5Ewr8f5cnoI56ZmevR-cCSVc,15938
transformers/models/llava_onevision/video_processing_llava_onevision.py,sha256=jPirIH9oL-YeWz78wjeONZo3M5svaqwwzq38i7_aejs,16276
transformers/models/longformer/__init__.py,sha256=vg5ScmyEX2D-xPfnxNNBhdj6-Xj0t3HoPmt709PQjTE,1134
transformers/models/longformer/__pycache__/__init__.cpython-312.pyc,,
transformers/models/longformer/__pycache__/configuration_longformer.cpython-312.pyc,,
transformers/models/longformer/__pycache__/modeling_longformer.cpython-312.pyc,,
transformers/models/longformer/__pycache__/modeling_tf_longformer.cpython-312.pyc,,
transformers/models/longformer/__pycache__/tokenization_longformer.cpython-312.pyc,,
transformers/models/longformer/__pycache__/tokenization_longformer_fast.cpython-312.pyc,,
transformers/models/longformer/configuration_longformer.py,sha256=PugPJuv12EETU77Unxdp3tmQ6vGVa9kpjgRPhChZiWo,8822
transformers/models/longformer/modeling_longformer.py,sha256=4B78g4uNVGU6PKfWJ1G1UB_9ZBM6LCcfz43BfSN4KG4,114116
transformers/models/longformer/modeling_tf_longformer.py,sha256=5YDMosU1eAxh613cCXPzlUVD4-kg-Rb0c60MMVjN9Ck,129648
transformers/models/longformer/tokenization_longformer.py,sha256=4oTvnk6y6Kgpw40mKMWuoXvX19wdetSA86M0cPw3TJ4,16833
transformers/models/longformer/tokenization_longformer_fast.py,sha256=ysIwuVAdWqTlirIcbCa7_SJTD8-tTZhr5Z9kVNDLg20,11243
transformers/models/longt5/__init__.py,sha256=TzoI1JGkvJIf9NlHDQY8_EUuW-upkQZ23wh_8Urtet0,1033
transformers/models/longt5/__pycache__/__init__.cpython-312.pyc,,
transformers/models/longt5/__pycache__/configuration_longt5.cpython-312.pyc,,
transformers/models/longt5/__pycache__/modeling_flax_longt5.cpython-312.pyc,,
transformers/models/longt5/__pycache__/modeling_longt5.cpython-312.pyc,,
transformers/models/longt5/configuration_longt5.py,sha256=s6wNUR2DwLe7sVM-3stgsGwlwZ2wLqapd2nhYORCSlw,8107
transformers/models/longt5/modeling_flax_longt5.py,sha256=A8cA-Tk2m6QxXvcE2F0rw0AMtKL3t7i7r0rgclhfqvk,105769
transformers/models/longt5/modeling_longt5.py,sha256=F4RhTfE1tkzOq1HugIvvKbSgNLHk3V8Yg7-BozqYxOU,112370
transformers/models/luke/__init__.py,sha256=YQL403sV6tk5t8sjvi-4hgvx1rvyThx45l7S4T4xpEE,1026
transformers/models/luke/__pycache__/__init__.cpython-312.pyc,,
transformers/models/luke/__pycache__/configuration_luke.cpython-312.pyc,,
transformers/models/luke/__pycache__/modeling_luke.cpython-312.pyc,,
transformers/models/luke/__pycache__/tokenization_luke.cpython-312.pyc,,
transformers/models/luke/configuration_luke.py,sha256=Th-ke2nWmSnDRZNu_0_DnYFbAzzzEj6Gct6YCR-nlb0,6620
transformers/models/luke/modeling_luke.py,sha256=NppKcmXpqWoxMQFzceWfs1jtPY_32NlDO9qRqvopxXo,104090
transformers/models/luke/tokenization_luke.py,sha256=cS-ZLe07IvCpA2tMtMJhrI-G6mTEYC9wpyhxPQ0v67E,85678
transformers/models/lxmert/__init__.py,sha256=iUyLmlBuiz_av7H5ghaQB4RNbpw275N7wwdmiiV0PAc,1114
transformers/models/lxmert/__pycache__/__init__.cpython-312.pyc,,
transformers/models/lxmert/__pycache__/configuration_lxmert.cpython-312.pyc,,
transformers/models/lxmert/__pycache__/modeling_lxmert.cpython-312.pyc,,
transformers/models/lxmert/__pycache__/modeling_tf_lxmert.cpython-312.pyc,,
transformers/models/lxmert/__pycache__/tokenization_lxmert.cpython-312.pyc,,
transformers/models/lxmert/__pycache__/tokenization_lxmert_fast.cpython-312.pyc,,
transformers/models/lxmert/configuration_lxmert.py,sha256=ScIeG49SSLVuwrj25wKY85troVtNniZdjnuZcrewx1g,8934
transformers/models/lxmert/modeling_lxmert.py,sha256=kuNXogwnKfJscdcjOPUAgzBRbyXu84bkFqK6N5cJy2U,66100
transformers/models/lxmert/modeling_tf_lxmert.py,sha256=TrJItgAEXtQx_TCHmOTUHUyMvmxp3DyASG6bwYglj50,72777
transformers/models/lxmert/tokenization_lxmert.py,sha256=BbNrfRfJZr1Y7uGS6XOh9CJkpP0AJ3e_tq-636nXaBg,21316
transformers/models/lxmert/tokenization_lxmert_fast.py,sha256=eujDqy2iYa1Tz7a5WNBYgaRa9f6yh2B00jSH6h4Ez6o,7756
transformers/models/m2m_100/__init__.py,sha256=0uPov299rgQmMwwSyM_m0yGFejP5djgaUY37GkNGnC8,1035
transformers/models/m2m_100/__pycache__/__init__.cpython-312.pyc,,
transformers/models/m2m_100/__pycache__/configuration_m2m_100.cpython-312.pyc,,
transformers/models/m2m_100/__pycache__/modeling_m2m_100.cpython-312.pyc,,
transformers/models/m2m_100/__pycache__/tokenization_m2m_100.cpython-312.pyc,,
transformers/models/m2m_100/configuration_m2m_100.py,sha256=CsVEF-ussIdQvMYTwbtLzEdDuEko_EdPN5SL83JH3Ss,13411
transformers/models/m2m_100/modeling_m2m_100.py,sha256=eCvND0xFN3pUBtq3V6o6tdfOF0zCYXHNVb9uaozjUAE,79232
transformers/models/m2m_100/tokenization_m2m_100.py,sha256=7sTz3PU6MkKKy6RuAQg5rhHKKfPyB_aSj-rqx_L4vYU,16353
transformers/models/mamba/__init__.py,sha256=4oGJySQbwoALRGVWMEwXBm0A6fhKsr4Raly46a5g1G0,991
transformers/models/mamba/__pycache__/__init__.cpython-312.pyc,,
transformers/models/mamba/__pycache__/configuration_mamba.cpython-312.pyc,,
transformers/models/mamba/__pycache__/modeling_mamba.cpython-312.pyc,,
transformers/models/mamba/configuration_mamba.py,sha256=jLOixlXsTzDBM_q3rV0cVNg8CU8RSqPf2Um7vlm2tho,7432
transformers/models/mamba/modeling_mamba.py,sha256=13NpUfQipxbHIJgjjhIv2yzpdkFKKwkg21cCv-iwbfw,38131
transformers/models/mamba2/__init__.py,sha256=Ui4j-I2cnPEEszkzRTLSUW42SE4Qg1YTuW6hGeaOFZg,993
transformers/models/mamba2/__pycache__/__init__.cpython-312.pyc,,
transformers/models/mamba2/__pycache__/configuration_mamba2.cpython-312.pyc,,
transformers/models/mamba2/__pycache__/modeling_mamba2.cpython-312.pyc,,
transformers/models/mamba2/configuration_mamba2.py,sha256=-aPC9WlLdLSiPtfiOmsuILLMiiBTL2WrBQkYKPycShw,7918
transformers/models/mamba2/modeling_mamba2.py,sha256=g2hdeFwN9leP78qNWi7TgtcOZR_8g8ALj0C2SAegT9s,51930
transformers/models/marian/__init__.py,sha256=Yg8jbvM0Hf6WXua0__v_G-34dvG6zFib5R5e_qHtmYM,1110
transformers/models/marian/__pycache__/__init__.cpython-312.pyc,,
transformers/models/marian/__pycache__/configuration_marian.cpython-312.pyc,,
transformers/models/marian/__pycache__/modeling_flax_marian.cpython-312.pyc,,
transformers/models/marian/__pycache__/modeling_marian.cpython-312.pyc,,
transformers/models/marian/__pycache__/modeling_tf_marian.cpython-312.pyc,,
transformers/models/marian/__pycache__/tokenization_marian.cpython-312.pyc,,
transformers/models/marian/configuration_marian.py,sha256=Dv_60lB0pJolwdN-1onmOp_dLaTEIHUE2FvujXcjShs,18377
transformers/models/marian/modeling_flax_marian.py,sha256=mdLvaEt8x5AX_EHipw6CVpuIxKAAg-Efn0GuSkaTw1c,64343
transformers/models/marian/modeling_marian.py,sha256=izRXWEhLS311rpZJdp4YY_OtTL7PVnWKuyfWCbKupC8,79633
transformers/models/marian/modeling_tf_marian.py,sha256=pGmWoXmGt1bj-hWyy1utMZC1BX90_wrkg9xuJFd3lS0,72756
transformers/models/marian/tokenization_marian.py,sha256=uM-1KmZ_WXyeMEGXuz3zobsOthMPMB32DUhcsc6h-So,16844
transformers/models/markuplm/__init__.py,sha256=PyhrxFsms-oD4SOBO5j3t2mIPLN3PHjKBjTGaUTITMY,1170
transformers/models/markuplm/__pycache__/__init__.cpython-312.pyc,,
transformers/models/markuplm/__pycache__/configuration_markuplm.cpython-312.pyc,,
transformers/models/markuplm/__pycache__/feature_extraction_markuplm.cpython-312.pyc,,
transformers/models/markuplm/__pycache__/modeling_markuplm.cpython-312.pyc,,
transformers/models/markuplm/__pycache__/processing_markuplm.cpython-312.pyc,,
transformers/models/markuplm/__pycache__/tokenization_markuplm.cpython-312.pyc,,
transformers/models/markuplm/__pycache__/tokenization_markuplm_fast.cpython-312.pyc,,
transformers/models/markuplm/configuration_markuplm.py,sha256=70RVe4KsIBXJBvY3uEOBk83YoDaxC0NSh0GGYjZcfv8,7342
transformers/models/markuplm/feature_extraction_markuplm.py,sha256=5TaHlA8AsJQXC7tq0c2I5XDJalRdrfVMduE50H6ne8o,6449
transformers/models/markuplm/modeling_markuplm.py,sha256=34_MWgbEpSE4_zRXBoXORWYCQwSDcBlaHqNrRvWX6AI,57310
transformers/models/markuplm/processing_markuplm.py,sha256=WuabRmuYMRBgWn3y4aLlwx4Dff8NEnXmu7GNU41DGko,6383
transformers/models/markuplm/tokenization_markuplm.py,sha256=pTjbIQ4OqbRT-6kh6i_n8nKplClX7GYnLOWkdtnzJzg,70142
transformers/models/markuplm/tokenization_markuplm_fast.py,sha256=H9s2Wj1BTJtdV_GeilV1csAyIWgMxo0NoVPRoa1ke6U,43324
transformers/models/mask2former/__init__.py,sha256=6gmVc8RS8CDX2nkBzyySXTjdw61BJgjiIukresOTuFg,1051
transformers/models/mask2former/__pycache__/__init__.cpython-312.pyc,,
transformers/models/mask2former/__pycache__/configuration_mask2former.cpython-312.pyc,,
transformers/models/mask2former/__pycache__/image_processing_mask2former.cpython-312.pyc,,
transformers/models/mask2former/__pycache__/modeling_mask2former.cpython-312.pyc,,
transformers/models/mask2former/configuration_mask2former.py,sha256=S80wQDek68I0jIWZ2Y8DXjZyh-l3SVAwdhnAyYCHMiY,12375
transformers/models/mask2former/image_processing_mask2former.py,sha256=MMKOAXSXL_S3PozL5RssgkHtDWhrNB2iP2NRN1OOrKw,57296
transformers/models/mask2former/modeling_mask2former.py,sha256=QpISz6oA-bpNoayW5pAd57m0ewIS7s_bH2u_iji5hn4,122040
transformers/models/maskformer/__init__.py,sha256=gNY7kNWBY38tpjXbqjijMoGOOQBzju9Woxs7svG09es,1190
transformers/models/maskformer/__pycache__/__init__.cpython-312.pyc,,
transformers/models/maskformer/__pycache__/configuration_maskformer.cpython-312.pyc,,
transformers/models/maskformer/__pycache__/configuration_maskformer_swin.cpython-312.pyc,,
transformers/models/maskformer/__pycache__/feature_extraction_maskformer.cpython-312.pyc,,
transformers/models/maskformer/__pycache__/image_processing_maskformer.cpython-312.pyc,,
transformers/models/maskformer/__pycache__/modeling_maskformer.cpython-312.pyc,,
transformers/models/maskformer/__pycache__/modeling_maskformer_swin.cpython-312.pyc,,
transformers/models/maskformer/configuration_maskformer.py,sha256=GRAhXC4xsMk2-SGRksQVBEeVz11ZhKLZLetwfxprTl0,10293
transformers/models/maskformer/configuration_maskformer_swin.py,sha256=HbFVMCwZaLJjr8HgN1tDd4z7NBhH3vQBkdPpKQSOF9I,7253
transformers/models/maskformer/feature_extraction_maskformer.py,sha256=8JchapZ-d7HrbJxFPnd1v9eQLxUWGcJMY1IsS7twwHA,1257
transformers/models/maskformer/image_processing_maskformer.py,sha256=RluOw5hWmpuNB509A30snwPerMM8Cx-c9uWEdLm1rZ0,58190
transformers/models/maskformer/modeling_maskformer.py,sha256=87INxscMdOPR3HqlOGQHQB59dsLb3lKO_cag5_zSV8o,91015
transformers/models/maskformer/modeling_maskformer_swin.py,sha256=mBuo1xCCfIlYpiOjRwnfpPPtthztzT3W5XN4gFAKp-8,43072
transformers/models/mbart/__init__.py,sha256=VefKwprf7OVOTgkXowKV2hT8X3mM369sRJXDY5a49ig,1148
transformers/models/mbart/__pycache__/__init__.cpython-312.pyc,,
transformers/models/mbart/__pycache__/configuration_mbart.cpython-312.pyc,,
transformers/models/mbart/__pycache__/modeling_flax_mbart.cpython-312.pyc,,
transformers/models/mbart/__pycache__/modeling_mbart.cpython-312.pyc,,
transformers/models/mbart/__pycache__/modeling_tf_mbart.cpython-312.pyc,,
transformers/models/mbart/__pycache__/tokenization_mbart.cpython-312.pyc,,
transformers/models/mbart/__pycache__/tokenization_mbart_fast.cpython-312.pyc,,
transformers/models/mbart/configuration_mbart.py,sha256=e4Bz1c8ZW2BKGPQPsJErgIT-ZJTJoAmZTQl8x-Np2vo,18209
transformers/models/mbart/modeling_flax_mbart.py,sha256=lXjbKiHARX8jIqqD05Jx8B9nuqpaRecdwKRQbUQ-9kk,75287
transformers/models/mbart/modeling_mbart.py,sha256=VWs_eJVYBwPa7jHuToG7RpsTcWBdT1H2w_kRq5n89Y0,101917
transformers/models/mbart/modeling_tf_mbart.py,sha256=huZgEbvXI_D_0bORe1uAM71BWbgSqxPmBjexQOMQFYM,74283
transformers/models/mbart/tokenization_mbart.py,sha256=nFSUWWKpbYunRMV69t-w6ib__vqJTQskBQMl6KiuhkM,14137
transformers/models/mbart/tokenization_mbart_fast.py,sha256=q2Ol2COBvP-Cmi7Uzdet1ofbYC3l8FWVfxcir5MwBmA,11032
transformers/models/mbart50/__init__.py,sha256=9ukVFi1NqU3OoJcCJ-iKpJUZiu-K0t8yINuJHGltup0,1003
transformers/models/mbart50/__pycache__/__init__.cpython-312.pyc,,
transformers/models/mbart50/__pycache__/tokenization_mbart50.cpython-312.pyc,,
transformers/models/mbart50/__pycache__/tokenization_mbart50_fast.cpython-312.pyc,,
transformers/models/mbart50/tokenization_mbart50.py,sha256=nOOpupssZR8xpbBOGae57GMDfiTOc16SDIV_u9WeQnU,16340
transformers/models/mbart50/tokenization_mbart50_fast.py,sha256=oQj0N-DTI33kyykNqyJ9VJLw0SGxdgrdRXa-QarlfaM,11631
transformers/models/megatron_bert/__init__.py,sha256=u1UIYjQlrfHcy81i2FzehRDJpt6KNfNJ4AePQYKgwOU,1007
transformers/models/megatron_bert/__pycache__/__init__.cpython-312.pyc,,
transformers/models/megatron_bert/__pycache__/configuration_megatron_bert.cpython-312.pyc,,
transformers/models/megatron_bert/__pycache__/modeling_megatron_bert.cpython-312.pyc,,
transformers/models/megatron_bert/configuration_megatron_bert.py,sha256=amM48KV4ndrBgCKY4opvk10mGucRngBb_mMQDO_RPiI,6501
transformers/models/megatron_bert/modeling_megatron_bert.py,sha256=M7FEsDKcSwkdQUOx8O09QTnCGzHTSUZbwgejtRcQTss,82720
transformers/models/megatron_gpt2/__init__.py,sha256=WycFl9cUevoXIBhB76qKtnNRIPMk2LoTDkmkfAfOy9M,630
transformers/models/megatron_gpt2/__pycache__/__init__.cpython-312.pyc,,
transformers/models/megatron_gpt2/__pycache__/checkpoint_reshaping_and_interoperability.cpython-312.pyc,,
transformers/models/megatron_gpt2/checkpoint_reshaping_and_interoperability.py,sha256=2yI0NmcgRXJ54yjDcug5NdtHNeyDb7UM66EFdhQoLaU,37444
transformers/models/mgp_str/__init__.py,sha256=Qb3mXPCrWbQ1ksMRYMeXorrva97OOFNr1zoy4YQg-9k,1073
transformers/models/mgp_str/__pycache__/__init__.cpython-312.pyc,,
transformers/models/mgp_str/__pycache__/configuration_mgp_str.cpython-312.pyc,,
transformers/models/mgp_str/__pycache__/modeling_mgp_str.cpython-312.pyc,,
transformers/models/mgp_str/__pycache__/processing_mgp_str.cpython-312.pyc,,
transformers/models/mgp_str/__pycache__/tokenization_mgp_str.cpython-312.pyc,,
transformers/models/mgp_str/configuration_mgp_str.py,sha256=HYlPZgcF71-Qr_TTba6rkf3P5qFm0dNJrfywOEE5DTU,5810
transformers/models/mgp_str/modeling_mgp_str.py,sha256=BdxKDPksoHrSC8PGtImWMEjGtyY0SNZjuSNkxPFj13k,22010
transformers/models/mgp_str/processing_mgp_str.py,sha256=Qm2OduvUf4Yq2nUYxdIEx9eMGSfujsEzxuVOL6A_CLI,9330
transformers/models/mgp_str/tokenization_mgp_str.py,sha256=8U0UW-dlrKNzwBqiPOdfR0ydmlULY1bcaZvhkwDmNuA,3808
transformers/models/mimi/__init__.py,sha256=VXRZ-D8-AyOYcmRGvSxhjwTYQcSNXcCXi5ubks6Qxhk,989
transformers/models/mimi/__pycache__/__init__.cpython-312.pyc,,
transformers/models/mimi/__pycache__/configuration_mimi.cpython-312.pyc,,
transformers/models/mimi/__pycache__/modeling_mimi.cpython-312.pyc,,
transformers/models/mimi/configuration_mimi.py,sha256=G9Il35tvzshXo694vG87mnVvI1NIDwYeDCFt8b1sUgM,11921
transformers/models/mimi/modeling_mimi.py,sha256=8uZ4q-IEycfS5iIWDD7YQZq-I6ibYZFuSNqivPEcRR4,83364
transformers/models/mistral/__init__.py,sha256=Gd3l8JZ-Oxe8fvqYKH1BW_GI4Pvc4sYy89_5h9hosFI,3248
transformers/models/mistral/__pycache__/__init__.cpython-312.pyc,,
transformers/models/mistral/__pycache__/configuration_mistral.cpython-312.pyc,,
transformers/models/mistral/__pycache__/modeling_flax_mistral.cpython-312.pyc,,
transformers/models/mistral/__pycache__/modeling_mistral.cpython-312.pyc,,
transformers/models/mistral/__pycache__/modeling_tf_mistral.cpython-312.pyc,,
transformers/models/mistral/__pycache__/modular_mistral.cpython-312.pyc,,
transformers/models/mistral/configuration_mistral.py,sha256=AG5uUJEupoUaLrjXWWLzUDegAJmKgITMrMdDltCLu_Q,7759
transformers/models/mistral/modeling_flax_mistral.py,sha256=M2Fio6tl63E0fATg077-CwLoT1iNV7pZWmquxNvOTAI,31682
transformers/models/mistral/modeling_mistral.py,sha256=9wSRmex8K03IlEAoY4HnjuXlahC_9V_P_2j6b8_x05g,53292
transformers/models/mistral/modeling_tf_mistral.py,sha256=zceqm2mQAV68J0S4gXyAwAQOAOS9rR1Yq2AZBw1KOuw,45013
transformers/models/mistral/modular_mistral.py,sha256=PXt0h10TWnPTDa8xNlArRlZsUfFk5vShbpvsJgSX7cc,16098
transformers/models/mixtral/__init__.py,sha256=K-r1Mh5wzRlbIfzTr9PSpvZqZPUwIgdXMCEBK7WOPn4,1890
transformers/models/mixtral/__pycache__/__init__.cpython-312.pyc,,
transformers/models/mixtral/__pycache__/configuration_mixtral.cpython-312.pyc,,
transformers/models/mixtral/__pycache__/modeling_mixtral.cpython-312.pyc,,
transformers/models/mixtral/__pycache__/modular_mixtral.cpython-312.pyc,,
transformers/models/mixtral/configuration_mixtral.py,sha256=Ng_UJLNl93XC2Xq_HbGSW8P4geA_NsYyb_9_PLU6moo,9112
transformers/models/mixtral/modeling_mixtral.py,sha256=SndZaAWBpt4YQd9UWyKMPIQQDFPHf1omKzcV9aFqbEI,64476
transformers/models/mixtral/modular_mixtral.py,sha256=CFeK9xHheuoWlnM03Vs74C6bfhiwYlLme7IB_WCalL0,24657
transformers/models/mllama/__init__.py,sha256=2lTGCiL6EZirXNcu4aKV7vSmv50iRsQnCV-c9sahNXg,1073
transformers/models/mllama/__pycache__/__init__.cpython-312.pyc,,
transformers/models/mllama/__pycache__/configuration_mllama.cpython-312.pyc,,
transformers/models/mllama/__pycache__/image_processing_mllama.cpython-312.pyc,,
transformers/models/mllama/__pycache__/modeling_mllama.cpython-312.pyc,,
transformers/models/mllama/__pycache__/processing_mllama.cpython-312.pyc,,
transformers/models/mllama/configuration_mllama.py,sha256=b6NUWeOFTa6Oz85ouPqsVRvRPp-E0aqq8fqCefhxPAg,18146
transformers/models/mllama/image_processing_mllama.py,sha256=8yCh4d7FFgoypEL8zaxGFz1OwL2gVBDSOYjcuDLASmk,37907
transformers/models/mllama/modeling_mllama.py,sha256=FHtpwkxRWenLaI0tbtmEBHf02FL4W7Mg2vdH181WQmM,106939
transformers/models/mllama/processing_mllama.py,sha256=cYwyVp4RfcINtp3_AYF2ZtyzcQXfnFG9Rihboqc4iK0,16698
transformers/models/mluke/__init__.py,sha256=e_3cNftWOmhNXk-zsA1-2DOBT9L56SHr-6qev0xI7Ws,956
transformers/models/mluke/__pycache__/__init__.cpython-312.pyc,,
transformers/models/mluke/__pycache__/tokenization_mluke.cpython-312.pyc,,
transformers/models/mluke/tokenization_mluke.py,sha256=8jrk37E5OS1Vxf1FZXkZoLD1xZWnVWDp8CBQMtpHjUE,82104
transformers/models/mobilebert/__init__.py,sha256=Jy7IZ2oQAjyE_KOoT-I7Z9bqPRVLfsOwx8XY3Y43RFc,1134
transformers/models/mobilebert/__pycache__/__init__.cpython-312.pyc,,
transformers/models/mobilebert/__pycache__/configuration_mobilebert.cpython-312.pyc,,
transformers/models/mobilebert/__pycache__/modeling_mobilebert.cpython-312.pyc,,
transformers/models/mobilebert/__pycache__/modeling_tf_mobilebert.cpython-312.pyc,,
transformers/models/mobilebert/__pycache__/tokenization_mobilebert.cpython-312.pyc,,
transformers/models/mobilebert/__pycache__/tokenization_mobilebert_fast.cpython-312.pyc,,
transformers/models/mobilebert/configuration_mobilebert.py,sha256=xE45pc42VCyNfA3dDQSaa1PBq9sGiHyh6M5TpeLI7Sc,8274
transformers/models/mobilebert/modeling_mobilebert.py,sha256=p2xG2mYD8wtpkAAYRcUXp_rjG8zIppejoz61CryRL8c,70973
transformers/models/mobilebert/modeling_tf_mobilebert.py,sha256=yi0BW1vZCHDFvL5BKW56FUFSC42SCLvuPWwAh8SPlOY,84096
transformers/models/mobilebert/tokenization_mobilebert.py,sha256=wdTDWMIuroy9XIgcc0YAE8jH4dHqv5Lzr_q-WoRa8CQ,21304
transformers/models/mobilebert/tokenization_mobilebert_fast.py,sha256=SO23cnn17U1YmVkRb27jKg-0OXNzFHKag-etFKHYHlM,7838
transformers/models/mobilenet_v1/__init__.py,sha256=3U7ptbKYiiXR37wVJRbjEKXSe1YBQr03WxtkvkY7lpE,1105
transformers/models/mobilenet_v1/__pycache__/__init__.cpython-312.pyc,,
transformers/models/mobilenet_v1/__pycache__/configuration_mobilenet_v1.cpython-312.pyc,,
transformers/models/mobilenet_v1/__pycache__/feature_extraction_mobilenet_v1.cpython-312.pyc,,
transformers/models/mobilenet_v1/__pycache__/image_processing_mobilenet_v1.cpython-312.pyc,,
transformers/models/mobilenet_v1/__pycache__/modeling_mobilenet_v1.cpython-312.pyc,,
transformers/models/mobilenet_v1/configuration_mobilenet_v1.py,sha256=v5hjZ9pV9ZigSYs-3HuJ6t2EPDPKMpvNxGExvvr7egQ,4930
transformers/models/mobilenet_v1/feature_extraction_mobilenet_v1.py,sha256=anErZjMwKNsiNUdDtE9dIkppxsEUU-IU298ctbE2B94,1266
transformers/models/mobilenet_v1/image_processing_mobilenet_v1.py,sha256=wViNCNs_3wB0BpNfCqzwNMxzgWJfZhWnLsBOSoNxQI8,15280
transformers/models/mobilenet_v1/modeling_mobilenet_v1.py,sha256=vEoD4MW4Ytp_NWVtaBcIZZgWuPvVAakyeuwg3O3Mhz8,18748
transformers/models/mobilenet_v2/__init__.py,sha256=n-a4qG6zDuMSEnOYeIsqErxILr_0z3m1zanJksulIa4,1105
transformers/models/mobilenet_v2/__pycache__/__init__.cpython-312.pyc,,
transformers/models/mobilenet_v2/__pycache__/configuration_mobilenet_v2.cpython-312.pyc,,
transformers/models/mobilenet_v2/__pycache__/feature_extraction_mobilenet_v2.cpython-312.pyc,,
transformers/models/mobilenet_v2/__pycache__/image_processing_mobilenet_v2.cpython-312.pyc,,
transformers/models/mobilenet_v2/__pycache__/modeling_mobilenet_v2.cpython-312.pyc,,
transformers/models/mobilenet_v2/configuration_mobilenet_v2.py,sha256=ddJ4XJItvwVjK3QY9tjKuKOpUuOLmODC9rVMpv--gPc,6826
transformers/models/mobilenet_v2/feature_extraction_mobilenet_v2.py,sha256=0tWo6rF6EIg06KW21VYuu_6nOt0FpuGKLT6bmNXHmag,1266
transformers/models/mobilenet_v2/image_processing_mobilenet_v2.py,sha256=tCiIyGu9K3nFYC6X90pohk5d3KcZOOnZBW_yVkw8KIk,17634
transformers/models/mobilenet_v2/modeling_mobilenet_v2.py,sha256=dXVsvtnCL002K6Dl9X6PSVsYlL9uckAEJGDMZqdcwkc,34677
transformers/models/mobilevit/__init__.py,sha256=v313uWvioi8yQuYM408mf0aEWVNcwFHjBeplAo6GtV0,1134
transformers/models/mobilevit/__pycache__/__init__.cpython-312.pyc,,
transformers/models/mobilevit/__pycache__/configuration_mobilevit.cpython-312.pyc,,
transformers/models/mobilevit/__pycache__/feature_extraction_mobilevit.cpython-312.pyc,,
transformers/models/mobilevit/__pycache__/image_processing_mobilevit.cpython-312.pyc,,
transformers/models/mobilevit/__pycache__/modeling_mobilevit.cpython-312.pyc,,
transformers/models/mobilevit/__pycache__/modeling_tf_mobilevit.cpython-312.pyc,,
transformers/models/mobilevit/configuration_mobilevit.py,sha256=rW2nFW5N3MxWzlMI7DOESzPykR-kFbxUlM9PohjET7k,7587
transformers/models/mobilevit/feature_extraction_mobilevit.py,sha256=AmtPlzbLUP_7urr2s6U36GfM8QnyNXUJ_MlaI2q-UhI,1249
transformers/models/mobilevit/image_processing_mobilevit.py,sha256=JW_jtrF81tOknX5N2hdHIwHWxudwmEile9jNeOPeOpw,21512
transformers/models/mobilevit/modeling_mobilevit.py,sha256=U9dyAT7Rkkt1lvluh4a5U4vLmYuM0lYBuhw8zjc-YKE,40279
transformers/models/mobilevit/modeling_tf_mobilevit.py,sha256=pUuFUsH-6t6J2vDNneT4Bkci4OGXpuNYwVESZzswQ_Y,54833
transformers/models/mobilevitv2/__init__.py,sha256=pAGk_9X22yOYvlcwbqTc4nm6fL4rPhAhDpdBguna5Q0,1003
transformers/models/mobilevitv2/__pycache__/__init__.cpython-312.pyc,,
transformers/models/mobilevitv2/__pycache__/configuration_mobilevitv2.cpython-312.pyc,,
transformers/models/mobilevitv2/__pycache__/modeling_mobilevitv2.cpython-312.pyc,,
transformers/models/mobilevitv2/configuration_mobilevitv2.py,sha256=XJcBxOXaWExP78dtuFQ8HoA77B6smeTBB-204JnUoIM,7150
transformers/models/mobilevitv2/modeling_mobilevitv2.py,sha256=tnTmaQmeEt41z7aphPg4BYEj-PEBvQM-e1jRErkbaY4,38362
transformers/models/modernbert/__init__.py,sha256=BEQFRFfcKvUlphA1ibW3s34Vkbm-MUuyqzaLbrIFiAA,1006
transformers/models/modernbert/__pycache__/__init__.cpython-312.pyc,,
transformers/models/modernbert/__pycache__/configuration_modernbert.cpython-312.pyc,,
transformers/models/modernbert/__pycache__/modeling_modernbert.cpython-312.pyc,,
transformers/models/modernbert/__pycache__/modular_modernbert.cpython-312.pyc,,
transformers/models/modernbert/configuration_modernbert.py,sha256=CwKAU-4iX8-bscAu0HrZpOif7q28guf3F8j5-5qpbfw,11310
transformers/models/modernbert/modeling_modernbert.py,sha256=4jXoAgxu4IKiGm8mK_Wzyz9HbWMCAc46FRjxcgCujes,58897
transformers/models/modernbert/modular_modernbert.py,sha256=FukcrPEKIe8I-Ee4ztuoEgaY_SCx_niI3PspKqY2QUs,63215
transformers/models/moonshine/__init__.py,sha256=eBgvc9LtoDnB6HnNvrObDWL3h_L4Sgn5-D-hepNfAmI,999
transformers/models/moonshine/__pycache__/__init__.cpython-312.pyc,,
transformers/models/moonshine/__pycache__/configuration_moonshine.cpython-312.pyc,,
transformers/models/moonshine/__pycache__/modeling_moonshine.cpython-312.pyc,,
transformers/models/moonshine/__pycache__/modular_moonshine.cpython-312.pyc,,
transformers/models/moonshine/configuration_moonshine.py,sha256=yjIrntV9DETz_2quXYO94WpGQObvtw2LED2vfHpwpZQ,13512
transformers/models/moonshine/modeling_moonshine.py,sha256=H-5Z8Q7vmclbLN6of2fBOTgv9uScxh6QcU4yjek7Ad8,75930
transformers/models/moonshine/modular_moonshine.py,sha256=_YVtr68u9qSBLAx4k1ydgHzvvhAqOzuJsnDadhEb6qE,57843
transformers/models/moshi/__init__.py,sha256=uW4oqTKZdbmURZaC_xwwHXnYEMyLJrMEJAlfbUzSWO8,991
transformers/models/moshi/__pycache__/__init__.cpython-312.pyc,,
transformers/models/moshi/__pycache__/configuration_moshi.cpython-312.pyc,,
transformers/models/moshi/__pycache__/modeling_moshi.cpython-312.pyc,,
transformers/models/moshi/configuration_moshi.py,sha256=ZYWbDase6o3SUCpLQVjcFaReiPXOgWihNDb76fh124Q,16050
transformers/models/moshi/modeling_moshi.py,sha256=qJ6J_udzs21YG0NT71kFA4_VgO63R5TICNQaBSGqw1I,140225
transformers/models/mpnet/__init__.py,sha256=agt4uraqHTtlIphsDB17XVAPzCKHaPBKlVaQkKHxRyM,1109
transformers/models/mpnet/__pycache__/__init__.cpython-312.pyc,,
transformers/models/mpnet/__pycache__/configuration_mpnet.cpython-312.pyc,,
transformers/models/mpnet/__pycache__/modeling_mpnet.cpython-312.pyc,,
transformers/models/mpnet/__pycache__/modeling_tf_mpnet.cpython-312.pyc,,
transformers/models/mpnet/__pycache__/tokenization_mpnet.cpython-312.pyc,,
transformers/models/mpnet/__pycache__/tokenization_mpnet_fast.cpython-312.pyc,,
transformers/models/mpnet/configuration_mpnet.py,sha256=DsCgTVE6hDGcaVxd2yqEPj7Ph-JLE2nPyt1AJlVZkx4,5327
transformers/models/mpnet/modeling_mpnet.py,sha256=BqMYodX425EhnXRSqg28t-EOK5fvuETR93fstV3nK4I,42841
transformers/models/mpnet/modeling_tf_mpnet.py,sha256=Q2KQ__x0k3XDov75cLAhCN-XkM6pmavudQH28osR7Sg,55748
transformers/models/mpnet/tokenization_mpnet.py,sha256=eYYgPLpYR3ODzls2m-m1sPRA6skT-wQ4wdN3lxP01jE,22475
transformers/models/mpnet/tokenization_mpnet_fast.py,sha256=WXozzALSVhzKSAm0FrhbQpkJ_KVvkkr7D9_w9fWPvSg,9193
transformers/models/mpt/__init__.py,sha256=DAIIAY0kPL-bXMkPUvxmP97HCXPi-SoM3NLnlJJYarg,987
transformers/models/mpt/__pycache__/__init__.cpython-312.pyc,,
transformers/models/mpt/__pycache__/configuration_mpt.cpython-312.pyc,,
transformers/models/mpt/__pycache__/modeling_mpt.cpython-312.pyc,,
transformers/models/mpt/configuration_mpt.py,sha256=fw4DDg2YlaYnIqsdw2S3xNrpjn1HqlRRL8FEEj19eSY,10543
transformers/models/mpt/modeling_mpt.py,sha256=shpHDPQKzKxzjsKpsHbc0Jv12Rt6WgZf3To7_H-t_vI,39463
transformers/models/mra/__init__.py,sha256=51mnm4DFq6aWxOsmaaVZDL28QozNauXyTtbEihDxUQU,987
transformers/models/mra/__pycache__/__init__.cpython-312.pyc,,
transformers/models/mra/__pycache__/configuration_mra.cpython-312.pyc,,
transformers/models/mra/__pycache__/modeling_mra.cpython-312.pyc,,
transformers/models/mra/configuration_mra.py,sha256=oNhRz6PdvUK_ugoiAhHDuNkGgBNyDguATgQdKeTJBnY,6536
transformers/models/mra/modeling_mra.py,sha256=BLiEYTX9SekcTwtGRL4rSD84AesPTPOD2nnUUQyZGDc,62199
transformers/models/mt5/__init__.py,sha256=UK8vGX9r6fPdzPaJKCbGJ7RCqKOdIo-7H9V-Qp8rwEg,1095
transformers/models/mt5/__pycache__/__init__.cpython-312.pyc,,
transformers/models/mt5/__pycache__/configuration_mt5.cpython-312.pyc,,
transformers/models/mt5/__pycache__/modeling_flax_mt5.cpython-312.pyc,,
transformers/models/mt5/__pycache__/modeling_mt5.cpython-312.pyc,,
transformers/models/mt5/__pycache__/modeling_tf_mt5.cpython-312.pyc,,
transformers/models/mt5/__pycache__/tokenization_mt5.cpython-312.pyc,,
transformers/models/mt5/__pycache__/tokenization_mt5_fast.cpython-312.pyc,,
transformers/models/mt5/configuration_mt5.py,sha256=VyZeWfoCTMskhWm1s80fIesKNfyYqvRncASS9XGqyJw,8002
transformers/models/mt5/modeling_flax_mt5.py,sha256=9WjlLB_EV9WDiy-rBxzVUPocsHrv02cEa4OB8lVR6EA,4329
transformers/models/mt5/modeling_mt5.py,sha256=2FP1XQDz6L5IXCab8zRyJtGOU7JA5CgjsgSV0j8hXE4,119564
transformers/models/mt5/modeling_tf_mt5.py,sha256=EIUkWvuApAbiaX6qhveT1KC43s_NDmQazLrbYT45aao,3406
transformers/models/mt5/tokenization_mt5.py,sha256=AckaXSw5OojOGLezMhrsv2a9BMZXwzhy5IsT3hvp_Q8,746
transformers/models/mt5/tokenization_mt5_fast.py,sha256=1npEFH_c4nDQxOFNoqcGNW30KCWe04BpLrrv7aDcDQ8,762
transformers/models/musicgen/__init__.py,sha256=iwtW9pg6iDe5D2dWVC4IRU8QbNmRK5kMqPCM8fsUSgo,1036
transformers/models/musicgen/__pycache__/__init__.cpython-312.pyc,,
transformers/models/musicgen/__pycache__/configuration_musicgen.cpython-312.pyc,,
transformers/models/musicgen/__pycache__/modeling_musicgen.cpython-312.pyc,,
transformers/models/musicgen/__pycache__/processing_musicgen.cpython-312.pyc,,
transformers/models/musicgen/configuration_musicgen.py,sha256=3nUL5CBMpG7IDKZ1659J58JsRAckf6rYxyGwflbo1SQ,10876
transformers/models/musicgen/modeling_musicgen.py,sha256=JOKCkgAwgLzxo0eqc51AcG1lepmtfnvHPcqzYL6dIUM,135981
transformers/models/musicgen/processing_musicgen.py,sha256=yLMH8wTOkE-Rv3YFeI6fz1NbdRCDbU0AL4dVZZ-Ih5s,5701
transformers/models/musicgen_melody/__init__.py,sha256=v3FVLsoE2TEh_eAaYKcb8v114HPo9RZN-p5TSS4eD_I,2594
transformers/models/musicgen_melody/__pycache__/__init__.cpython-312.pyc,,
transformers/models/musicgen_melody/__pycache__/configuration_musicgen_melody.cpython-312.pyc,,
transformers/models/musicgen_melody/__pycache__/feature_extraction_musicgen_melody.cpython-312.pyc,,
transformers/models/musicgen_melody/__pycache__/modeling_musicgen_melody.cpython-312.pyc,,
transformers/models/musicgen_melody/__pycache__/processing_musicgen_melody.cpython-312.pyc,,
transformers/models/musicgen_melody/configuration_musicgen_melody.py,sha256=OiqJNBa-iWwLlwkrkA9Uodg9tuMPA95Cvy7_4hSPnMo,11931
transformers/models/musicgen_melody/feature_extraction_musicgen_melody.py,sha256=0-gMjuGhG4JMeM-44wa3aTo3Nph-_cjZs7k3nhc6cfE,15227
transformers/models/musicgen_melody/modeling_musicgen_melody.py,sha256=4ezqr4dKdvihEWrYFgOIGP89UncYb5CN0KsgQkiQ_SM,128281
transformers/models/musicgen_melody/processing_musicgen_melody.py,sha256=W2dEpemPPhX10YgTwm0T3zvy71Z8QxJ_LSoz6Z00UAI,8634
transformers/models/mvp/__init__.py,sha256=0e0-wP4EkfzPiO_BlHlmyVUEq-1kb9RHY2Ikbk66W7s,1064
transformers/models/mvp/__pycache__/__init__.cpython-312.pyc,,
transformers/models/mvp/__pycache__/configuration_mvp.cpython-312.pyc,,
transformers/models/mvp/__pycache__/modeling_mvp.cpython-312.pyc,,
transformers/models/mvp/__pycache__/tokenization_mvp.cpython-312.pyc,,
transformers/models/mvp/__pycache__/tokenization_mvp_fast.cpython-312.pyc,,
transformers/models/mvp/configuration_mvp.py,sha256=AzvHDoXei3ZGo_lYWLM64GrqafASaAX2_sU0RuBSKqM,8435
transformers/models/mvp/modeling_mvp.py,sha256=MF8ieJiSjDhORXRRty1pJUgoS9-Ezgk3iD0h4XIlfpY,90497
transformers/models/mvp/tokenization_mvp.py,sha256=FNCM8ee93oXqWSJ6KEs7AHBORCbhCvQR1yDDxPKID5I,16221
transformers/models/mvp/tokenization_mvp_fast.py,sha256=nVUAFwajGI2hZXUE5h9_3mDnCvwAkipVhmsBeEtBEyw,11832
transformers/models/myt5/__init__.py,sha256=MFQX-RuvZujGb_twBWBQpTt4NZq6FxreEysWmF2fFGI,955
transformers/models/myt5/__pycache__/__init__.cpython-312.pyc,,
transformers/models/myt5/__pycache__/tokenization_myt5.cpython-312.pyc,,
transformers/models/myt5/tokenization_myt5.py,sha256=xsj12hpUt7sJj3slLU9mRwOeq3L_724yxoPlNrQsDtI,15555
transformers/models/nemotron/__init__.py,sha256=ZwaMH1AQ0VIuFnouYe0Sx0HcCGA7PaCp3-_yw3xjeQA,997
transformers/models/nemotron/__pycache__/__init__.cpython-312.pyc,,
transformers/models/nemotron/__pycache__/configuration_nemotron.cpython-312.pyc,,
transformers/models/nemotron/__pycache__/modeling_nemotron.cpython-312.pyc,,
transformers/models/nemotron/configuration_nemotron.py,sha256=JaqyIqgHyuy7Z2J6PCJd1Ub_m0p5pvQ6ge3ag5hYtgs,7393
transformers/models/nemotron/modeling_nemotron.py,sha256=WAedmCAhqSd4HHSRtQi4djjWsbzZ-pld0Co_EvaRZ28,65374
transformers/models/nllb/__init__.py,sha256=MLFrxhOJ3xvOAcRulvCEMoKsajLuudllZLMrYDYQOas,997
transformers/models/nllb/__pycache__/__init__.cpython-312.pyc,,
transformers/models/nllb/__pycache__/tokenization_nllb.cpython-312.pyc,,
transformers/models/nllb/__pycache__/tokenization_nllb_fast.cpython-312.pyc,,
transformers/models/nllb/tokenization_nllb.py,sha256=bxNqY1VepeLj6BTqismS9LHpZZTly7P7KeTi09e62q4,19095
transformers/models/nllb/tokenization_nllb_fast.py,sha256=SjrPK2Z7hnfIuFf-baiftFFhG5JViWA0wqoWcshAf2Q,15974
transformers/models/nllb_moe/__init__.py,sha256=sAfoAnhHK_reU1a2WUoF1rFtPBckeGGrzJCD8gUv54A,997
transformers/models/nllb_moe/__pycache__/__init__.cpython-312.pyc,,
transformers/models/nllb_moe/__pycache__/configuration_nllb_moe.cpython-312.pyc,,
transformers/models/nllb_moe/__pycache__/modeling_nllb_moe.cpython-312.pyc,,
transformers/models/nllb_moe/configuration_nllb_moe.py,sha256=mgDfnEpxjMp0ACizcpHZ70lU-TaE0CLAXzfAPzZJQdw,11198
transformers/models/nllb_moe/modeling_nllb_moe.py,sha256=gOpgkAPRRHIuAl9OiMnpuQffE9PQjrX7xtagzGeFCWU,84588
transformers/models/nougat/__init__.py,sha256=W-_PD9oOisHzq8UvCK10HGSaz8ljuAkcBC5ElCPj6Bs,1042
transformers/models/nougat/__pycache__/__init__.cpython-312.pyc,,
transformers/models/nougat/__pycache__/image_processing_nougat.cpython-312.pyc,,
transformers/models/nougat/__pycache__/processing_nougat.cpython-312.pyc,,
transformers/models/nougat/__pycache__/tokenization_nougat_fast.cpython-312.pyc,,
transformers/models/nougat/image_processing_nougat.py,sha256=XC8mwaDeH8mtIwHjQGssTyTNFlMjNIx5U1sp9mNq0-Y,23739
transformers/models/nougat/processing_nougat.py,sha256=Rdm5WKDzUJmLX2SPYoMRRudyW4tYz8UWZIh97WYpRcY,6763
transformers/models/nougat/tokenization_nougat_fast.py,sha256=c6y7vWMpIEQWNNal-NPP-Vq6GM7tht55nCClCIo5UjA,24740
transformers/models/nystromformer/__init__.py,sha256=CwEg6m4nJW_AfNDws_MIv1O1x5IO3xPp-FYqirlFXwk,1007
transformers/models/nystromformer/__pycache__/__init__.cpython-312.pyc,,
transformers/models/nystromformer/__pycache__/configuration_nystromformer.cpython-312.pyc,,
transformers/models/nystromformer/__pycache__/modeling_nystromformer.cpython-312.pyc,,
transformers/models/nystromformer/configuration_nystromformer.py,sha256=UyLmPF2li3_ADTz9tS1h5t4CDY5d5GzsfeC9hG42RzI,6402
transformers/models/nystromformer/modeling_nystromformer.py,sha256=fi3bgpEtCWBJFZHVEgiGrjvELrarXGJxw9kXPMEPXpE,49062
transformers/models/olmo/__init__.py,sha256=_dNlQLxAlwk4Yt9djxtrLXy90ben8LUx4LtD8wZR5hU,1658
transformers/models/olmo/__pycache__/__init__.cpython-312.pyc,,
transformers/models/olmo/__pycache__/configuration_olmo.cpython-312.pyc,,
transformers/models/olmo/__pycache__/modeling_olmo.cpython-312.pyc,,
transformers/models/olmo/__pycache__/modular_olmo.cpython-312.pyc,,
transformers/models/olmo/configuration_olmo.py,sha256=5KeFHDi3uqTuhNA4TrbCUn4xHqGETyhmjjao7ouSdJA,9390
transformers/models/olmo/modeling_olmo.py,sha256=E35v9A_Sy88sy-Hfl8eF48d-HfUfHDIuqqCu-toS6sE,39089
transformers/models/olmo/modular_olmo.py,sha256=IiT1bB4e_6LwChEsMYnMIx-eqd6GW6gtVECqnTXnBnA,4899
transformers/models/olmo2/__init__.py,sha256=Frt9nEMsfPszod1lkFTAJUobU50IjOFlqI6uJkuQVcY,1011
transformers/models/olmo2/__pycache__/__init__.cpython-312.pyc,,
transformers/models/olmo2/__pycache__/configuration_olmo2.cpython-312.pyc,,
transformers/models/olmo2/__pycache__/modeling_olmo2.cpython-312.pyc,,
transformers/models/olmo2/__pycache__/modular_olmo2.cpython-312.pyc,,
transformers/models/olmo2/configuration_olmo2.py,sha256=2LPyk1g4dn8hp0DID8-5QOkogpwaOg_hN0fl_MvV9dY,9434
transformers/models/olmo2/modeling_olmo2.py,sha256=U_PyqevKxuym8WqLH-e435FpKo-Yi1300oCiiDmTykM,39425
transformers/models/olmo2/modular_olmo2.py,sha256=qbyymHFbIR2B_gaYXUKc719XS6SkrPgr9EFra8KSNtQ,13876
transformers/models/olmoe/__init__.py,sha256=eQ6mx9aBIcA4RiK3p7dbqORokkuMfQNRss06E8uWNrk,991
transformers/models/olmoe/__pycache__/__init__.cpython-312.pyc,,
transformers/models/olmoe/__pycache__/configuration_olmoe.cpython-312.pyc,,
transformers/models/olmoe/__pycache__/modeling_olmoe.cpython-312.pyc,,
transformers/models/olmoe/configuration_olmoe.py,sha256=OqnPtXjK4Kdjzk-HozshemJ8IemwbIgheYCM0BY7mbE,9064
transformers/models/olmoe/modeling_olmoe.py,sha256=NOsq5g-9nYgRUeoqmMr6aOFtghlOUWOu3aLQl3U0jHk,61686
transformers/models/omdet_turbo/__init__.py,sha256=XIckpuo9tkT7NB5uTs9wLdpxr9GDedQPVJL2P8XU-7Q,1045
transformers/models/omdet_turbo/__pycache__/__init__.cpython-312.pyc,,
transformers/models/omdet_turbo/__pycache__/configuration_omdet_turbo.cpython-312.pyc,,
transformers/models/omdet_turbo/__pycache__/modeling_omdet_turbo.cpython-312.pyc,,
transformers/models/omdet_turbo/__pycache__/processing_omdet_turbo.cpython-312.pyc,,
transformers/models/omdet_turbo/configuration_omdet_turbo.py,sha256=pwzcgMM_6bEv7s0zH3-w1QvoAQQfcck7IevKDhEQiCg,14479
transformers/models/omdet_turbo/modeling_omdet_turbo.py,sha256=Hx-TEBbWCmx3D5gY0-e6oQnrl6LGFpUlnBkCbcDFKGA,81584
transformers/models/omdet_turbo/processing_omdet_turbo.py,sha256=OymTXjgRc-8Jbc6bFM7JuU7avH-bfkUUvdaf25krhoA,17411
transformers/models/oneformer/__init__.py,sha256=w9mGWZlVRSSC_IVWwcXxJudlvc_XvCffD1_yupoIDRY,1085
transformers/models/oneformer/__pycache__/__init__.cpython-312.pyc,,
transformers/models/oneformer/__pycache__/configuration_oneformer.cpython-312.pyc,,
transformers/models/oneformer/__pycache__/image_processing_oneformer.cpython-312.pyc,,
transformers/models/oneformer/__pycache__/modeling_oneformer.cpython-312.pyc,,
transformers/models/oneformer/__pycache__/processing_oneformer.cpython-312.pyc,,
transformers/models/oneformer/configuration_oneformer.py,sha256=lx36Li_mBnawtbdvKUdHrF5qj0-2nAijgTF-SEadvDs,13468
transformers/models/oneformer/image_processing_oneformer.py,sha256=v_pWc4wZ5_VKTrPgiYxYqbsdKyxo9moI3RUUo3yWZ5I,61266
transformers/models/oneformer/modeling_oneformer.py,sha256=AWgXS43DqO5Hbem-77J5VRvP_6vdnVhaxr8N5y-wOmQ,143688
transformers/models/oneformer/processing_oneformer.py,sha256=MmkQH3xINYzVzebgiT0qH5iIq6hHPsMPxYugszkI9aY,9412
transformers/models/openai/__init__.py,sha256=q0fAl8ajoJyknHe5A3ZHuHH3zww8xdupt_j49lIaObY,1114
transformers/models/openai/__pycache__/__init__.cpython-312.pyc,,
transformers/models/openai/__pycache__/configuration_openai.cpython-312.pyc,,
transformers/models/openai/__pycache__/modeling_openai.cpython-312.pyc,,
transformers/models/openai/__pycache__/modeling_tf_openai.cpython-312.pyc,,
transformers/models/openai/__pycache__/tokenization_openai.cpython-312.pyc,,
transformers/models/openai/__pycache__/tokenization_openai_fast.cpython-312.pyc,,
transformers/models/openai/configuration_openai.py,sha256=ERFfcrsaGEuG-8WnuBDfYyHR7uc5ihEr9JfItBMGZm0,7109
transformers/models/openai/modeling_openai.py,sha256=YLRkqPMQ6ciNehNIzWfY9qEDRAJ225nradqqu06n3is,38485
transformers/models/openai/modeling_tf_openai.py,sha256=8DaZFoqmQfiR__7-G3T_Xt62yujLZapTD8qrlZZ17jE,40975
transformers/models/openai/tokenization_openai.py,sha256=KQ699NFTu1N7nB5ugH5sKTYSrWuOESVCJMubWRWS6aU,15187
transformers/models/openai/tokenization_openai_fast.py,sha256=M3hYvAYNCF-qRUg23AhU5AkvKZIxWcNLpF-6dzJJLaw,2560
transformers/models/opt/__init__.py,sha256=Xk3Z-OdrOC4Y5J0KOEIB74Pp4PsfAllBI503NT7yFk8,1059
transformers/models/opt/__pycache__/__init__.cpython-312.pyc,,
transformers/models/opt/__pycache__/configuration_opt.cpython-312.pyc,,
transformers/models/opt/__pycache__/modeling_flax_opt.cpython-312.pyc,,
transformers/models/opt/__pycache__/modeling_opt.cpython-312.pyc,,
transformers/models/opt/__pycache__/modeling_tf_opt.cpython-312.pyc,,
transformers/models/opt/configuration_opt.py,sha256=bgH5bXI8nuYRBPOP93zqxqaV5T6mmEw_AP7uDq7Bt-k,6686
transformers/models/opt/modeling_flax_opt.py,sha256=MvU2uukG3UpYAZ_UkXcYAPEFT1f-Gt1Ir9TBzjS2Wuc,31617
transformers/models/opt/modeling_opt.py,sha256=0SgKY6s0yvgCFvIMZSuVOihtRT3M4-PUj83AOInW_QE,70076
transformers/models/opt/modeling_tf_opt.py,sha256=4XcpnLOnfNMOvHcviavTPp1B7PSdXYqMbN-IltFFpsU,49623
transformers/models/owlv2/__init__.py,sha256=vCDn8zY6eLkh1fT2R0YnXKC9C7xe5Q0UHe5cvce3cxs,1069
transformers/models/owlv2/__pycache__/__init__.cpython-312.pyc,,
transformers/models/owlv2/__pycache__/configuration_owlv2.cpython-312.pyc,,
transformers/models/owlv2/__pycache__/image_processing_owlv2.cpython-312.pyc,,
transformers/models/owlv2/__pycache__/modeling_owlv2.cpython-312.pyc,,
transformers/models/owlv2/__pycache__/processing_owlv2.cpython-312.pyc,,
transformers/models/owlv2/configuration_owlv2.py,sha256=cf5_-eT4QouWjnJ1NsLVGEo5_LmEdM1EKnmEO7JB98A,13203
transformers/models/owlv2/image_processing_owlv2.py,sha256=9JDzApJ3jIM6v9s6_H2FKwJK7xqDm84_DEklwh94qLY,27991
transformers/models/owlv2/modeling_owlv2.py,sha256=uz7J0xey2rI4Z14ScNJ1RqZo84orNazxWR7Spzbae9s,86159
transformers/models/owlv2/processing_owlv2.py,sha256=l0dyO47_1TdYQRCPaTBrNgvEXCQXaZQ609zezEHRC-o,16156
transformers/models/owlvit/__init__.py,sha256=rN_V6yzWDuBHgrDtr_qAOn2X1ek-lCE3QsamyEFmAVg,1118
transformers/models/owlvit/__pycache__/__init__.cpython-312.pyc,,
transformers/models/owlvit/__pycache__/configuration_owlvit.cpython-312.pyc,,
transformers/models/owlvit/__pycache__/feature_extraction_owlvit.cpython-312.pyc,,
transformers/models/owlvit/__pycache__/image_processing_owlvit.cpython-312.pyc,,
transformers/models/owlvit/__pycache__/modeling_owlvit.cpython-312.pyc,,
transformers/models/owlvit/__pycache__/processing_owlvit.cpython-312.pyc,,
transformers/models/owlvit/configuration_owlvit.py,sha256=GOX1i1CekVQYYpqStS3qfQ3uhtWW8mkQmi9E_6KkHwY,14414
transformers/models/owlvit/feature_extraction_owlvit.py,sha256=1e7IvryNbsbYMKeAj257kEmW3xuo755gQb7VaPvtWLc,1225
transformers/models/owlvit/image_processing_owlvit.py,sha256=mdOll8EpxUlFILzlplkcJbMsYR3kicwCSxIp4FMHUp0,29406
transformers/models/owlvit/modeling_owlvit.py,sha256=zWbFqHrfU8ZKgqCcu_le9G7eHLSp6cNcd2F_aQ_DgxY,81573
transformers/models/owlvit/processing_owlvit.py,sha256=ws5UjgT_lEK8s2-O3eKoq3mEtKXX06QcrFjKPjUKdb8,17015
transformers/models/paligemma/__init__.py,sha256=nKnTTLC8XYlI7uYfS8h-D4vz3gFhknkNeDlZIwZlZ9w,1039
transformers/models/paligemma/__pycache__/__init__.cpython-312.pyc,,
transformers/models/paligemma/__pycache__/configuration_paligemma.cpython-312.pyc,,
transformers/models/paligemma/__pycache__/modeling_paligemma.cpython-312.pyc,,
transformers/models/paligemma/__pycache__/processing_paligemma.cpython-312.pyc,,
transformers/models/paligemma/configuration_paligemma.py,sha256=pG8Uprjy6ENP3IWFLNKNEcYdvI16cMWUBYOgQJoJo94,6063
transformers/models/paligemma/modeling_paligemma.py,sha256=qMHFp6xmU39sdMYeGWEnPjj46usr5oyQLrKxiguuklY,31716
transformers/models/paligemma/processing_paligemma.py,sha256=W-JhXKAGoj3Ificomyt5wP4z6GpF7qQR-2TBIxKaOSw,15401
transformers/models/patchtsmixer/__init__.py,sha256=deFjF_Tu67XcAcNHaq1PXO77N4kVW9wG80SnXBaeagE,1005
transformers/models/patchtsmixer/__pycache__/__init__.cpython-312.pyc,,
transformers/models/patchtsmixer/__pycache__/configuration_patchtsmixer.cpython-312.pyc,,
transformers/models/patchtsmixer/__pycache__/modeling_patchtsmixer.cpython-312.pyc,,
transformers/models/patchtsmixer/configuration_patchtsmixer.py,sha256=wLXHLBxIFjsOIXHOzSmWxhOgRwdxr7gcu-ZKEiTDJgg,12566
transformers/models/patchtsmixer/modeling_patchtsmixer.py,sha256=tFdqSR1Fw_2zFLAWnqJe3tjPSakOt9gM-hKMqmJ2VkM,87918
transformers/models/patchtst/__init__.py,sha256=lrpuBvP25Yq6HZOCyS4yWVYZ47qWzK--rqC0AOIGGPE,997
transformers/models/patchtst/__pycache__/__init__.cpython-312.pyc,,
transformers/models/patchtst/__pycache__/configuration_patchtst.cpython-312.pyc,,
transformers/models/patchtst/__pycache__/modeling_patchtst.cpython-312.pyc,,
transformers/models/patchtst/configuration_patchtst.py,sha256=F7VEYrtDyw-GEOyDcdVyupuUrVO-4p4PBWT_0kK_7VM,12315
transformers/models/patchtst/modeling_patchtst.py,sha256=dnM_I2pFuqxU1kx0BnNO374_Z0aQlOWekvKo0v0JbH4,91914
transformers/models/pegasus/__init__.py,sha256=4b7vCYJfIWUPuKrbcBGTG7LtobUdZ5ZjeQhloScTrXs,1160
transformers/models/pegasus/__pycache__/__init__.cpython-312.pyc,,
transformers/models/pegasus/__pycache__/configuration_pegasus.cpython-312.pyc,,
transformers/models/pegasus/__pycache__/modeling_flax_pegasus.cpython-312.pyc,,
transformers/models/pegasus/__pycache__/modeling_pegasus.cpython-312.pyc,,
transformers/models/pegasus/__pycache__/modeling_tf_pegasus.cpython-312.pyc,,
transformers/models/pegasus/__pycache__/tokenization_pegasus.cpython-312.pyc,,
transformers/models/pegasus/__pycache__/tokenization_pegasus_fast.cpython-312.pyc,,
transformers/models/pegasus/configuration_pegasus.py,sha256=nMfDkbgXkv7lEFWlLrijcYXFgGqKF-wP0_N8UHa_Bt8,7501
transformers/models/pegasus/modeling_flax_pegasus.py,sha256=_YU7jwBfcjS9ylcIwLNGHP3FGxJ2LZmh0nMjALuicBc,66074
transformers/models/pegasus/modeling_pegasus.py,sha256=3RAfL4_cwlTXo5UH0bN5BXShUMxd26c1_92OG2FgYbA,78275
transformers/models/pegasus/modeling_tf_pegasus.py,sha256=4BBU7FpCKFmfqOO9xLWRtc-kaEWilCyMezp9Hjoj-9Y,74296
transformers/models/pegasus/tokenization_pegasus.py,sha256=cYiIdbzE_ddjIx9xrfOES1f4MZMopx3svdE82qyU3cY,13158
transformers/models/pegasus/tokenization_pegasus_fast.py,sha256=dU1D5wObpz9wPIALiZouM5OzbISR8jEg8tZEPFmGgZg,9977
transformers/models/pegasus_x/__init__.py,sha256=qSLaqKRA1upZOobapHW5MjSZvIEzf-ij-ZmY1VGzqaE,999
transformers/models/pegasus_x/__pycache__/__init__.cpython-312.pyc,,
transformers/models/pegasus_x/__pycache__/configuration_pegasus_x.cpython-312.pyc,,
transformers/models/pegasus_x/__pycache__/modeling_pegasus_x.cpython-312.pyc,,
transformers/models/pegasus_x/configuration_pegasus_x.py,sha256=d0by30PpS5eiLt9Pccsy_HIqZRohZQ1MjJCTScHqRk4,8116
transformers/models/pegasus_x/modeling_pegasus_x.py,sha256=3MteMW85xo2n3n_o38ianiXqqGxN4AxzWGZW2Ly3PKs,75619
transformers/models/perceiver/__init__.py,sha256=C8S_9aD_JZCcDqv5lZhUw3I45vr09RYiZWlAmo83688,1135
transformers/models/perceiver/__pycache__/__init__.cpython-312.pyc,,
transformers/models/perceiver/__pycache__/configuration_perceiver.cpython-312.pyc,,
transformers/models/perceiver/__pycache__/feature_extraction_perceiver.cpython-312.pyc,,
transformers/models/perceiver/__pycache__/image_processing_perceiver.cpython-312.pyc,,
transformers/models/perceiver/__pycache__/modeling_perceiver.cpython-312.pyc,,
transformers/models/perceiver/__pycache__/tokenization_perceiver.cpython-312.pyc,,
transformers/models/perceiver/configuration_perceiver.py,sha256=dOaFFVps56ciQfRpHWV8UYTcJfI4Yc39cZF8FyknLkU,12209
transformers/models/perceiver/feature_extraction_perceiver.py,sha256=tDYvohyPm9KiWTT8TbiKuPqAX4vowyjp3_hX7zWsKRk,1249
transformers/models/perceiver/image_processing_perceiver.py,sha256=jtV6dnqRj8oXW0XDC6IBe8XOPr3fW5J5OrNfxiJjtdc,17488
transformers/models/perceiver/modeling_perceiver.py,sha256=XvajaLalUL4NEO0Ulj3AgckbHbx8RxbsYddVt9MeOog,149298
transformers/models/perceiver/tokenization_perceiver.py,sha256=Go8KZHZ3zl2hHOg8NvuENG_QV402rlbNJv5yeivdvnE,8053
transformers/models/persimmon/__init__.py,sha256=T1WqyE78N2TO74u9a9QdRIGaMowYqP6vWv8KhPojkLg,999
transformers/models/persimmon/__pycache__/__init__.cpython-312.pyc,,
transformers/models/persimmon/__pycache__/configuration_persimmon.cpython-312.pyc,,
transformers/models/persimmon/__pycache__/modeling_persimmon.cpython-312.pyc,,
transformers/models/persimmon/configuration_persimmon.py,sha256=TPogwaoT3PYEHds8wR_G-GIZWPPKKj84g-ykK3NljBg,9149
transformers/models/persimmon/modeling_persimmon.py,sha256=zwnlUtJwfc4EasZCjI37SGCCBaQUBuI4YZGyVAMBsTg,52204
transformers/models/phi/__init__.py,sha256=qMWyJRn1PnnyX647VO4xrJbR7hlTiwvtEkyQVDEKHxw,1807
transformers/models/phi/__pycache__/__init__.cpython-312.pyc,,
transformers/models/phi/__pycache__/configuration_phi.cpython-312.pyc,,
transformers/models/phi/__pycache__/modeling_phi.cpython-312.pyc,,
transformers/models/phi/__pycache__/modular_phi.cpython-312.pyc,,
transformers/models/phi/configuration_phi.py,sha256=m-UE3-Jo6JvVryef762Ma4UXw9_QX2bfw90C0JAgfTE,11140
transformers/models/phi/modeling_phi.py,sha256=ZGO3wscnFb1sWdEWSp-gfHOak2UriXXuYY9XFD7JUYw,47639
transformers/models/phi/modular_phi.py,sha256=UQbWfJoqo2SX-BdeGHHkwtSpzZXwGuFJKXFpLQme34Q,12190
transformers/models/phi3/__init__.py,sha256=dxyO-jIh0yB6t2Dzs173aRrEnTceVMIYIkg6JxIeyWs,989
transformers/models/phi3/__pycache__/__init__.cpython-312.pyc,,
transformers/models/phi3/__pycache__/configuration_phi3.cpython-312.pyc,,
transformers/models/phi3/__pycache__/modeling_phi3.cpython-312.pyc,,
transformers/models/phi3/__pycache__/modular_phi3.cpython-312.pyc,,
transformers/models/phi3/configuration_phi3.py,sha256=eeMnUVgktAqu8ZPk-w3vW2rY_6_9P8RX5d_fWkheG0s,11573
transformers/models/phi3/modeling_phi3.py,sha256=xe1EHDHPmm9vKV6-cX4qtmJoRhSVs9DCYUStHDyZ1fA,55096
transformers/models/phi3/modular_phi3.py,sha256=-OjXwtGxFzlaqGKFEGlGzr-MknD6sw6jg_k_P6jL3YI,15729
transformers/models/phimoe/__init__.py,sha256=wGasPysu0EH_q0QGaZmXqQL57GxfZn8NTsvB2I6U2ro,1013
transformers/models/phimoe/__pycache__/__init__.cpython-312.pyc,,
transformers/models/phimoe/__pycache__/configuration_phimoe.cpython-312.pyc,,
transformers/models/phimoe/__pycache__/modeling_phimoe.cpython-312.pyc,,
transformers/models/phimoe/configuration_phimoe.py,sha256=ChXe5y5wBQvk4nbo9m4cqLp_n8xlhzpB6ZG6ru71cN0,10273
transformers/models/phimoe/modeling_phimoe.py,sha256=PrQngWZqwZrkcJz6LTrFWxn6wRxEvYMgG8zUk240knk,74405
transformers/models/phobert/__init__.py,sha256=mau-2HIOzSk8qGIhxivVBPPYTx3hhdgoKPtnptDF38M,958
transformers/models/phobert/__pycache__/__init__.cpython-312.pyc,,
transformers/models/phobert/__pycache__/tokenization_phobert.cpython-312.pyc,,
transformers/models/phobert/tokenization_phobert.py,sha256=MvkVnqP_ZVu7qiN88MUxwc948LJJ0gCvDjgyWjIwN80,13124
transformers/models/pix2struct/__init__.py,sha256=ivncogrVjZZ6ag6FYHJ0XqyCMJYbsCYlh5boqxe09Yo,1089
transformers/models/pix2struct/__pycache__/__init__.cpython-312.pyc,,
transformers/models/pix2struct/__pycache__/configuration_pix2struct.cpython-312.pyc,,
transformers/models/pix2struct/__pycache__/image_processing_pix2struct.cpython-312.pyc,,
transformers/models/pix2struct/__pycache__/modeling_pix2struct.cpython-312.pyc,,
transformers/models/pix2struct/__pycache__/processing_pix2struct.cpython-312.pyc,,
transformers/models/pix2struct/configuration_pix2struct.py,sha256=fqBGk2kwErmFVBkcbM0_5shTg3VUcQynKoAqPw_Kp3U,15803
transformers/models/pix2struct/image_processing_pix2struct.py,sha256=nibo9FTJil2WHIyo9uaAsK5J2k6XcEYPWDPHqHBUX-o,19769
transformers/models/pix2struct/modeling_pix2struct.py,sha256=a6mnHJUaY1Sd3xt_JXOH1GHnz71Btf2dswwvaoxqDsE,88714
transformers/models/pix2struct/processing_pix2struct.py,sha256=yyNOHBL_8-ljZUcoEoulZls4pYQ_sksThesjTKvizcA,6912
transformers/models/pixtral/__init__.py,sha256=WKCxuWpCeTYsYSaTH1XnUcGkIHEx5BIIXwwwqG_E83s,1126
transformers/models/pixtral/__pycache__/__init__.cpython-312.pyc,,
transformers/models/pixtral/__pycache__/configuration_pixtral.cpython-312.pyc,,
transformers/models/pixtral/__pycache__/image_processing_pixtral.cpython-312.pyc,,
transformers/models/pixtral/__pycache__/image_processing_pixtral_fast.cpython-312.pyc,,
transformers/models/pixtral/__pycache__/modeling_pixtral.cpython-312.pyc,,
transformers/models/pixtral/__pycache__/processing_pixtral.cpython-312.pyc,,
transformers/models/pixtral/configuration_pixtral.py,sha256=86cY74VW7J8XqU1JbvpxLqOXnnzoPh7I_9zja8j3Wng,4237
transformers/models/pixtral/image_processing_pixtral.py,sha256=Ggx01NI_YgE6_u0_USFcEyZ5JDc_toVhRg6-m0itNGs,21839
transformers/models/pixtral/image_processing_pixtral_fast.py,sha256=4AGck9FNegXldv7TxI3qbdJp8xwGfCpCxgUxoPNTwgA,8876
transformers/models/pixtral/modeling_pixtral.py,sha256=qr5t3G8yAPRc2pKDQVfc_edEXXpRO0ojMaqUDmBjj6k,22613
transformers/models/pixtral/processing_pixtral.py,sha256=d89Di6tdjdItJcZ1tZasFCZFPEmIJ8NXbDb2W1fkK8o,10732
transformers/models/plbart/__init__.py,sha256=jmP857QTG7jGfr9n0qK3TB_1-hdVDD1ajtJvP6C7FIw,1032
transformers/models/plbart/__pycache__/__init__.cpython-312.pyc,,
transformers/models/plbart/__pycache__/configuration_plbart.cpython-312.pyc,,
transformers/models/plbart/__pycache__/modeling_plbart.cpython-312.pyc,,
transformers/models/plbart/__pycache__/tokenization_plbart.cpython-312.pyc,,
transformers/models/plbart/configuration_plbart.py,sha256=dw0_2B0Ij7jaYwqa5kOmn8S8D7CAoNgU7NqGw1B_AU4,8532
transformers/models/plbart/modeling_plbart.py,sha256=GX6eyVsLq8e6nGRp40qiZwgndN84b-0tmZsS8buosDI,82545
transformers/models/plbart/tokenization_plbart.py,sha256=XIGZiAIM7Z8edC7GgAqk3RVjDapVIYxltIA-Y_RmcKM,18892
transformers/models/poolformer/__init__.py,sha256=pkSn3nUzqUgBbSmXc7vFD6xYpMlPAuPkhCptxKCdB8s,1097
transformers/models/poolformer/__pycache__/__init__.cpython-312.pyc,,
transformers/models/poolformer/__pycache__/configuration_poolformer.cpython-312.pyc,,
transformers/models/poolformer/__pycache__/feature_extraction_poolformer.cpython-312.pyc,,
transformers/models/poolformer/__pycache__/image_processing_poolformer.cpython-312.pyc,,
transformers/models/poolformer/__pycache__/modeling_poolformer.cpython-312.pyc,,
transformers/models/poolformer/configuration_poolformer.py,sha256=08vAc_wIJXHt-lO09RK6rezPGn_i4i5WcM-cnVm_0mA,5632
transformers/models/poolformer/feature_extraction_poolformer.py,sha256=wj7ZMJPbN-YgxbMqOaIu61VL66QdjxDPuzdi38SVLnY,1257
transformers/models/poolformer/image_processing_poolformer.py,sha256=wSvnkMwYTBuBsmbVp96lXfX5Boh1VQuVlEpqpfUfJ90,17850
transformers/models/poolformer/modeling_poolformer.py,sha256=j-bJCPk7paqTHPp-D2aHMQGhkqvAkRfPlDiUJa-ES18,17877
transformers/models/pop2piano/__init__.py,sha256=I2PPcFi-p0X5py7dLqobymv3E9g-mUv1QRn0luyPlIk,999
transformers/models/pop2piano/__pycache__/__init__.cpython-312.pyc,,
transformers/models/pop2piano/__pycache__/configuration_pop2piano.cpython-312.pyc,,
transformers/models/pop2piano/__pycache__/feature_extraction_pop2piano.cpython-312.pyc,,
transformers/models/pop2piano/__pycache__/modeling_pop2piano.cpython-312.pyc,,
transformers/models/pop2piano/__pycache__/processing_pop2piano.cpython-312.pyc,,
transformers/models/pop2piano/__pycache__/tokenization_pop2piano.cpython-312.pyc,,
transformers/models/pop2piano/configuration_pop2piano.py,sha256=aAnTDZdBrl19Kg6eOuPs13cz1_9ITlN7IgxysOqDGT4,5959
transformers/models/pop2piano/feature_extraction_pop2piano.py,sha256=Qf3TkSvUFoqyWGS6yTNF0rJefvF3ncWiF0X3TocABlU,19880
transformers/models/pop2piano/modeling_pop2piano.py,sha256=qxLOMQxU_lqM_hqnHVTCwzMz9e_IBsEfkgyusZWr0gs,72157
transformers/models/pop2piano/processing_pop2piano.py,sha256=8VHQrgsBsIHss09fV56-GLC8rt3YRZGGgqM5OISKZnI,5559
transformers/models/pop2piano/tokenization_pop2piano.py,sha256=q_DK9ZFVfprNQ1SvAc0P8ZcbABj87rnZB6-sFEnkyFs,32712
transformers/models/prophetnet/__init__.py,sha256=TYI21JDlj449kTgKAOtUBpuxVv5L_I70CDjofSZ627M,1044
transformers/models/prophetnet/__pycache__/__init__.cpython-312.pyc,,
transformers/models/prophetnet/__pycache__/configuration_prophetnet.cpython-312.pyc,,
transformers/models/prophetnet/__pycache__/modeling_prophetnet.cpython-312.pyc,,
transformers/models/prophetnet/__pycache__/tokenization_prophetnet.cpython-312.pyc,,
transformers/models/prophetnet/configuration_prophetnet.py,sha256=amGaPOOT0kJjxVPVJ2oyZN_XdC0LQ1LZVuJMheTCyF4,8903
transformers/models/prophetnet/modeling_prophetnet.py,sha256=_t8iOZeV0EsRd8kgU0pF9RrYUervyf517hQEZ2sP2mM,114690
transformers/models/prophetnet/tokenization_prophetnet.py,sha256=l3og-JwYmHAKWtv2yWv9X3lz1Y13DsmxTdRo4xCSb_Y,21236
transformers/models/pvt/__init__.py,sha256=wxkffT1tVLlQ14D466ickBR_-mjAZaV0vRLDkwKWBmE,1027
transformers/models/pvt/__pycache__/__init__.cpython-312.pyc,,
transformers/models/pvt/__pycache__/configuration_pvt.cpython-312.pyc,,
transformers/models/pvt/__pycache__/image_processing_pvt.cpython-312.pyc,,
transformers/models/pvt/__pycache__/modeling_pvt.cpython-312.pyc,,
transformers/models/pvt/configuration_pvt.py,sha256=H0cHrBRM-Ex9XbxEE3oHBRYDd1iMP58OpBKac7NOv6E,6962
transformers/models/pvt/image_processing_pvt.py,sha256=LeZI5PBVq_2_y-SrMPT-qoYVpbTzr2qwLlvdv2BrNsg,13864
transformers/models/pvt/modeling_pvt.py,sha256=w6w5Ty-6ikJjvcXBHHu0ONNJGN_-o55irN-WQLmMyFc,28491
transformers/models/pvt_v2/__init__.py,sha256=LkmqeLd7cZGKTFX_2d9_jU0sj_bDlML042kr_vMJTLw,993
transformers/models/pvt_v2/__pycache__/__init__.cpython-312.pyc,,
transformers/models/pvt_v2/__pycache__/configuration_pvt_v2.cpython-312.pyc,,
transformers/models/pvt_v2/__pycache__/modeling_pvt_v2.cpython-312.pyc,,
transformers/models/pvt_v2/configuration_pvt_v2.py,sha256=3UNQlRykqWBvv1gmg_t4EFFw8YkQyHKeRxWon0dvLxc,7991
transformers/models/pvt_v2/modeling_pvt_v2.py,sha256=6QVe1Pi2BFVyFrdl7GQrseSYD-MpGqU_LGYHKW0io7w,29516
transformers/models/qwen2/__init__.py,sha256=qoTTnT8A-pEg5kXdtnX0NgkIszex-35xul2PvJ3ab48,2434
transformers/models/qwen2/__pycache__/__init__.cpython-312.pyc,,
transformers/models/qwen2/__pycache__/configuration_qwen2.cpython-312.pyc,,
transformers/models/qwen2/__pycache__/modeling_qwen2.cpython-312.pyc,,
transformers/models/qwen2/__pycache__/modular_qwen2.cpython-312.pyc,,
transformers/models/qwen2/__pycache__/tokenization_qwen2.cpython-312.pyc,,
transformers/models/qwen2/__pycache__/tokenization_qwen2_fast.cpython-312.pyc,,
transformers/models/qwen2/configuration_qwen2.py,sha256=TT_El0erndC-ZYhCvTC4d72LtkTklLukhLmMGR45Uu0,10764
transformers/models/qwen2/modeling_qwen2.py,sha256=R7FsAHW_JAbqFxda07Y_wApy-iEHZ7UNaEI77HqLOEs,53706
transformers/models/qwen2/modular_qwen2.py,sha256=q5UIUvI_FiMPwsDlZGJWjlrqhGq3G__uQhzVkqKlF2g,5307
transformers/models/qwen2/tokenization_qwen2.py,sha256=y9hRJ6oYYRa_4UyoQUPU_BlsrnTPKoEByiCQ3zelSmE,13913
transformers/models/qwen2/tokenization_qwen2_fast.py,sha256=NL0QjEs36hiJUo0yu6X3-kp74LAjioKyoJeqnxhdsY8,5182
transformers/models/qwen2_5_vl/__init__.py,sha256=8-dsgLIeeE3n90n6F0XOu-tBZ-80Wotz89pjZi5GqjQ,1065
transformers/models/qwen2_5_vl/__pycache__/__init__.cpython-312.pyc,,
transformers/models/qwen2_5_vl/__pycache__/configuration_qwen2_5_vl.cpython-312.pyc,,
transformers/models/qwen2_5_vl/__pycache__/modeling_qwen2_5_vl.cpython-312.pyc,,
transformers/models/qwen2_5_vl/__pycache__/modular_qwen2_5_vl.cpython-312.pyc,,
transformers/models/qwen2_5_vl/__pycache__/processing_qwen2_5_vl.cpython-312.pyc,,
transformers/models/qwen2_5_vl/configuration_qwen2_5_vl.py,sha256=iz_tcDvK9ch2zxVwEnpjsVZ5skQe0Xmfrd2SVAAiT2g,13765
transformers/models/qwen2_5_vl/modeling_qwen2_5_vl.py,sha256=B2qbmJVavgA62Q-SQDcplrvHLZ8k4wM0byO3dzUQbNc,103533
transformers/models/qwen2_5_vl/modular_qwen2_5_vl.py,sha256=2oyUO8v8UlFZ5imwLBK7jTrwbh8wAxvTMleX9QhBgS0,47103
transformers/models/qwen2_5_vl/processing_qwen2_5_vl.py,sha256=hLJ5_lWQrxbVEHM_wnAfN1xI2CcEZEPXHmQ698By6Ac,11486
transformers/models/qwen2_audio/__init__.py,sha256=KaUmP3FK3GdeWvbunzyp1QjBki0USS4E80NlvhaJ3D8,1045
transformers/models/qwen2_audio/__pycache__/__init__.cpython-312.pyc,,
transformers/models/qwen2_audio/__pycache__/configuration_qwen2_audio.cpython-312.pyc,,
transformers/models/qwen2_audio/__pycache__/modeling_qwen2_audio.cpython-312.pyc,,
transformers/models/qwen2_audio/__pycache__/processing_qwen2_audio.cpython-312.pyc,,
transformers/models/qwen2_audio/configuration_qwen2_audio.py,sha256=XIpu1XkgfrpKbbou17XQ8Rxa0KoHBIKJGk5CGY2SaGE,8649
transformers/models/qwen2_audio/modeling_qwen2_audio.py,sha256=1O4oel42-7UCa6EPmuWYWWIQKoxzsXl8fLIufmX--Qc,67629
transformers/models/qwen2_audio/processing_qwen2_audio.py,sha256=bHnuweFNMDR1u3_fnwqi89b9nmN6b0VExdZpcU2bf_I,11959
transformers/models/qwen2_moe/__init__.py,sha256=TZM20WtUr1UyV-hDDgq5B-qFT4aUulMpjWwSUNdUs2w,999
transformers/models/qwen2_moe/__pycache__/__init__.cpython-312.pyc,,
transformers/models/qwen2_moe/__pycache__/configuration_qwen2_moe.cpython-312.pyc,,
transformers/models/qwen2_moe/__pycache__/modeling_qwen2_moe.cpython-312.pyc,,
transformers/models/qwen2_moe/configuration_qwen2_moe.py,sha256=3yAq3uhpRsgfEAQGkvLf2SaQcnGZkPXqJx40xaw5yT8,12993
transformers/models/qwen2_moe/modeling_qwen2_moe.py,sha256=bFYMsh1tTaoOVoJ7XZ6PbdxJJpr5f9jBuZDaJd39qy8,77502
transformers/models/qwen2_vl/__init__.py,sha256=MtNDD6sEQws-WTLwPxUL5UNd-UyDPrDh8yWzIAsRp-U,1131
transformers/models/qwen2_vl/__pycache__/__init__.cpython-312.pyc,,
transformers/models/qwen2_vl/__pycache__/configuration_qwen2_vl.cpython-312.pyc,,
transformers/models/qwen2_vl/__pycache__/image_processing_qwen2_vl.cpython-312.pyc,,
transformers/models/qwen2_vl/__pycache__/image_processing_qwen2_vl_fast.cpython-312.pyc,,
transformers/models/qwen2_vl/__pycache__/modeling_qwen2_vl.cpython-312.pyc,,
transformers/models/qwen2_vl/__pycache__/processing_qwen2_vl.cpython-312.pyc,,
transformers/models/qwen2_vl/configuration_qwen2_vl.py,sha256=Dy9OjQOcwQzemhgangdESmorNb_UphBHUHdxSBAmYZM,12433
transformers/models/qwen2_vl/image_processing_qwen2_vl.py,sha256=Lp18ID4PlVcmbF5DgIBDm6dKbxCKjTz11XlRWY5Akec,20923
transformers/models/qwen2_vl/image_processing_qwen2_vl_fast.py,sha256=qnrwALMQvkNAmTD8M0fL7NuI_GfOrWKU1-CLS2bumRU,18529
transformers/models/qwen2_vl/modeling_qwen2_vl.py,sha256=nLpGofi98iIAdCSheZWdVfM_pUXt1xPjn4nTE-WU_EQ,95991
transformers/models/qwen2_vl/processing_qwen2_vl.py,sha256=aoDu3-4N6EIPuCuHKZ9WoTPQlvHtMNrN447tgiM8vco,9513
transformers/models/rag/__init__.py,sha256=89sLlT4QJ96h0U-X6FmTdfSNJ8NjDjTpqyI1yK0L1Cw,1091
transformers/models/rag/__pycache__/__init__.cpython-312.pyc,,
transformers/models/rag/__pycache__/configuration_rag.cpython-312.pyc,,
transformers/models/rag/__pycache__/modeling_rag.cpython-312.pyc,,
transformers/models/rag/__pycache__/modeling_tf_rag.cpython-312.pyc,,
transformers/models/rag/__pycache__/retrieval_rag.cpython-312.pyc,,
transformers/models/rag/__pycache__/tokenization_rag.cpython-312.pyc,,
transformers/models/rag/configuration_rag.py,sha256=i6XOIi_KQ2Lft2GzrrlQQHnn8LJhhINqhEzMC5SgLPw,8513
transformers/models/rag/modeling_rag.py,sha256=W_0--XKi9KZVqzd58gEw62HmQnRId4zd7AtbSn_6Zjg,86376
transformers/models/rag/modeling_tf_rag.py,sha256=hwmDt8BCPOeWjh2lVfmhwcxONd0f8WgVH4TDQED7Y8I,88917
transformers/models/rag/retrieval_rag.py,sha256=Jmz6sr0QI-ailZ7CYQguZjTTK18kQxPGbDDxQckGZe4,29951
transformers/models/rag/tokenization_rag.py,sha256=3ZOzFZ1PV9sxaBkTt8F0tg5IFim-vwoyqH3ekeuHqIk,4606
transformers/models/recurrent_gemma/__init__.py,sha256=i86Cydx-eAdwsVMjNc0yG9hGxe_amyfAdvF5Eg-UCGM,1011
transformers/models/recurrent_gemma/__pycache__/__init__.cpython-312.pyc,,
transformers/models/recurrent_gemma/__pycache__/configuration_recurrent_gemma.cpython-312.pyc,,
transformers/models/recurrent_gemma/__pycache__/modeling_recurrent_gemma.cpython-312.pyc,,
transformers/models/recurrent_gemma/configuration_recurrent_gemma.py,sha256=ZsGiKPvFxUwJzd5xVUxJ2OkfapLGdNKJMbFEJFMcX9U,7750
transformers/models/recurrent_gemma/modeling_recurrent_gemma.py,sha256=8epFB2ySoW5F1TrY_AauJ_Sd82r0ckYSIvQt6WTi88E,41393
transformers/models/reformer/__init__.py,sha256=zjiMjHIRPssQ8pVa4fQ0zMCCn0ee_mtJt6wc9J23QYQ,1084
transformers/models/reformer/__pycache__/__init__.cpython-312.pyc,,
transformers/models/reformer/__pycache__/configuration_reformer.cpython-312.pyc,,
transformers/models/reformer/__pycache__/modeling_reformer.cpython-312.pyc,,
transformers/models/reformer/__pycache__/tokenization_reformer.cpython-312.pyc,,
transformers/models/reformer/__pycache__/tokenization_reformer_fast.cpython-312.pyc,,
transformers/models/reformer/configuration_reformer.py,sha256=ewfu4yWtk_TwV6MMzrkAtYcP9nkB-5Wv3Deh442Cb7M,13196
transformers/models/reformer/modeling_reformer.py,sha256=RE-n77Ro0i7BjdHr5CZM29s6oWagwiRcWH0M1XTaQoo,115643
transformers/models/reformer/tokenization_reformer.py,sha256=BqwwxreW56tOP7QBPUJY_f0yRnQUOv_e1v-ZLLK2keA,6760
transformers/models/reformer/tokenization_reformer_fast.py,sha256=nfNUC7uZtvyKAQfQql5OpEz9ColY6m6VH-qE4wL56Q8,4283
transformers/models/regnet/__init__.py,sha256=X_FU3wnZJ5KkCmRi4EyHk6ZUm_f0--YyyTS8lrknS9Y,1071
transformers/models/regnet/__pycache__/__init__.cpython-312.pyc,,
transformers/models/regnet/__pycache__/configuration_regnet.cpython-312.pyc,,
transformers/models/regnet/__pycache__/modeling_flax_regnet.cpython-312.pyc,,
transformers/models/regnet/__pycache__/modeling_regnet.cpython-312.pyc,,
transformers/models/regnet/__pycache__/modeling_tf_regnet.cpython-312.pyc,,
transformers/models/regnet/configuration_regnet.py,sha256=5_p_leo8Cvb4ZiHJGISKq_rGcTnNaw98LAG0sEBz_Pg,3974
transformers/models/regnet/modeling_flax_regnet.py,sha256=g0LNoW8SlhmHZ34MUOpewyAfycwvSSNXiv7_Ia4pNeY,28507
transformers/models/regnet/modeling_regnet.py,sha256=laDinWyMxbVrs-kYtFJ9UzAoFr3BBSt4mXqE8xwMuxY,17772
transformers/models/regnet/modeling_tf_regnet.py,sha256=n-24MtY8UVuuYKLVwkEaRAIv0QFxCpHXI_YD2pY_LK4,24391
transformers/models/rembert/__init__.py,sha256=Gif9TX1kvmD5iVWqsViSjxKYIDhR3FiBfp_QfA7U7i4,1119
transformers/models/rembert/__pycache__/__init__.cpython-312.pyc,,
transformers/models/rembert/__pycache__/configuration_rembert.cpython-312.pyc,,
transformers/models/rembert/__pycache__/modeling_rembert.cpython-312.pyc,,
transformers/models/rembert/__pycache__/modeling_tf_rembert.cpython-312.pyc,,
transformers/models/rembert/__pycache__/tokenization_rembert.cpython-312.pyc,,
transformers/models/rembert/__pycache__/tokenization_rembert_fast.cpython-312.pyc,,
transformers/models/rembert/configuration_rembert.py,sha256=VvcwZWc3akBef7VeiDAGMx0inuob_zilhrGsKvl9smA,7291
transformers/models/rembert/modeling_rembert.py,sha256=RCjtCOqM4NpwU8yg2o0onPR5V_DxS0k5xKHmjFM-0Ms,67339
transformers/models/rembert/modeling_tf_rembert.py,sha256=99WKl_IgABQlt1O58QnCnGInogXh0HSzlvTOrmCedzY,77981
transformers/models/rembert/tokenization_rembert.py,sha256=Y-sstejHDNDw-Cb3kJbvEN_j3g2zaGkzIpx0QntkHII,10625
transformers/models/rembert/tokenization_rembert_fast.py,sha256=t_YjcmDD5MzFoQ9Wl2aH2IBUUTSRJFcy4EHtZJYZu_Y,10032
transformers/models/resnet/__init__.py,sha256=NCgMoczDbEI_XDWkWNWKIKGPYeohOC95f0o2X-Vh2vA,1071
transformers/models/resnet/__pycache__/__init__.cpython-312.pyc,,
transformers/models/resnet/__pycache__/configuration_resnet.cpython-312.pyc,,
transformers/models/resnet/__pycache__/modeling_flax_resnet.cpython-312.pyc,,
transformers/models/resnet/__pycache__/modeling_resnet.cpython-312.pyc,,
transformers/models/resnet/__pycache__/modeling_tf_resnet.cpython-312.pyc,,
transformers/models/resnet/configuration_resnet.py,sha256=K8n9ba6A2OiIqmFAfTJKQUF4q8o9xgq14xdZisEpEqc,6067
transformers/models/resnet/modeling_flax_resnet.py,sha256=RsRBMcXQ7NDoMx3L0ip7Tfu8eNOCwrDI6KUkCpxccsg,24704
transformers/models/resnet/modeling_resnet.py,sha256=DpNfzcFFTi8r4YHsmzNQGX8ZB0QcUUV8iGwEEFlWBwY,19891
transformers/models/resnet/modeling_tf_resnet.py,sha256=nSUzzdq6HBE1qhxMvI5tUc3O2_Hyjj2tvNACMQgKPFE,23741
transformers/models/roberta/__init__.py,sha256=p1qYu_9qpmxsxMfXuoxK-VrmRQMEshwiM8Ekoij2J1M,1160
transformers/models/roberta/__pycache__/__init__.cpython-312.pyc,,
transformers/models/roberta/__pycache__/configuration_roberta.cpython-312.pyc,,
transformers/models/roberta/__pycache__/modeling_flax_roberta.cpython-312.pyc,,
transformers/models/roberta/__pycache__/modeling_roberta.cpython-312.pyc,,
transformers/models/roberta/__pycache__/modeling_tf_roberta.cpython-312.pyc,,
transformers/models/roberta/__pycache__/tokenization_roberta.cpython-312.pyc,,
transformers/models/roberta/__pycache__/tokenization_roberta_fast.cpython-312.pyc,,
transformers/models/roberta/configuration_roberta.py,sha256=r1rJghjnlXorwp-ZqR45IAKaZJMMORD_or7GHfk8dgY,7311
transformers/models/roberta/modeling_flax_roberta.py,sha256=lPTxEfaLr4zkIq_Jm404y8fQCR3szkxTVgA1QAXBO3w,57270
transformers/models/roberta/modeling_roberta.py,sha256=SDdThW-8V1hLsrjGGGPY4IA4t5Jm2gHGB7gfA4FOhIY,77868
transformers/models/roberta/modeling_tf_roberta.py,sha256=4UTC7zcqsFTqHzUQjPL0Mfh8A1-SDEzkIxeB2T3RyCk,80179
transformers/models/roberta/tokenization_roberta.py,sha256=5wuB8fCNXGXk4svqPNOcXkR9mLA12JvYqr9TlpVECrg,16484
transformers/models/roberta/tokenization_roberta_fast.py,sha256=Ay29BGVvDcsd91D4yVPhYLNI21-DLWlGweeegTjZ-vU,10991
transformers/models/roberta_prelayernorm/__init__.py,sha256=QsVJJaoujnLHyCgwSsz53MV88vI183tTGJNXHDCHCAc,1127
transformers/models/roberta_prelayernorm/__pycache__/__init__.cpython-312.pyc,,
transformers/models/roberta_prelayernorm/__pycache__/configuration_roberta_prelayernorm.cpython-312.pyc,,
transformers/models/roberta_prelayernorm/__pycache__/modeling_flax_roberta_prelayernorm.cpython-312.pyc,,
transformers/models/roberta_prelayernorm/__pycache__/modeling_roberta_prelayernorm.cpython-312.pyc,,
transformers/models/roberta_prelayernorm/__pycache__/modeling_tf_roberta_prelayernorm.cpython-312.pyc,,
transformers/models/roberta_prelayernorm/configuration_roberta_prelayernorm.py,sha256=DM0trohLskvy5OYLcDjpEa5ri-htNy5dgISllI3b0og,7883
transformers/models/roberta_prelayernorm/modeling_flax_roberta_prelayernorm.py,sha256=HiEkWv_mTjRU6eT9er8jlJmLROt0RMafp8MJKct3C2A,60927
transformers/models/roberta_prelayernorm/modeling_roberta_prelayernorm.py,sha256=rfd5vX5_fmnXB2jbaB23-onhLKiL5qCYBZ_hqKby3m0,72774
transformers/models/roberta_prelayernorm/modeling_tf_roberta_prelayernorm.py,sha256=9Ow8t-WqDGGDcUQB2KGyDZPOn1NCouxMmVAUsfL8FCE,83452
transformers/models/roc_bert/__init__.py,sha256=4CveMGU-dY3nV4E6x-Xpb1jicRniwrPuSOrY8-SHIUI,1038
transformers/models/roc_bert/__pycache__/__init__.cpython-312.pyc,,
transformers/models/roc_bert/__pycache__/configuration_roc_bert.cpython-312.pyc,,
transformers/models/roc_bert/__pycache__/modeling_roc_bert.cpython-312.pyc,,
transformers/models/roc_bert/__pycache__/tokenization_roc_bert.cpython-312.pyc,,
transformers/models/roc_bert/configuration_roc_bert.py,sha256=XpKYrVUjci1ykruLmUKTUlUc7RjHXq7AW71w2wc6ars,8528
transformers/models/roc_bert/modeling_roc_bert.py,sha256=D7U54JtcnXkLgevFhyXobl1dBL4DOoIcF7ZyLVnuPxY,93476
transformers/models/roc_bert/tokenization_roc_bert.py,sha256=5HdBmjNFHR3uJUoiTnU8WJS2zzrgMnmDFshlDeBLa6M,50739
transformers/models/roformer/__init__.py,sha256=v1CIjowYMq6aN-V9gyl-RWlMi_uQQxopuvEv76geFqk,1166
transformers/models/roformer/__pycache__/__init__.cpython-312.pyc,,
transformers/models/roformer/__pycache__/configuration_roformer.cpython-312.pyc,,
transformers/models/roformer/__pycache__/modeling_flax_roformer.cpython-312.pyc,,
transformers/models/roformer/__pycache__/modeling_roformer.cpython-312.pyc,,
transformers/models/roformer/__pycache__/modeling_tf_roformer.cpython-312.pyc,,
transformers/models/roformer/__pycache__/tokenization_roformer.cpython-312.pyc,,
transformers/models/roformer/__pycache__/tokenization_roformer_fast.cpython-312.pyc,,
transformers/models/roformer/__pycache__/tokenization_utils.cpython-312.pyc,,
transformers/models/roformer/configuration_roformer.py,sha256=lqqzrsI95wVya5jcUoeUjQmTy76AEtTEEwJ-1TLNIcE,6856
transformers/models/roformer/modeling_flax_roformer.py,sha256=MsQ62YShLRpfv_DA9twbYm4k12JCiKlWQDHdpICSElw,39370
transformers/models/roformer/modeling_roformer.py,sha256=JRG7pxDw8WDtVF1aytfecvMom-NDtzw48aQfPcVbe08,68513
transformers/models/roformer/modeling_tf_roformer.py,sha256=F28YCrWA8MamBsLo0oEA557uatj4Oj1SPhT7gnMA2Xw,66222
transformers/models/roformer/tokenization_roformer.py,sha256=T5xTBpn2oJicg8O6Ooivi-n2Sw-vrlhmSp6tNdZQ_qw,22011
transformers/models/roformer/tokenization_roformer_fast.py,sha256=b2p3BdYUv-AcUpG-6zbUK5XRliuqtd8trcwuORkg5XU,6717
transformers/models/roformer/tokenization_utils.py,sha256=0ciH13qW2kCa5my1rPwfwAuSXX-jGzN0nzemvGvOBxw,2652
transformers/models/rt_detr/__init__.py,sha256=c9Y3NeKQwBP46tyFF99kjqTngoIWhLMq7XvzEJOfLaY,1181
transformers/models/rt_detr/__pycache__/__init__.cpython-312.pyc,,
transformers/models/rt_detr/__pycache__/configuration_rt_detr.cpython-312.pyc,,
transformers/models/rt_detr/__pycache__/configuration_rt_detr_resnet.cpython-312.pyc,,
transformers/models/rt_detr/__pycache__/image_processing_rt_detr.cpython-312.pyc,,
transformers/models/rt_detr/__pycache__/image_processing_rt_detr_fast.cpython-312.pyc,,
transformers/models/rt_detr/__pycache__/modeling_rt_detr.cpython-312.pyc,,
transformers/models/rt_detr/__pycache__/modeling_rt_detr_resnet.cpython-312.pyc,,
transformers/models/rt_detr/__pycache__/modular_rt_detr.cpython-312.pyc,,
transformers/models/rt_detr/configuration_rt_detr.py,sha256=-nrNyKF-E1k_sWYe29bD54Ab51MKcZc2CN4pVXaIygc,18070
transformers/models/rt_detr/configuration_rt_detr_resnet.py,sha256=SRxquPIXRdu5Xs6YWQgtzYyT3cyoIEig2KKpNeSFfKQ,5557
transformers/models/rt_detr/image_processing_rt_detr.py,sha256=G7AEnUq7Gs835nKR1i-tNHoUx0rO4PpM63QdSIfbHpQ,51648
transformers/models/rt_detr/image_processing_rt_detr_fast.py,sha256=WbW4ipTXrWmZbd-tl6E1SLSbT4ktHo8BQiBFhW9jlVE,28102
transformers/models/rt_detr/modeling_rt_detr.py,sha256=_AmqcoUXDeKW8ordBApQ6HDYldL2s_Vrs9zkCEzavSE,103833
transformers/models/rt_detr/modeling_rt_detr_resnet.py,sha256=r8jDRnj_U6iq2ddtoh2Ay2kHHZFRbmnSk-inIb0yzeg,16462
transformers/models/rt_detr/modular_rt_detr.py,sha256=supqv0dOUOnwpyuLPxsFgZFmbUwZTCifr_EFpKF1jk4,18196
transformers/models/rt_detr_v2/__init__.py,sha256=7RL5U-hsGt3HQZ5SuWn8iZY_L166EYswBvaQXFRkzRc,1003
transformers/models/rt_detr_v2/__pycache__/__init__.cpython-312.pyc,,
transformers/models/rt_detr_v2/__pycache__/configuration_rt_detr_v2.cpython-312.pyc,,
transformers/models/rt_detr_v2/__pycache__/modeling_rt_detr_v2.cpython-312.pyc,,
transformers/models/rt_detr_v2/__pycache__/modular_rt_detr_v2.cpython-312.pyc,,
transformers/models/rt_detr_v2/configuration_rt_detr_v2.py,sha256=WSiwJSduunro0HncAvSvXsvIfxweNcAc8NfgY0RPfKA,19622
transformers/models/rt_detr_v2/modeling_rt_detr_v2.py,sha256=HCfyI54RUeB82jjI-xW4Mp2xYXeIaBH2iL5wkXR3yMc,101642
transformers/models/rt_detr_v2/modular_rt_detr_v2.py,sha256=JQEB1yMfaRiMjp2VsPp35XEz5MTp1lRLGv9XLs6cVwM,29530
transformers/models/rwkv/__init__.py,sha256=HAiwEvW1j_xuHj_PbmN25srY9RtA1gLmN_0RWvAyG78,989
transformers/models/rwkv/__pycache__/__init__.cpython-312.pyc,,
transformers/models/rwkv/__pycache__/configuration_rwkv.cpython-312.pyc,,
transformers/models/rwkv/__pycache__/modeling_rwkv.cpython-312.pyc,,
transformers/models/rwkv/configuration_rwkv.py,sha256=SEMdtDGoCzSlG7sgHlc-I2WbIFsIBz00qrJdSUZ1xkY,5203
transformers/models/rwkv/modeling_rwkv.py,sha256=gyEjvwdimPYOD9Q7tw2JPdCKaJPRwOBuGZnxk27QALU,36786
transformers/models/sam/__init__.py,sha256=vLpuKLgQZgbv3WGjn6Kr4bawb_4ZmYsrpNg2ojKkHiE,1096
transformers/models/sam/__pycache__/__init__.cpython-312.pyc,,
transformers/models/sam/__pycache__/configuration_sam.cpython-312.pyc,,
transformers/models/sam/__pycache__/image_processing_sam.cpython-312.pyc,,
transformers/models/sam/__pycache__/modeling_sam.cpython-312.pyc,,
transformers/models/sam/__pycache__/modeling_tf_sam.cpython-312.pyc,,
transformers/models/sam/__pycache__/processing_sam.cpython-312.pyc,,
transformers/models/sam/configuration_sam.py,sha256=nBf9jKHgrI_OS49j-1laQ-IloCTPb12dWXHffcCYaL4,14165
transformers/models/sam/image_processing_sam.py,sha256=olmPjkuvl2mqnCxAXoxztRH1Z5AcUL53kRHH5lAOtQs,67632
transformers/models/sam/modeling_sam.py,sha256=o_9LYX8IQk8ZqM0DN4G-Aab_JmMhQaGNLR87S9IPnxs,71324
transformers/models/sam/modeling_tf_sam.py,sha256=Gx3NwM484-V3uBUeIV0qvcT7tcBLJhBrLVE-RUr91mA,75501
transformers/models/sam/processing_sam.py,sha256=BhxmbHKBC27rWGe23WUSZn9SNTTLgt4qdYXxHBkadbk,12930
transformers/models/seamless_m4t/__init__.py,sha256=Y5c_W1E83fh8ToTMqF4NcReXzKZiTDv3A4ePoNUxXDg,1194
transformers/models/seamless_m4t/__pycache__/__init__.cpython-312.pyc,,
transformers/models/seamless_m4t/__pycache__/configuration_seamless_m4t.cpython-312.pyc,,
transformers/models/seamless_m4t/__pycache__/feature_extraction_seamless_m4t.cpython-312.pyc,,
transformers/models/seamless_m4t/__pycache__/modeling_seamless_m4t.cpython-312.pyc,,
transformers/models/seamless_m4t/__pycache__/processing_seamless_m4t.cpython-312.pyc,,
transformers/models/seamless_m4t/__pycache__/tokenization_seamless_m4t.cpython-312.pyc,,
transformers/models/seamless_m4t/__pycache__/tokenization_seamless_m4t_fast.cpython-312.pyc,,
transformers/models/seamless_m4t/configuration_seamless_m4t.py,sha256=P7ZjMZwVx_BXv6Thl_qeWHrW5yVLKsRKgecZAgCPZW8,23497
transformers/models/seamless_m4t/feature_extraction_seamless_m4t.py,sha256=q1tQ_fL_rO89GaohPfh9fUVXmxvGEuYc6rVEZL_k4k8,13643
transformers/models/seamless_m4t/modeling_seamless_m4t.py,sha256=0I4GgU9kiRPF0wvt1gvn11H3AS5KK3wyC_9Mbi34sgc,199258
transformers/models/seamless_m4t/processing_seamless_m4t.py,sha256=voYavxsi-yG0I8lSsGmevP2zKW-qB2CP-kk3-jMDwPo,5930
transformers/models/seamless_m4t/tokenization_seamless_m4t.py,sha256=cf1SIsG5Cxw368x7uMN58scaFS_aq2se5E3PSMnl0UE,26013
transformers/models/seamless_m4t/tokenization_seamless_m4t_fast.py,sha256=UMmq_S9I2tGjVWc9dYATqF-wenc3pxA7gy7oV7CPhC8,19926
transformers/models/seamless_m4t_v2/__init__.py,sha256=mMY04PBMrOwTIQLq01RHqZjssvrSYl3UDhP5Y5vFifs,1011
transformers/models/seamless_m4t_v2/__pycache__/__init__.cpython-312.pyc,,
transformers/models/seamless_m4t_v2/__pycache__/configuration_seamless_m4t_v2.cpython-312.pyc,,
transformers/models/seamless_m4t_v2/__pycache__/modeling_seamless_m4t_v2.cpython-312.pyc,,
transformers/models/seamless_m4t_v2/configuration_seamless_m4t_v2.py,sha256=AGu4RlVCJkC5iGAvJGJhMFzJ7i-uNSkgmMfJHbdXS7w,24356
transformers/models/seamless_m4t_v2/modeling_seamless_m4t_v2.py,sha256=b8gybvvLjIQULbFtsbEw5_E1nJPkvBHIhxQbI7ixj9Q,225709
transformers/models/segformer/__init__.py,sha256=ITklna1wOGVI09TgGcRxn-rc2tYosLRov_Un0n5XHPo,1134
transformers/models/segformer/__pycache__/__init__.cpython-312.pyc,,
transformers/models/segformer/__pycache__/configuration_segformer.cpython-312.pyc,,
transformers/models/segformer/__pycache__/feature_extraction_segformer.cpython-312.pyc,,
transformers/models/segformer/__pycache__/image_processing_segformer.cpython-312.pyc,,
transformers/models/segformer/__pycache__/modeling_segformer.cpython-312.pyc,,
transformers/models/segformer/__pycache__/modeling_tf_segformer.cpython-312.pyc,,
transformers/models/segformer/configuration_segformer.py,sha256=R4qcxjTrvHA3O2YyJ0MFPhR9FXGX1ynClDeK44loqU8,7420
transformers/models/segformer/feature_extraction_segformer.py,sha256=dEAG-c8JoEQl2nvT9wXx_DasFq4pSHYHGZuxnc5ErWc,1249
transformers/models/segformer/image_processing_segformer.py,sha256=9qk77S-AYykNLnAVUmO_WPfHKaWHt2jAoXNu7i1ynM8,22784
transformers/models/segformer/modeling_segformer.py,sha256=eyY9KeHwf68MMmGZaJe3q1NwCqL6hGFl_GMMfFIWxoA,35550
transformers/models/segformer/modeling_tf_segformer.py,sha256=6ggHaUQkVHRGWCp95lhKf55eGM2F51yGwyJ9c9v0SRM,43796
transformers/models/seggpt/__init__.py,sha256=RzV8DKCX1lOWGqXv2BlE1R7T4QuEcdYAVy_csccLvEw,1036
transformers/models/seggpt/__pycache__/__init__.cpython-312.pyc,,
transformers/models/seggpt/__pycache__/configuration_seggpt.cpython-312.pyc,,
transformers/models/seggpt/__pycache__/image_processing_seggpt.cpython-312.pyc,,
transformers/models/seggpt/__pycache__/modeling_seggpt.cpython-312.pyc,,
transformers/models/seggpt/configuration_seggpt.py,sha256=_v6RESyTwPgfynZR8xjyJKuB_mRXBbVylb7AJ8USaKg,6492
transformers/models/seggpt/image_processing_seggpt.py,sha256=rEy1Mcx8UD9dFvfU5KN8wCbY-_y9xp3kP8IrAMKpaeY,31509
transformers/models/seggpt/modeling_seggpt.py,sha256=lHXpLoRSWXckFfCR2sxDPs47AiB2u6ck2iQaP1wCA2M,46275
transformers/models/sew/__init__.py,sha256=POCF36ZRa_dr7oQhkDU2X17bsZuLoWI5V8DSihqr_vU,987
transformers/models/sew/__pycache__/__init__.cpython-312.pyc,,
transformers/models/sew/__pycache__/configuration_sew.cpython-312.pyc,,
transformers/models/sew/__pycache__/modeling_sew.cpython-312.pyc,,
transformers/models/sew/configuration_sew.py,sha256=3gBVqNaBxEehYEU-MXTunHM6qmDqQub35vOmjiirDiA,14207
transformers/models/sew/modeling_sew.py,sha256=OoqcODgmBRz78H1wh80PbNI-ZobvrvPsU1w1eJhAd-M,67413
transformers/models/sew_d/__init__.py,sha256=zE9sw10e_a1d-8-Jsb75z5frCjkFGD0dZMHAXiNgGwk,991
transformers/models/sew_d/__pycache__/__init__.cpython-312.pyc,,
transformers/models/sew_d/__pycache__/configuration_sew_d.cpython-312.pyc,,
transformers/models/sew_d/__pycache__/modeling_sew_d.cpython-312.pyc,,
transformers/models/sew_d/configuration_sew_d.py,sha256=QKAeDcDJfVbPCLuxqBrAkXrOU8BeGRaYNkot83YKt-4,16175
transformers/models/sew_d/modeling_sew_d.py,sha256=6veDdr--1kTMM6Fhl2fzTbz8AxKxdUkWfKk-XAklvIw,72858
transformers/models/siglip/__init__.py,sha256=CnNqbSQ25tKLz0MGJVmhSXjVyASRDu7v5yjTHWYZ6M4,1160
transformers/models/siglip/__pycache__/__init__.cpython-312.pyc,,
transformers/models/siglip/__pycache__/configuration_siglip.cpython-312.pyc,,
transformers/models/siglip/__pycache__/image_processing_siglip.cpython-312.pyc,,
transformers/models/siglip/__pycache__/image_processing_siglip_fast.cpython-312.pyc,,
transformers/models/siglip/__pycache__/modeling_siglip.cpython-312.pyc,,
transformers/models/siglip/__pycache__/processing_siglip.cpython-312.pyc,,
transformers/models/siglip/__pycache__/tokenization_siglip.cpython-312.pyc,,
transformers/models/siglip/configuration_siglip.py,sha256=buZJADRSQs_QHesht7h6QxkFaZNDgI9EFtyCcr85oMA,11947
transformers/models/siglip/image_processing_siglip.py,sha256=cNEIEpmxSkNcxu-hwIC_Et_okpSF46dQyqygjHPp92o,11963
transformers/models/siglip/image_processing_siglip_fast.py,sha256=82jjDbNyXCf1MEFdn8SoIC1uiSYD2rlEPrWnug4efLM,1399
transformers/models/siglip/modeling_siglip.py,sha256=NnCV3w0TmxqRdeIuZFuk2iCGwNgYiBLpw8FkMe7ovMQ,69422
transformers/models/siglip/processing_siglip.py,sha256=ReTGUQAWrCNTT9gdw4_T0qAxcK5sn_56w27z3LgK1es,7334
transformers/models/siglip/tokenization_siglip.py,sha256=Lrv4eyGkAKU5AZwaR-Wg6vzZyP-pTkB1q6LMwA9mhw0,15984
transformers/models/speech_encoder_decoder/__init__.py,sha256=0MwevN904dCSAb0dvznhDH--q-m3-MzdCtx0B-T5hpk,1081
transformers/models/speech_encoder_decoder/__pycache__/__init__.cpython-312.pyc,,
transformers/models/speech_encoder_decoder/__pycache__/configuration_speech_encoder_decoder.cpython-312.pyc,,
transformers/models/speech_encoder_decoder/__pycache__/modeling_flax_speech_encoder_decoder.cpython-312.pyc,,
transformers/models/speech_encoder_decoder/__pycache__/modeling_speech_encoder_decoder.cpython-312.pyc,,
transformers/models/speech_encoder_decoder/configuration_speech_encoder_decoder.py,sha256=vdMSRuVo6eOhpM2JVkwhCd28NbdJHDm6OBTCA914cm0,4683
transformers/models/speech_encoder_decoder/modeling_flax_speech_encoder_decoder.py,sha256=MDoGAgEKzMfIehXrt1P9rJpax_2-L-Cy8fz-p2F4ySA,44688
transformers/models/speech_encoder_decoder/modeling_speech_encoder_decoder.py,sha256=auRqA88dfmY9yo2j26Ksdk6c342QNooILZAI0cmGb0c,32125
transformers/models/speech_to_text/__init__.py,sha256=qZzt5u1rbSsOjPVmX40R4b4pkL1mxOQZ66q8GPDKao8,1200
transformers/models/speech_to_text/__pycache__/__init__.cpython-312.pyc,,
transformers/models/speech_to_text/__pycache__/configuration_speech_to_text.cpython-312.pyc,,
transformers/models/speech_to_text/__pycache__/feature_extraction_speech_to_text.cpython-312.pyc,,
transformers/models/speech_to_text/__pycache__/modeling_speech_to_text.cpython-312.pyc,,
transformers/models/speech_to_text/__pycache__/modeling_tf_speech_to_text.cpython-312.pyc,,
transformers/models/speech_to_text/__pycache__/processing_speech_to_text.cpython-312.pyc,,
transformers/models/speech_to_text/__pycache__/tokenization_speech_to_text.cpython-312.pyc,,
transformers/models/speech_to_text/configuration_speech_to_text.py,sha256=akMZy_BM5rCB0g-DIVFsf5GwGTL1TxQ1eK4swvOK8pM,9809
transformers/models/speech_to_text/feature_extraction_speech_to_text.py,sha256=PkD2ItQrXUUDkmZN3wllBmj-UbQ5AiclIyb-ISlwM_Q,13232
transformers/models/speech_to_text/modeling_speech_to_text.py,sha256=8SrsX3TMGrEu4iDjGbS07MadjaT1nNoI9IdESdRZwxY,63640
transformers/models/speech_to_text/modeling_tf_speech_to_text.py,sha256=7iGWE1TPaKcb9E9G4aW6Nw-EDGiB9RTHn9CQmGH2Z1U,74420
transformers/models/speech_to_text/processing_speech_to_text.py,sha256=cfHZTf5_O9jOXg9gAtqBc6diEpQw4OynmqgTkl-Lj2I,4856
transformers/models/speech_to_text/tokenization_speech_to_text.py,sha256=CXVyp4Lze3PMbwmqmB0xVhtshsFVWfFri_zPlcRFBBU,11438
transformers/models/speecht5/__init__.py,sha256=DploRLnZX4ZO40Z7BstCZ7aNWGuZE06tIeMo0GTyR60,1124
transformers/models/speecht5/__pycache__/__init__.cpython-312.pyc,,
transformers/models/speecht5/__pycache__/configuration_speecht5.cpython-312.pyc,,
transformers/models/speecht5/__pycache__/feature_extraction_speecht5.cpython-312.pyc,,
transformers/models/speecht5/__pycache__/modeling_speecht5.cpython-312.pyc,,
transformers/models/speecht5/__pycache__/number_normalizer.cpython-312.pyc,,
transformers/models/speecht5/__pycache__/processing_speecht5.cpython-312.pyc,,
transformers/models/speecht5/__pycache__/tokenization_speecht5.cpython-312.pyc,,
transformers/models/speecht5/configuration_speecht5.py,sha256=FaCOuh7O7jhrWQEaFWmj0WpGgrKLZ9ziSOAduEVkQAk,23434
transformers/models/speecht5/feature_extraction_speecht5.py,sha256=9PHkKDOahe7l0m-AnAFpiNfSBW30jmT6YXuPUVJHADM,17850
transformers/models/speecht5/modeling_speecht5.py,sha256=4IlEVg_SYgF4VEMRQGxpId5rESA3NE7rYiW9GJcB7lo,154653
transformers/models/speecht5/number_normalizer.py,sha256=cxnEUdHSISW5eAo15cLuVkZa65zMFuMFaJ8zAOQCsAA,7019
transformers/models/speecht5/processing_speecht5.py,sha256=lp8lCue0tNo3xQVqlHpzruReD0iGUZeNz4KRsXP12rg,7596
transformers/models/speecht5/tokenization_speecht5.py,sha256=eeIqgfTtt_OTdg_uPl6G2il1zvmtLoMI728DroM-pUg,8946
transformers/models/splinter/__init__.py,sha256=N3tdgJIqZRPK0g3pfLE3p3-HkGJMRf-GQ189anQ51to,1084
transformers/models/splinter/__pycache__/__init__.cpython-312.pyc,,
transformers/models/splinter/__pycache__/configuration_splinter.cpython-312.pyc,,
transformers/models/splinter/__pycache__/modeling_splinter.cpython-312.pyc,,
transformers/models/splinter/__pycache__/tokenization_splinter.cpython-312.pyc,,
transformers/models/splinter/__pycache__/tokenization_splinter_fast.cpython-312.pyc,,
transformers/models/splinter/configuration_splinter.py,sha256=ZajZPX6f9K7gBqp2PbOtmJg-_fAU8h72tKdTNjyQV0M,5625
transformers/models/splinter/modeling_splinter.py,sha256=unKeBbKPkZ1-yMDOGxAUCPBkNTlo1ENK4TWeuhGj1qs,53454
transformers/models/splinter/tokenization_splinter.py,sha256=a-Z2ZgRffsIVGn122hHRjnkab5KDx900uPbCzzKzKUs,20981
transformers/models/splinter/tokenization_splinter_fast.py,sha256=Y4D08-btQwQepNw4ZDAr-y-lkHbqRIcKn8HeL4zaXW8,8603
transformers/models/squeezebert/__init__.py,sha256=_kzQtfoJetCK99e_FICGZl5DN8S2VVcOUFioGyN0sLI,1096
transformers/models/squeezebert/__pycache__/__init__.cpython-312.pyc,,
transformers/models/squeezebert/__pycache__/configuration_squeezebert.cpython-312.pyc,,
transformers/models/squeezebert/__pycache__/modeling_squeezebert.cpython-312.pyc,,
transformers/models/squeezebert/__pycache__/tokenization_squeezebert.cpython-312.pyc,,
transformers/models/squeezebert/__pycache__/tokenization_squeezebert_fast.cpython-312.pyc,,
transformers/models/squeezebert/configuration_squeezebert.py,sha256=24rAypu_QmOVu_CTO_e6hos_xEtnPVQZHmsEVv-F3mk,7303
transformers/models/squeezebert/modeling_squeezebert.py,sha256=ERtHnCeYCB-PTacSp7HANHxFxxDn0Rbf-BmQD1SPXYU,45342
transformers/models/squeezebert/tokenization_squeezebert.py,sha256=gNsEVyb_FE47-orxA4xDVidgTGEUoLODjdK38H9XD6Y,21248
transformers/models/squeezebert/tokenization_squeezebert_fast.py,sha256=uZvcCVHG-ObsxQlwlcMTAL6sTgBAjFtMjKJvP42ga3o,7860
transformers/models/stablelm/__init__.py,sha256=aVgWTcwBuuiGJDp8H_ZU6BvhYqjmNEqCukU7jEfwd_I,997
transformers/models/stablelm/__pycache__/__init__.cpython-312.pyc,,
transformers/models/stablelm/__pycache__/configuration_stablelm.cpython-312.pyc,,
transformers/models/stablelm/__pycache__/modeling_stablelm.cpython-312.pyc,,
transformers/models/stablelm/configuration_stablelm.py,sha256=qAiB-_tQupM4gJBL3Fslpww60FMGyAgRsxGSi8PpttA,10837
transformers/models/stablelm/modeling_stablelm.py,sha256=KNHCcFWg7sA7EQXv85X22WRYWi8fG88XuUHH3BI_kzg,64520
transformers/models/starcoder2/__init__.py,sha256=fZ8HHZCGjxRfVgROe7zuoi9ADIAa4SeqxGHkvKUQiQM,1001
transformers/models/starcoder2/__pycache__/__init__.cpython-312.pyc,,
transformers/models/starcoder2/__pycache__/configuration_starcoder2.cpython-312.pyc,,
transformers/models/starcoder2/__pycache__/modeling_starcoder2.cpython-312.pyc,,
transformers/models/starcoder2/__pycache__/modular_starcoder2.cpython-312.pyc,,
transformers/models/starcoder2/configuration_starcoder2.py,sha256=mxuXeE-BCNRdz6U3j1E6MsTQkKyZqWtxGfVblcCBFI4,10892
transformers/models/starcoder2/modeling_starcoder2.py,sha256=4bDw_dJLPfcj8uJiIvXz4sUN4VEs--Z8Oz1TNyjcWH4,50204
transformers/models/starcoder2/modular_starcoder2.py,sha256=TUZIcLaG90eVy1V12XTMMr-1UQPwSVmpKpdsQTr5fso,11496
transformers/models/superglue/__init__.py,sha256=Sg_nfSbBltkVhp40pVc04SthUCnXMX3kWHH_qC_YL4Y,1045
transformers/models/superglue/__pycache__/__init__.cpython-312.pyc,,
transformers/models/superglue/__pycache__/configuration_superglue.cpython-312.pyc,,
transformers/models/superglue/__pycache__/image_processing_superglue.cpython-312.pyc,,
transformers/models/superglue/__pycache__/modeling_superglue.cpython-312.pyc,,
transformers/models/superglue/configuration_superglue.py,sha256=EzSi1gp8DnOurYwR2COc15e6MIhcgusjQs_QWOiTTVo,5402
transformers/models/superglue/image_processing_superglue.py,sha256=4HrRpIyMsG5zXYp05BkLqQ8h-9x7WjpAP7JI-0CWzl4,18718
transformers/models/superglue/modeling_superglue.py,sha256=yJcDr_lZrvYBsy8nU7QNNGxOT__FvqbtBEFDq34aYto,39714
transformers/models/superpoint/__init__.py,sha256=CeDGkon6FhcDhbdXs9IlLKFmS1d3THdAB5p4mH6gZ_M,1048
transformers/models/superpoint/__pycache__/__init__.cpython-312.pyc,,
transformers/models/superpoint/__pycache__/configuration_superpoint.cpython-312.pyc,,
transformers/models/superpoint/__pycache__/image_processing_superpoint.cpython-312.pyc,,
transformers/models/superpoint/__pycache__/modeling_superpoint.cpython-312.pyc,,
transformers/models/superpoint/configuration_superpoint.py,sha256=F6qo1YZWmIv83xPGzhvMpBDD7Kfk8EVJJg39CnkEF6g,4072
transformers/models/superpoint/image_processing_superpoint.py,sha256=5v6jxKITWJDghe9eGIiOfp0pSDKMXCc-6TGpyBs8YbU,15860
transformers/models/superpoint/modeling_superpoint.py,sha256=_2whda8SKT-T5hBU3LrFqG9Z-sN6JnxXhtzC3JuKi3c,21654
transformers/models/swiftformer/__init__.py,sha256=cW3-9efPxdjZV1KziM8j1S8e8wH3wJQhWqMXlULhG6c,1046
transformers/models/swiftformer/__pycache__/__init__.cpython-312.pyc,,
transformers/models/swiftformer/__pycache__/configuration_swiftformer.cpython-312.pyc,,
transformers/models/swiftformer/__pycache__/modeling_swiftformer.cpython-312.pyc,,
transformers/models/swiftformer/__pycache__/modeling_tf_swiftformer.cpython-312.pyc,,
transformers/models/swiftformer/configuration_swiftformer.py,sha256=0_PbPpI5DyuAukHnqq3vy-urxe4dS_qATNhC_MDp-fM,5858
transformers/models/swiftformer/modeling_swiftformer.py,sha256=m5XuKIrQCjUbYbDmeok-FqyQMICyJwN5xDh2uLenTkY,22846
transformers/models/swiftformer/modeling_tf_swiftformer.py,sha256=eFDCLFNvEQ3PEKxcVqQuaJ3-U1P5tukZCw7J_IvCb1g,34966
transformers/models/swin/__init__.py,sha256=7pcdahUG9WcEkEDRoUcMVxdonKglhOpXaQLo8xI6KTg,1025
transformers/models/swin/__pycache__/__init__.cpython-312.pyc,,
transformers/models/swin/__pycache__/configuration_swin.cpython-312.pyc,,
transformers/models/swin/__pycache__/modeling_swin.cpython-312.pyc,,
transformers/models/swin/__pycache__/modeling_tf_swin.cpython-312.pyc,,
transformers/models/swin/configuration_swin.py,sha256=8xaMjjRiwRngEhBf5yklrd67PekE6RVsz3a1j4EzQp0,7949
transformers/models/swin/modeling_swin.py,sha256=QukH6wd6jEz5CorSsUEN7uSuqzgR-FJlZcVjLA774n0,63190
transformers/models/swin/modeling_tf_swin.py,sha256=O4NGM4AotiGgoDZ_ww0rkQg19eTKe5gUlwvJHBa0buY,70907
transformers/models/swin2sr/__init__.py,sha256=xEgE9PSRZ7w4fMZeHQ42QyfS1xua7kNne-K7ADvGRn0,1039
transformers/models/swin2sr/__pycache__/__init__.cpython-312.pyc,,
transformers/models/swin2sr/__pycache__/configuration_swin2sr.cpython-312.pyc,,
transformers/models/swin2sr/__pycache__/image_processing_swin2sr.cpython-312.pyc,,
transformers/models/swin2sr/__pycache__/modeling_swin2sr.cpython-312.pyc,,
transformers/models/swin2sr/configuration_swin2sr.py,sha256=6ZRVIyo6z1oQvPm13QvkrWcKpf1qjMf0QqwmdHMdvto,6841
transformers/models/swin2sr/image_processing_swin2sr.py,sha256=T5JpOohG19DOgjlUHgtw06vOv1Q5FHg-oK6ImXPL2zQ,9247
transformers/models/swin2sr/modeling_swin2sr.py,sha256=fV0zATWkz7_qdAS_Ur5p0pGFY3PwZu3ZvRTJAgK2U7I,50897
transformers/models/swinv2/__init__.py,sha256=njM902tlEQ82mYRN9ZTMOiXpJn1NHnxKbm_LCvn2I-M,993
transformers/models/swinv2/__pycache__/__init__.cpython-312.pyc,,
transformers/models/swinv2/__pycache__/configuration_swinv2.cpython-312.pyc,,
transformers/models/swinv2/__pycache__/modeling_swinv2.cpython-312.pyc,,
transformers/models/swinv2/configuration_swinv2.py,sha256=2ELV-6mQG8Jbmkkd-E5YVKXlr08d6RL-3HZUmNwW4D8,7547
transformers/models/swinv2/modeling_swinv2.py,sha256=RJvpsxmMm98DgkSclLKN80deIjyPYdBI9ni_XN5WPh0,67006
transformers/models/switch_transformers/__init__.py,sha256=Iw38A9kfIT5mJ0G00YE-TVN-M_b1DBHYQqb0pEyTZMY,1019
transformers/models/switch_transformers/__pycache__/__init__.cpython-312.pyc,,
transformers/models/switch_transformers/__pycache__/configuration_switch_transformers.cpython-312.pyc,,
transformers/models/switch_transformers/__pycache__/modeling_switch_transformers.cpython-312.pyc,,
transformers/models/switch_transformers/configuration_switch_transformers.py,sha256=oIUAs_6pAQ1ExMl2uFAYiUMA-l8GJvqpChiPkIFYYq4,9046
transformers/models/switch_transformers/modeling_switch_transformers.py,sha256=jtzr1AUrhlf3Y4m171_mRhoQ7oHb7dd7i8wz7VAavaw,94507
transformers/models/t5/__init__.py,sha256=hCQO8nkKAJqFgMOwC7nxhyDYOUA9fcDT0pDb7DAHt5Y,1130
transformers/models/t5/__pycache__/__init__.cpython-312.pyc,,
transformers/models/t5/__pycache__/configuration_t5.cpython-312.pyc,,
transformers/models/t5/__pycache__/modeling_flax_t5.cpython-312.pyc,,
transformers/models/t5/__pycache__/modeling_t5.cpython-312.pyc,,
transformers/models/t5/__pycache__/modeling_tf_t5.cpython-312.pyc,,
transformers/models/t5/__pycache__/tokenization_t5.cpython-312.pyc,,
transformers/models/t5/__pycache__/tokenization_t5_fast.cpython-312.pyc,,
transformers/models/t5/configuration_t5.py,sha256=DfwNeewoBtzp1VsB84uLW9k0N_nwPk7L66SVEDVLQwA,7372
transformers/models/t5/modeling_flax_t5.py,sha256=37D-nHUepd3Fo93JT4rXLTXCZbXGYRlRcTYiXq2Deog,74273
transformers/models/t5/modeling_t5.py,sha256=IOoxpJ3DhwLZtQZRuq_Ya0B6dnTcykBCJReuS4Jo4bQ,115405
transformers/models/t5/modeling_tf_t5.py,sha256=VZzl32hiqK1L9pZBw9OJzqpHGotBY5t9IpKWT3a_BNI,77180
transformers/models/t5/tokenization_t5.py,sha256=1g994QkBag1ht5dKWdKjbB9wfLusIWGTFd2Jmkp_Rz8,20019
transformers/models/t5/tokenization_t5_fast.py,sha256=wNerBtP7B5cB3o6UhVQB2b1W4NjNXwkvMFHLY3H8m9I,10200
transformers/models/table_transformer/__init__.py,sha256=VT-KM0_6LZ6fdOAglbfA8zEhCQuYa6He10Div7WEcD8,1015
transformers/models/table_transformer/__pycache__/__init__.cpython-312.pyc,,
transformers/models/table_transformer/__pycache__/configuration_table_transformer.cpython-312.pyc,,
transformers/models/table_transformer/__pycache__/modeling_table_transformer.cpython-312.pyc,,
transformers/models/table_transformer/configuration_table_transformer.py,sha256=eqmUeH6hf0B8WHidmq1Ca8DG2CQf4TCZol5u12ooAys,13373
transformers/models/table_transformer/modeling_table_transformer.py,sha256=LzvOJure8I8YPA3TuI5QHB4-sJcEF2kpYl8Xy6wM6J8,70029
transformers/models/tapas/__init__.py,sha256=DQTmog2nYukVsXxARy8v35SitI0Iv4ZLCGl7zUlLDuI,1066
transformers/models/tapas/__pycache__/__init__.cpython-312.pyc,,
transformers/models/tapas/__pycache__/configuration_tapas.cpython-312.pyc,,
transformers/models/tapas/__pycache__/modeling_tapas.cpython-312.pyc,,
transformers/models/tapas/__pycache__/modeling_tf_tapas.cpython-312.pyc,,
transformers/models/tapas/__pycache__/tokenization_tapas.cpython-312.pyc,,
transformers/models/tapas/configuration_tapas.py,sha256=ICAyq4RBVAgph30XEKBixFCQolEGQQ_wZk9o0DXl2pk,12293
transformers/models/tapas/modeling_tapas.py,sha256=dI6cKtkUOwOQDom1DiPovgUUzbdXNfs_LnHgakSfWd8,110377
transformers/models/tapas/modeling_tf_tapas.py,sha256=WNYBNH4uyA1bERtL5ObV2dWEQKN6lmZdbLE-_kyzhIw,112395
transformers/models/tapas/tokenization_tapas.py,sha256=OPyboBARXxk-QPTK5fY0jb16_uiFp0EuGZI0kXNWejk,118424
transformers/models/textnet/__init__.py,sha256=WCPdGs5LWKGDk5UvZm4wA0G76bIXMOhBr1M3x-WmE3s,1039
transformers/models/textnet/__pycache__/__init__.cpython-312.pyc,,
transformers/models/textnet/__pycache__/configuration_textnet.cpython-312.pyc,,
transformers/models/textnet/__pycache__/image_processing_textnet.cpython-312.pyc,,
transformers/models/textnet/__pycache__/modeling_textnet.cpython-312.pyc,,
transformers/models/textnet/configuration_textnet.py,sha256=kW_lRsSSythpV0dltKhkG74LJh_zPCSvTmXsMT-y47g,6212
transformers/models/textnet/image_processing_textnet.py,sha256=ZVkHCdJGeWtFz_j1uLkzBUypBbHW5v_pUgXUV5CfBjc,17613
transformers/models/textnet/modeling_textnet.py,sha256=snpUjqS6AcHhd0cywTYaSdgFSABOzZe8A9IpAbFGrfc,19010
transformers/models/time_series_transformer/__init__.py,sha256=3A_3Wog-6NDwCoBIMtkzJv9slc_wXpzDzsOo-xBQ8hE,1027
transformers/models/time_series_transformer/__pycache__/__init__.cpython-312.pyc,,
transformers/models/time_series_transformer/__pycache__/configuration_time_series_transformer.cpython-312.pyc,,
transformers/models/time_series_transformer/__pycache__/modeling_time_series_transformer.cpython-312.pyc,,
transformers/models/time_series_transformer/configuration_time_series_transformer.py,sha256=okwA_lr2uUmiXn6ETjLAn36lZ0tb22Yc8YOE9UMd_4M,11701
transformers/models/time_series_transformer/modeling_time_series_transformer.py,sha256=lhfG_jX0PxkjN8hol6UwyyhcPy2JfdcHAeQdKg8J0aU,88674
transformers/models/timesformer/__init__.py,sha256=4ODuyNRrYkbgpSbMHJX8XmpJdekHlu__zWey-plUSgI,1003
transformers/models/timesformer/__pycache__/__init__.cpython-312.pyc,,
transformers/models/timesformer/__pycache__/configuration_timesformer.cpython-312.pyc,,
transformers/models/timesformer/__pycache__/modeling_timesformer.cpython-312.pyc,,
transformers/models/timesformer/configuration_timesformer.py,sha256=GilCKil_40B_hqjh0-02CWrBupbwEfHhOZ3b5bUpTPI,5568
transformers/models/timesformer/modeling_timesformer.py,sha256=aofWD4rJQcLqXHOFGb6qLd0q7AOy8x9GWTJiCut_qqw,35293
transformers/models/timm_backbone/__init__.py,sha256=s0GlTaJ43Yt9ZdzG9-qjJNlp0Ol4vjN-14S6N7gXLsA,1007
transformers/models/timm_backbone/__pycache__/__init__.cpython-312.pyc,,
transformers/models/timm_backbone/__pycache__/configuration_timm_backbone.cpython-312.pyc,,
transformers/models/timm_backbone/__pycache__/modeling_timm_backbone.cpython-312.pyc,,
transformers/models/timm_backbone/configuration_timm_backbone.py,sha256=2TXijKvoZeRZzvZxGEsoVVTb0kNcMuoYoVfYQKYhZco,3186
transformers/models/timm_backbone/modeling_timm_backbone.py,sha256=xyVXDBWWzRl0wPSX4mv7vOCsGlLH5L7LlGRIpjBwuuI,6649
transformers/models/timm_wrapper/__init__.py,sha256=nO3xlv8KQmYCoxKqDteADLkli16cLqdLkfTY_G73O6k,1048
transformers/models/timm_wrapper/__pycache__/__init__.cpython-312.pyc,,
transformers/models/timm_wrapper/__pycache__/configuration_timm_wrapper.cpython-312.pyc,,
transformers/models/timm_wrapper/__pycache__/image_processing_timm_wrapper.cpython-312.pyc,,
transformers/models/timm_wrapper/__pycache__/modeling_timm_wrapper.cpython-312.pyc,,
transformers/models/timm_wrapper/configuration_timm_wrapper.py,sha256=-QGWNA57rY8f841qkCja8VgtVZupQAP80kLyG_a036k,4839
transformers/models/timm_wrapper/image_processing_timm_wrapper.py,sha256=-jHV_6GOwmcgCmzyhn4R9LKpTTQQCsL5k-puh3LFoh4,5288
transformers/models/timm_wrapper/modeling_timm_wrapper.py,sha256=r-vbJ12Qomy2iZOeJhWBhpWpOZhASZZ30U3y8H0mC4o,15880
transformers/models/trocr/__init__.py,sha256=Hllbq_42XbGRZyXsGOzYHcb33MOA5_yfijMRKEXJ4n4,1027
transformers/models/trocr/__pycache__/__init__.cpython-312.pyc,,
transformers/models/trocr/__pycache__/configuration_trocr.cpython-312.pyc,,
transformers/models/trocr/__pycache__/modeling_trocr.cpython-312.pyc,,
transformers/models/trocr/__pycache__/processing_trocr.cpython-312.pyc,,
transformers/models/trocr/configuration_trocr.py,sha256=7W4gKEwd5ZdCf3ovSjO9OJbY-VG-ebwl04WkSEdCUgI,6550
transformers/models/trocr/modeling_trocr.py,sha256=MRlODhiywnVx9_lobqSBI3qWxMh-TrgdMCbyrCtMPts,44940
transformers/models/trocr/processing_trocr.py,sha256=_am3vHsTzD0DrmQbwgzb3CtRWMqT8p0IxatDNAGK-nA,6354
transformers/models/tvp/__init__.py,sha256=CMKadZ9nKrh8p6u4Z-k6014a9LqDJY7KpyL009s3kpo,1061
transformers/models/tvp/__pycache__/__init__.cpython-312.pyc,,
transformers/models/tvp/__pycache__/configuration_tvp.cpython-312.pyc,,
transformers/models/tvp/__pycache__/image_processing_tvp.cpython-312.pyc,,
transformers/models/tvp/__pycache__/modeling_tvp.cpython-312.pyc,,
transformers/models/tvp/__pycache__/processing_tvp.cpython-312.pyc,,
transformers/models/tvp/configuration_tvp.py,sha256=DLhpoGcH2Sj9I-0etRbAlWFXhSedn3IiKt4NSQYgnN4,9932
transformers/models/tvp/image_processing_tvp.py,sha256=YtrQRYqJZO84ht-izgd2SFD3565jC6v6gvZMqftXxQU,22582
transformers/models/tvp/modeling_tvp.py,sha256=6XgJHOEWEoxLpx0rm1M1Cd0ZjfqNBRtQdFOWEhmR_FI,43675
transformers/models/tvp/processing_tvp.py,sha256=COYfa1VIKEH4Yo20wQ9tzpNcKvH4-tjngdAzWNpRoTA,7009
transformers/models/udop/__init__.py,sha256=CqFpHruzC__VtxEcVz31QxxMpBI1mjO77-Lj0RqW4Eo,1103
transformers/models/udop/__pycache__/__init__.cpython-312.pyc,,
transformers/models/udop/__pycache__/configuration_udop.cpython-312.pyc,,
transformers/models/udop/__pycache__/modeling_udop.cpython-312.pyc,,
transformers/models/udop/__pycache__/processing_udop.cpython-312.pyc,,
transformers/models/udop/__pycache__/tokenization_udop.cpython-312.pyc,,
transformers/models/udop/__pycache__/tokenization_udop_fast.cpython-312.pyc,,
transformers/models/udop/configuration_udop.py,sha256=T0ZtIom_dWCvj9_lYwZ-stuGkQC3XUR5w8tfF5t0hwU,7675
transformers/models/udop/modeling_udop.py,sha256=nUmXAjTmmTZ5fnnL9mLYCD3XcdZ2v5IcfkMh_Aex0EA,101003
transformers/models/udop/processing_udop.py,sha256=CoT7oSAEPKegNPiA95lp9u46vKDpqNvQU9q7UIVF3WE,10034
transformers/models/udop/tokenization_udop.py,sha256=MP2OjSQ406KmtBPsCnaqCp2VGs4inJq5jJrr-FDNoBs,71722
transformers/models/udop/tokenization_udop_fast.py,sha256=q4nmSOPUZDvyxqZfcLyP7lgp-GX8x4Hc-3Cx2ctHp50,49794
transformers/models/umt5/__init__.py,sha256=FKt6Ap3AvOCIKoeOM-5qY84lNEML9IujaDaYROINJMs,989
transformers/models/umt5/__pycache__/__init__.cpython-312.pyc,,
transformers/models/umt5/__pycache__/configuration_umt5.cpython-312.pyc,,
transformers/models/umt5/__pycache__/modeling_umt5.cpython-312.pyc,,
transformers/models/umt5/configuration_umt5.py,sha256=n5AuXtgmuknmr7_wbWdmzfA6-9t355hFj1jD_B7XsMA,7740
transformers/models/umt5/modeling_umt5.py,sha256=NtlECTR6xVbDgN0fOZmIpp31mBYmWHeTYt04sWieSIQ,94766
transformers/models/unispeech/__init__.py,sha256=AXJMExDoYYI71OKNXhAt7lyqcFIvcLHEQ1Fsm171m5w,999
transformers/models/unispeech/__pycache__/__init__.cpython-312.pyc,,
transformers/models/unispeech/__pycache__/configuration_unispeech.cpython-312.pyc,,
transformers/models/unispeech/__pycache__/modeling_unispeech.cpython-312.pyc,,
transformers/models/unispeech/configuration_unispeech.py,sha256=7Z8nZdyHVm7VrFNdngRLdvvJ9xLm8TRwlspGT2CpoXo,17486
transformers/models/unispeech/modeling_unispeech.py,sha256=XLUqkSmVJNV7Jt3BTkuf48nHnEkGk1Fgyj0cvKBzcFw,86866
transformers/models/unispeech_sat/__init__.py,sha256=P9lCzMg01s4Gj_Pb8t1l36MRAeoOcxUa4d7dbQSe9N4,1007
transformers/models/unispeech_sat/__pycache__/__init__.cpython-312.pyc,,
transformers/models/unispeech_sat/__pycache__/configuration_unispeech_sat.cpython-312.pyc,,
transformers/models/unispeech_sat/__pycache__/modeling_unispeech_sat.cpython-312.pyc,,
transformers/models/unispeech_sat/configuration_unispeech_sat.py,sha256=jyLGIqtOHBLZQ-lw8XSuG6ZrOOvvU-4aifWxulNoPSw,18831
transformers/models/unispeech_sat/modeling_unispeech_sat.py,sha256=C0iUmdP0gp477d1cBBoVnKWUaoEo7-JQtLz2IyPgrXA,101126
transformers/models/univnet/__init__.py,sha256=hfHyxyKGEfd58p1fUSA3IxK2q6JkVatkGceVaoKuODk,1041
transformers/models/univnet/__pycache__/__init__.cpython-312.pyc,,
transformers/models/univnet/__pycache__/configuration_univnet.cpython-312.pyc,,
transformers/models/univnet/__pycache__/feature_extraction_univnet.cpython-312.pyc,,
transformers/models/univnet/__pycache__/modeling_univnet.cpython-312.pyc,,
transformers/models/univnet/configuration_univnet.py,sha256=le3W3tTVtbIpdHEuAGSiQw67ZlKcmeFh7jdu4ESEoiI,6758
transformers/models/univnet/feature_extraction_univnet.py,sha256=eKmWqN-2p0DQ-HSaWDp6T0QpLicH2BQERSUjFVJj-HY,22861
transformers/models/univnet/modeling_univnet.py,sha256=IFAldSQ4Ubq_qxXkOuxfYVg91EDDNi8KRuRLtaMMxiA,27572
transformers/models/upernet/__init__.py,sha256=Wq3u7yXJul5PLmjalxKgx451sa_WuSXbEM45bZsRv3E,995
transformers/models/upernet/__pycache__/__init__.cpython-312.pyc,,
transformers/models/upernet/__pycache__/configuration_upernet.cpython-312.pyc,,
transformers/models/upernet/__pycache__/modeling_upernet.cpython-312.pyc,,
transformers/models/upernet/configuration_upernet.py,sha256=cVM6QHwf0DcfGV9tA4ejaQs6GHZZaSoHCC3ssZHiOpM,6643
transformers/models/upernet/modeling_upernet.py,sha256=_91OAqgT054ITpI9sP9_ZvqL3Q0UMqyxOd5xeTNG0Xg,17208
transformers/models/video_llava/__init__.py,sha256=bsLGp1WBBO_AvNVRxzOn5k7OYQIbX9SqFhESd24FImc,1093
transformers/models/video_llava/__pycache__/__init__.cpython-312.pyc,,
transformers/models/video_llava/__pycache__/configuration_video_llava.cpython-312.pyc,,
transformers/models/video_llava/__pycache__/image_processing_video_llava.cpython-312.pyc,,
transformers/models/video_llava/__pycache__/modeling_video_llava.cpython-312.pyc,,
transformers/models/video_llava/__pycache__/processing_video_llava.cpython-312.pyc,,
transformers/models/video_llava/configuration_video_llava.py,sha256=fBEjIa1SYy6jaoBD6pgFqOlWht1jYGAdOBU5JqWAm-E,6326
transformers/models/video_llava/image_processing_video_llava.py,sha256=6IXnWKubazLraKvpqdW36Zn3PGmGN8_DNkuO3_5UErQ,18690
transformers/models/video_llava/modeling_video_llava.py,sha256=mndi23BjZI400iCX0txU1z9vxxz3EEE9VLiFQhHSQ2o,31732
transformers/models/video_llava/processing_video_llava.py,sha256=s-Lks9ZI01X14PWlKcsWfTIm7K0ynTLF5yhe-Y8Qdrk,11818
transformers/models/videomae/__init__.py,sha256=IYw3qXj1-PDmBAp---YaZyqdBsIjdMZQI37xT_-9SgY,1089
transformers/models/videomae/__pycache__/__init__.cpython-312.pyc,,
transformers/models/videomae/__pycache__/configuration_videomae.cpython-312.pyc,,
transformers/models/videomae/__pycache__/feature_extraction_videomae.cpython-312.pyc,,
transformers/models/videomae/__pycache__/image_processing_videomae.cpython-312.pyc,,
transformers/models/videomae/__pycache__/modeling_videomae.cpython-312.pyc,,
transformers/models/videomae/configuration_videomae.py,sha256=O0BwqYZnc9Q5Kpemmel6rOxeDBSj7KKCxgpHfMVCVGE,6600
transformers/models/videomae/feature_extraction_videomae.py,sha256=ipQNgym9IqJFNUgI0tDxLmhXQTsXGnmUXHEEQnAG1BI,1241
transformers/models/videomae/image_processing_videomae.py,sha256=1bwAavDxV0gpVZf9U9xt7a7j9ZHljpBqTxmuaB2JJMw,16547
transformers/models/videomae/modeling_videomae.py,sha256=58VIORwAmCubzJUSrSc9e55hqD_5s7RTz5lMP82V_4s,49409
transformers/models/vilt/__init__.py,sha256=xi3VCXhgvk-7vjOrInBT3x_tucu7dkQ1oaJPdAQGCME,1108
transformers/models/vilt/__pycache__/__init__.cpython-312.pyc,,
transformers/models/vilt/__pycache__/configuration_vilt.cpython-312.pyc,,
transformers/models/vilt/__pycache__/feature_extraction_vilt.cpython-312.pyc,,
transformers/models/vilt/__pycache__/image_processing_vilt.cpython-312.pyc,,
transformers/models/vilt/__pycache__/modeling_vilt.cpython-312.pyc,,
transformers/models/vilt/__pycache__/processing_vilt.cpython-312.pyc,,
transformers/models/vilt/configuration_vilt.py,sha256=_ClQWl9Srq_ozXsB-gSdsy5gaTbhwNrP5bMcp6tIz4A,6815
transformers/models/vilt/feature_extraction_vilt.py,sha256=2dLqlDY3SUnKf-MypHrgpH5scJLJj_AtMWn-d3DrhUQ,1209
transformers/models/vilt/image_processing_vilt.py,sha256=1tgD7TQ7PyWFHidZEpMNuPw4OSceWYAQT71n7TEgTKU,23191
transformers/models/vilt/modeling_vilt.py,sha256=4owEuEq6X7oBNIUGnyvyqcWzEoj6Oi_9wGfECi-tXjA,65132
transformers/models/vilt/processing_vilt.py,sha256=0S5qkrduxIpeQHJsJv5G5T7YG4tB-mLZRl6tTVsxNeY,6109
transformers/models/vipllava/__init__.py,sha256=HJ5mZUNdt_bmaC9l-GycD7mVT2r1oN15prmnlBtz6oA,997
transformers/models/vipllava/__pycache__/__init__.cpython-312.pyc,,
transformers/models/vipllava/__pycache__/configuration_vipllava.cpython-312.pyc,,
transformers/models/vipllava/__pycache__/modeling_vipllava.cpython-312.pyc,,
transformers/models/vipllava/configuration_vipllava.py,sha256=emiqeog-to-INp_5yVCikUNtPspa8hiak5nQ8fZ4xwA,5070
transformers/models/vipllava/modeling_vipllava.py,sha256=Jq2yBhPAoXwZTZL6zVUbhe57TcJk5CbOLOH1AKNaU1k,24173
transformers/models/vision_encoder_decoder/__init__.py,sha256=xK5xKVeIOZSN1d9Y2nDa3NYkLdGidbwgQ6Es8JhzKzA,1135
transformers/models/vision_encoder_decoder/__pycache__/__init__.cpython-312.pyc,,
transformers/models/vision_encoder_decoder/__pycache__/configuration_vision_encoder_decoder.cpython-312.pyc,,
transformers/models/vision_encoder_decoder/__pycache__/modeling_flax_vision_encoder_decoder.cpython-312.pyc,,
transformers/models/vision_encoder_decoder/__pycache__/modeling_tf_vision_encoder_decoder.cpython-312.pyc,,
transformers/models/vision_encoder_decoder/__pycache__/modeling_vision_encoder_decoder.cpython-312.pyc,,
transformers/models/vision_encoder_decoder/configuration_vision_encoder_decoder.py,sha256=No1MOjy6fx-oA0Kngdn3rNeusBUzN-K0Ee2hrjmgpq4,8415
transformers/models/vision_encoder_decoder/modeling_flax_vision_encoder_decoder.py,sha256=tZ8n9UFZ68aq4h99apWiuCaQoVAsSOvY_YwHmLYBppw,41579
transformers/models/vision_encoder_decoder/modeling_tf_vision_encoder_decoder.py,sha256=E0PLKardMAzd7oQLDXXBIIp0FjlUPUBIEkjMoAyxzAA,36281
transformers/models/vision_encoder_decoder/modeling_vision_encoder_decoder.py,sha256=PFSb3CdMAaeRWMLcouZYSKMvoQJ0JeNVk77oz_7j0K8,34477
transformers/models/vision_text_dual_encoder/__init__.py,sha256=LRXs5oXk4_8AaHuIVaj1IgBO4X1vwP-ehQC1T1xEiAI,1198
transformers/models/vision_text_dual_encoder/__pycache__/__init__.cpython-312.pyc,,
transformers/models/vision_text_dual_encoder/__pycache__/configuration_vision_text_dual_encoder.cpython-312.pyc,,
transformers/models/vision_text_dual_encoder/__pycache__/modeling_flax_vision_text_dual_encoder.cpython-312.pyc,,
transformers/models/vision_text_dual_encoder/__pycache__/modeling_tf_vision_text_dual_encoder.cpython-312.pyc,,
transformers/models/vision_text_dual_encoder/__pycache__/modeling_vision_text_dual_encoder.cpython-312.pyc,,
transformers/models/vision_text_dual_encoder/__pycache__/processing_vision_text_dual_encoder.cpython-312.pyc,,
transformers/models/vision_text_dual_encoder/configuration_vision_text_dual_encoder.py,sha256=QVBDhO2nISzfOpwAE5oRoW973Ff0C29u9QSQufpH2vY,5014
transformers/models/vision_text_dual_encoder/modeling_flax_vision_text_dual_encoder.py,sha256=I9IkA7fnzzuW9vyPigPXhbpSI_6l9T1JBh1IA5dSVLo,26359
transformers/models/vision_text_dual_encoder/modeling_tf_vision_text_dual_encoder.py,sha256=tVIG_VSYiPtCkkHabB_ZZpNLUWcyWrPRnujeSUe9hXc,28685
transformers/models/vision_text_dual_encoder/modeling_vision_text_dual_encoder.py,sha256=hH4cQG6_7GJMhWXLOYh_rVNg47d2cvM0Y_F-PdgsOhg,25242
transformers/models/vision_text_dual_encoder/processing_vision_text_dual_encoder.py,sha256=7KMnZ_wyP62ORmCjUJdYIQKQmtI0-aGduzY_Jg8Ocjc,6976
transformers/models/visual_bert/__init__.py,sha256=zZFHfkE7OUMZUwYvB7v4ZIBXVUW9Mboqoa1QdTQURWM,1003
transformers/models/visual_bert/__pycache__/__init__.cpython-312.pyc,,
transformers/models/visual_bert/__pycache__/configuration_visual_bert.cpython-312.pyc,,
transformers/models/visual_bert/__pycache__/modeling_visual_bert.cpython-312.pyc,,
transformers/models/visual_bert/configuration_visual_bert.py,sha256=4U17YnlSjbOpzsAPdGH_EfvBjv7jppbHWlmLBrchGM4,6767
transformers/models/visual_bert/modeling_visual_bert.py,sha256=eGGQL3438HYVy5svXZGw09QyE1ElEYThswMh8mlwm_k,69195
transformers/models/vit/__init__.py,sha256=uTQRjeWgJLHyXfc7yVOEyv7wnr42Jhy-8p9k5UUbxAM,1186
transformers/models/vit/__pycache__/__init__.cpython-312.pyc,,
transformers/models/vit/__pycache__/configuration_vit.cpython-312.pyc,,
transformers/models/vit/__pycache__/feature_extraction_vit.cpython-312.pyc,,
transformers/models/vit/__pycache__/image_processing_vit.cpython-312.pyc,,
transformers/models/vit/__pycache__/image_processing_vit_fast.cpython-312.pyc,,
transformers/models/vit/__pycache__/modeling_flax_vit.cpython-312.pyc,,
transformers/models/vit/__pycache__/modeling_tf_vit.cpython-312.pyc,,
transformers/models/vit/__pycache__/modeling_vit.cpython-312.pyc,,
transformers/models/vit/configuration_vit.py,sha256=OjdcPt4dfH1Jxqli5OxqAVg2qu-hpTTZWfvOYQwdMRc,5655
transformers/models/vit/feature_extraction_vit.py,sha256=sHO5n66s6PACZUTDxNTNS4p4zQmqLQxt7KByxQLF1ks,1201
transformers/models/vit/image_processing_vit.py,sha256=O5h-venZuX2zCZpw99dGWgYZhdwhwCrL3tjl0l7NJw0,14356
transformers/models/vit/image_processing_vit_fast.py,sha256=UNVHoIRSTrM0muFwcCzM02T1YDG8BMlzMF0q452xooA,1380
transformers/models/vit/modeling_flax_vit.py,sha256=qJHCa7wmFrYscxoMp2UkBy0d5mprUtLjXZ6kqwrzh_o,25428
transformers/models/vit/modeling_tf_vit.py,sha256=lUnqxfGpXgvXydp99uWwY8qeyXMqeR7RCWIrgGalzPQ,37408
transformers/models/vit/modeling_vit.py,sha256=gsCAsbmlRo8e1KwaI1WNqLlAC9ujwhalSmO643cE2Pc,38082
transformers/models/vit_mae/__init__.py,sha256=C8NcxWwzXlNMeMOA9DNHfDYvRF9biIuUduuwhoaTTD8,1034
transformers/models/vit_mae/__pycache__/__init__.cpython-312.pyc,,
transformers/models/vit_mae/__pycache__/configuration_vit_mae.cpython-312.pyc,,
transformers/models/vit_mae/__pycache__/modeling_tf_vit_mae.cpython-312.pyc,,
transformers/models/vit_mae/__pycache__/modeling_vit_mae.cpython-312.pyc,,
transformers/models/vit_mae/configuration_vit_mae.py,sha256=3nnWDAbp6WLfOHLO3taJUNEuGRlk3oAa0qaLEEJgjHQ,6372
transformers/models/vit_mae/modeling_tf_vit_mae.py,sha256=aUDcL9w1V-k4rCfJa-CM7Dd6j-GV9Zes2JezW_-F2do,58028
transformers/models/vit_mae/modeling_vit_mae.py,sha256=KSPDdAbqEDYIKZJSTNvtuFM-Wjoa8he1rVMFfKadC1c,51286
transformers/models/vit_msn/__init__.py,sha256=Y1g56VRSNr-PxS-g4Cp2IlRR5M9CiaFGlhAQXwszGHo,995
transformers/models/vit_msn/__pycache__/__init__.cpython-312.pyc,,
transformers/models/vit_msn/__pycache__/configuration_vit_msn.cpython-312.pyc,,
transformers/models/vit_msn/__pycache__/modeling_vit_msn.cpython-312.pyc,,
transformers/models/vit_msn/configuration_vit_msn.py,sha256=HeU0UloranISU9zLiPsK0CyooMacqogTNmwE4xp2N-o,4864
transformers/models/vit_msn/modeling_vit_msn.py,sha256=7gdrXGWcp6D3ouGe2XoXpYcb7a9lm9fAidzEsS7XYlE,32419
transformers/models/vitdet/__init__.py,sha256=13LNGZwvKK3tBrQWVs43rQbxbgqvxLfnM0uMqomHqhM,993
transformers/models/vitdet/__pycache__/__init__.cpython-312.pyc,,
transformers/models/vitdet/__pycache__/configuration_vitdet.cpython-312.pyc,,
transformers/models/vitdet/__pycache__/modeling_vitdet.cpython-312.pyc,,
transformers/models/vitdet/configuration_vitdet.py,sha256=5p8B04eSluvXlpdMxyU6cRniCnMLTfYVyIqJW1iOwXc,7541
transformers/models/vitdet/modeling_vitdet.py,sha256=EKwG9JwcT6LjWwoGd8CP3btbdKin7FxziEvnZKvLc4E,34895
transformers/models/vitmatte/__init__.py,sha256=UGc3nIiSmHnzVsRoJ0BYxbZPXPRj1bB2pbzaMg1H6hM,1042
transformers/models/vitmatte/__pycache__/__init__.cpython-312.pyc,,
transformers/models/vitmatte/__pycache__/configuration_vitmatte.cpython-312.pyc,,
transformers/models/vitmatte/__pycache__/image_processing_vitmatte.cpython-312.pyc,,
transformers/models/vitmatte/__pycache__/modeling_vitmatte.cpython-312.pyc,,
transformers/models/vitmatte/configuration_vitmatte.py,sha256=RGKk5EN1Z_v9MaBHxcHOdTKVfPYmIrI_Bq1eSVE_9mI,6269
transformers/models/vitmatte/image_processing_vitmatte.py,sha256=cBkeqdnsvXG6c850RUBkYQNuvmRzTHKET2hZdECK1ig,13473
transformers/models/vitmatte/modeling_vitmatte.py,sha256=EB7xOVC4jmJ3MqrxSWpikq3FV6nWfDnOMexOvlbwEm4,13053
transformers/models/vitpose/__init__.py,sha256=VA7aRcVMgFJH46i6HurkXJS0Z38BotU3H3o3e2wgyXU,1039
transformers/models/vitpose/__pycache__/__init__.cpython-312.pyc,,
transformers/models/vitpose/__pycache__/configuration_vitpose.cpython-312.pyc,,
transformers/models/vitpose/__pycache__/image_processing_vitpose.cpython-312.pyc,,
transformers/models/vitpose/__pycache__/modeling_vitpose.cpython-312.pyc,,
transformers/models/vitpose/configuration_vitpose.py,sha256=pHi8AHS_s6_lyy6OfORNASj_s_fITxu2ABam-OZ3ad4,5741
transformers/models/vitpose/image_processing_vitpose.py,sha256=r2eevEQkWAuY9owSNSwDbY_gbzTPaKyrlhzqZ0-bMXU,29538
transformers/models/vitpose/modeling_vitpose.py,sha256=-yEaO0kTqa4qy4pUBlAZ9avgtro46psDWXd1fEGG_48,14702
transformers/models/vitpose_backbone/__init__.py,sha256=AJzKeeuuhEYwBUijmeuCSaio_RysaVoWuLQbQDQvHsw,1773
transformers/models/vitpose_backbone/__pycache__/__init__.cpython-312.pyc,,
transformers/models/vitpose_backbone/__pycache__/configuration_vitpose_backbone.cpython-312.pyc,,
transformers/models/vitpose_backbone/__pycache__/modeling_vitpose_backbone.cpython-312.pyc,,
transformers/models/vitpose_backbone/configuration_vitpose_backbone.py,sha256=44eEhv4XBnS9zsUU7JwFlGv97x32j9iUr_jYp6vnAsY,6613
transformers/models/vitpose_backbone/modeling_vitpose_backbone.py,sha256=ATKqXs_AkXyt2K1ydQRIXt2GB6HU0PSn-RgTuJkVoQo,22744
transformers/models/vits/__init__.py,sha256=7baZcqGvFlYQxAl721XtMptMZKkzvBOa2ttyOhqhUtk,1026
transformers/models/vits/__pycache__/__init__.cpython-312.pyc,,
transformers/models/vits/__pycache__/configuration_vits.cpython-312.pyc,,
transformers/models/vits/__pycache__/modeling_vits.cpython-312.pyc,,
transformers/models/vits/__pycache__/tokenization_vits.cpython-312.pyc,,
transformers/models/vits/configuration_vits.py,sha256=mrh8QVlkYg0pj52N8fTivMjNxxyOSMnzBil9gGIt1rI,13884
transformers/models/vits/modeling_vits.py,sha256=JV2PZ9Lv6g08WTiIMt_g7yEHkPnueZ2qkquqD4fF9QI,66639
transformers/models/vits/tokenization_vits.py,sha256=UWa1DWO5hMEViHZ4DeUfoiLUaACzkkTKLK7cwA2lzb0,9388
transformers/models/vivit/__init__.py,sha256=LT2FipIBdB69s9UY4viyuB5q2e0v3bCwtQMiOEOj2xg,1033
transformers/models/vivit/__pycache__/__init__.cpython-312.pyc,,
transformers/models/vivit/__pycache__/configuration_vivit.cpython-312.pyc,,
transformers/models/vivit/__pycache__/image_processing_vivit.cpython-312.pyc,,
transformers/models/vivit/__pycache__/modeling_vivit.cpython-312.pyc,,
transformers/models/vivit/configuration_vivit.py,sha256=9gBflSLQaYIF_hwzti8438fDtfHA8CCnzPdnG1aRf6Q,5142
transformers/models/vivit/image_processing_vivit.py,sha256=P4AP0K4x_hzENg7-6Z270pt-sTWrBNIJbnXnzD4WnZo,19075
transformers/models/vivit/modeling_vivit.py,sha256=KYWG5dqYb9AS3usjalaD0SeQA8HaWBaytozUcqMxxKM,35579
transformers/models/wav2vec2/__init__.py,sha256=5nXyY4dA0h9iNUQZrGAUXtjOnU6KbVq2B1gRzEGEUNI,1206
transformers/models/wav2vec2/__pycache__/__init__.cpython-312.pyc,,
transformers/models/wav2vec2/__pycache__/configuration_wav2vec2.cpython-312.pyc,,
transformers/models/wav2vec2/__pycache__/feature_extraction_wav2vec2.cpython-312.pyc,,
transformers/models/wav2vec2/__pycache__/modeling_flax_wav2vec2.cpython-312.pyc,,
transformers/models/wav2vec2/__pycache__/modeling_tf_wav2vec2.cpython-312.pyc,,
transformers/models/wav2vec2/__pycache__/modeling_wav2vec2.cpython-312.pyc,,
transformers/models/wav2vec2/__pycache__/processing_wav2vec2.cpython-312.pyc,,
transformers/models/wav2vec2/__pycache__/tokenization_wav2vec2.cpython-312.pyc,,
transformers/models/wav2vec2/configuration_wav2vec2.py,sha256=7EFLuyhxCXZ6eYWY9KPu2ABrsHMHS8vNVC-cf4m-tCw,20076
transformers/models/wav2vec2/feature_extraction_wav2vec2.py,sha256=PkgOaVsHGC3Jjoyl1nIekBoQxkR9-hq9N-q7B8JDTy0,11600
transformers/models/wav2vec2/modeling_flax_wav2vec2.py,sha256=9Lb6qXTcGvMcMKXs9gCKZPnRIHJpCJXzpk6VRfcELSo,57447
transformers/models/wav2vec2/modeling_tf_wav2vec2.py,sha256=pJyCyiasTpCWL8nEfztUckvTytMy9VCK2QO9i0ku5lo,78734
transformers/models/wav2vec2/modeling_wav2vec2.py,sha256=AJDolhXGzmbRR0k8ZW13ga5ewKb7YPCcBB7HCEWt66A,120912
transformers/models/wav2vec2/processing_wav2vec2.py,sha256=d6G7AVeK3GyBXs-PEjVASfjHhSaiou4nPm5qsyviNZ0,7738
transformers/models/wav2vec2/tokenization_wav2vec2.py,sha256=BPZBOhr9KVDBG3HeKE8CA_nUv1WULwZCmxfpp065PTA,38799
transformers/models/wav2vec2_bert/__init__.py,sha256=DL010VL3ZV3lAugPH-BOTNSgIedotOEaoy8iHo0sC1Q,1051
transformers/models/wav2vec2_bert/__pycache__/__init__.cpython-312.pyc,,
transformers/models/wav2vec2_bert/__pycache__/configuration_wav2vec2_bert.cpython-312.pyc,,
transformers/models/wav2vec2_bert/__pycache__/modeling_wav2vec2_bert.cpython-312.pyc,,
transformers/models/wav2vec2_bert/__pycache__/processing_wav2vec2_bert.cpython-312.pyc,,
transformers/models/wav2vec2_bert/configuration_wav2vec2_bert.py,sha256=sr2kV9gOzUscvQy1cjMYhnVAlLvDyMK6P-mXxWpGR74,18110
transformers/models/wav2vec2_bert/modeling_wav2vec2_bert.py,sha256=TSXI3jTLEL5qwbmdPDxJCQClMIIzmcOIIrCsxAG5Ajo,74875
transformers/models/wav2vec2_bert/processing_wav2vec2_bert.py,sha256=MZ0demC6dVjmKLjJofkllM8yDv1yQVavFBNOGc4MgiA,7882
transformers/models/wav2vec2_conformer/__init__.py,sha256=JBpapW8VF3yck4Bk29xKyUiQZqB_CXLSYtYxXGXAu2Q,1017
transformers/models/wav2vec2_conformer/__pycache__/__init__.cpython-312.pyc,,
transformers/models/wav2vec2_conformer/__pycache__/configuration_wav2vec2_conformer.cpython-312.pyc,,
transformers/models/wav2vec2_conformer/__pycache__/modeling_wav2vec2_conformer.cpython-312.pyc,,
transformers/models/wav2vec2_conformer/configuration_wav2vec2_conformer.py,sha256=cmbdw40JnLToNSMUttYGvE2r1EesHY5yZzSYJxxY4Wo,20914
transformers/models/wav2vec2_conformer/modeling_wav2vec2_conformer.py,sha256=YO79Lz8Itll7F8BuTu3h_1QbPROpzcf_qHs6TJ9bHVM,96436
transformers/models/wav2vec2_phoneme/__init__.py,sha256=LV4FKcFYNt0GuJvfsUOwTYVFRVfuzUuclKRybFyN9lk,967
transformers/models/wav2vec2_phoneme/__pycache__/__init__.cpython-312.pyc,,
transformers/models/wav2vec2_phoneme/__pycache__/tokenization_wav2vec2_phoneme.cpython-312.pyc,,
transformers/models/wav2vec2_phoneme/tokenization_wav2vec2_phoneme.py,sha256=HbJC1BoaOBTrPcMeRSudrumzKa8mxbFwapKux-FTax8,23205
transformers/models/wav2vec2_with_lm/__init__.py,sha256=yZKHsma85j7AMLB8g8uNXL5D_E5Gc3Vqe-D-V2W15oY,965
transformers/models/wav2vec2_with_lm/__pycache__/__init__.cpython-312.pyc,,
transformers/models/wav2vec2_with_lm/__pycache__/processing_wav2vec2_with_lm.cpython-312.pyc,,
transformers/models/wav2vec2_with_lm/processing_wav2vec2_with_lm.py,sha256=pFA2QDKdr9eir1_BIdeLMXwKd73g8hkScWoI_2oRCPU,30040
transformers/models/wavlm/__init__.py,sha256=wYnYuOpw2e95lauqDbD7u3OC-Pez8yoRsrgExSh_WJQ,991
transformers/models/wavlm/__pycache__/__init__.cpython-312.pyc,,
transformers/models/wavlm/__pycache__/configuration_wavlm.cpython-312.pyc,,
transformers/models/wavlm/__pycache__/modeling_wavlm.cpython-312.pyc,,
transformers/models/wavlm/configuration_wavlm.py,sha256=GKWOh0w-KsVQLy34Cq3ly-anGbBjM6QSiCwdsXdcXeQ,18564
transformers/models/wavlm/modeling_wavlm.py,sha256=Gj6VxBbtmt-IY9oHufO1hlGl-AkMYQO18eMBLQH0Fng,79381
transformers/models/whisper/__init__.py,sha256=qT70wGFDyOsAGuyaHe9if7kn8fxK2shCe6rovr3onw4,1244
transformers/models/whisper/__pycache__/__init__.cpython-312.pyc,,
transformers/models/whisper/__pycache__/configuration_whisper.cpython-312.pyc,,
transformers/models/whisper/__pycache__/english_normalizer.cpython-312.pyc,,
transformers/models/whisper/__pycache__/feature_extraction_whisper.cpython-312.pyc,,
transformers/models/whisper/__pycache__/generation_whisper.cpython-312.pyc,,
transformers/models/whisper/__pycache__/modeling_flax_whisper.cpython-312.pyc,,
transformers/models/whisper/__pycache__/modeling_tf_whisper.cpython-312.pyc,,
transformers/models/whisper/__pycache__/modeling_whisper.cpython-312.pyc,,
transformers/models/whisper/__pycache__/processing_whisper.cpython-312.pyc,,
transformers/models/whisper/__pycache__/tokenization_whisper.cpython-312.pyc,,
transformers/models/whisper/__pycache__/tokenization_whisper_fast.cpython-312.pyc,,
transformers/models/whisper/configuration_whisper.py,sha256=Ljqle73Il6yvso4MfpxHBsPQwbkhEIxPKW6ZtSxmAzA,17042
transformers/models/whisper/english_normalizer.py,sha256=MTJ16OhstprR2X8owfEJmONqkoSHHyzztENejmEhSBM,22822
transformers/models/whisper/feature_extraction_whisper.py,sha256=Y_O7xrT8zQ_kK9NSGafiGKfwAPiRBbcCYEiwjMYQHHo,14775
transformers/models/whisper/generation_whisper.py,sha256=Dnlxg8KJE3M_aIkFDLS4kPoetm7p9wiXAUMjvh72ti8,102437
transformers/models/whisper/modeling_flax_whisper.py,sha256=WURGm5l04PO1mfxsoLcIbHbrzNgSYZOXT89NFFgrYWI,73768
transformers/models/whisper/modeling_tf_whisper.py,sha256=cXOrTKxsKUpU8nrXxhclN-wwX8f5EyiAmorTygKwiq8,84861
transformers/models/whisper/modeling_whisper.py,sha256=nvt8f0Xhxqmf67l6FgrU1MvD3bnI1J146hffwLR-wpw,104421
transformers/models/whisper/processing_whisper.py,sha256=OqcvUtgrBq1hJHeIZDrgh5d6epo4U4W6yI-sbjomZiw,3923
transformers/models/whisper/tokenization_whisper.py,sha256=zz5kIhhyeRfvk1BtwPGAKtcdqw9suJRZFxEojuAV09k,57326
transformers/models/whisper/tokenization_whisper_fast.py,sha256=HhkV7bEBDgTCXGp1lJ_VGgREMshUG232wqDp-rsQkQg,30243
transformers/models/x_clip/__init__.py,sha256=ufjh6w7SNuNAUjAHp_MK3yRcrHm22-SfhZ0ZfbiXhGw,1030
transformers/models/x_clip/__pycache__/__init__.cpython-312.pyc,,
transformers/models/x_clip/__pycache__/configuration_x_clip.cpython-312.pyc,,
transformers/models/x_clip/__pycache__/modeling_x_clip.cpython-312.pyc,,
transformers/models/x_clip/__pycache__/processing_x_clip.cpython-312.pyc,,
transformers/models/x_clip/configuration_x_clip.py,sha256=6g8IhFhbceFhELrUURHL2kYIGs1u6aPcnZIjQu7CvKk,18729
transformers/models/x_clip/modeling_x_clip.py,sha256=9OfV1xIUxEJwpjA0Nt6vKwj6F8eTjljAVzfUezgFp9A,73360
transformers/models/x_clip/processing_x_clip.py,sha256=6V8_tO7mYPKO4dg9cy1rRvsEFVK01vegmfGWVbKkTp0,6928
transformers/models/xglm/__init__.py,sha256=ZU7tQBmBXzr8wh9MJNDZ5uIrsCRQP8tuNrpGDd2W3OI,1142
transformers/models/xglm/__pycache__/__init__.cpython-312.pyc,,
transformers/models/xglm/__pycache__/configuration_xglm.cpython-312.pyc,,
transformers/models/xglm/__pycache__/modeling_flax_xglm.cpython-312.pyc,,
transformers/models/xglm/__pycache__/modeling_tf_xglm.cpython-312.pyc,,
transformers/models/xglm/__pycache__/modeling_xglm.cpython-312.pyc,,
transformers/models/xglm/__pycache__/tokenization_xglm.cpython-312.pyc,,
transformers/models/xglm/__pycache__/tokenization_xglm_fast.cpython-312.pyc,,
transformers/models/xglm/configuration_xglm.py,sha256=WvBIkxXt1Kv0-kpNDPPirmP6bkFn6juD5DKiruGPyKc,5873
transformers/models/xglm/modeling_flax_xglm.py,sha256=s52aHZ8L5WYF07UfPlXgTiXC2SXLp9-q990oTua52z8,33195
transformers/models/xglm/modeling_tf_xglm.py,sha256=jBjFE1g5qCL8cHci4Vp31_XIIetBIyUZC6-fnyDs2lM,45349
transformers/models/xglm/modeling_xglm.py,sha256=TEk4ZpZyBIoXxvhRM8SQinVMI5rOrezolRBRUtO70H4,38524
transformers/models/xglm/tokenization_xglm.py,sha256=INwt_y3XTT9qbhCV3Q14hlX54D9WaEDN75_5DIGcr9A,12513
transformers/models/xglm/tokenization_xglm_fast.py,sha256=bl4P5U6fic-V3r7d4jZ8btJT0CCcI5v2rprw129-aIM,7622
transformers/models/xlm/__init__.py,sha256=QevE83gMJ5h41H7EKxRAUN-kmE0zgOsyGj6QzWcpjmk,1058
transformers/models/xlm/__pycache__/__init__.cpython-312.pyc,,
transformers/models/xlm/__pycache__/configuration_xlm.cpython-312.pyc,,
transformers/models/xlm/__pycache__/modeling_tf_xlm.cpython-312.pyc,,
transformers/models/xlm/__pycache__/modeling_xlm.cpython-312.pyc,,
transformers/models/xlm/__pycache__/tokenization_xlm.cpython-312.pyc,,
transformers/models/xlm/configuration_xlm.py,sha256=M1S7atRiLWIsRmmSFnm-s179-_AWtcDm3B_LeqTnNok,11053
transformers/models/xlm/modeling_tf_xlm.py,sha256=UH-lSaKroJ7dmVi7HDVL9wqe7Cq9bkOn6snQ4hR4ocU,56664
transformers/models/xlm/modeling_xlm.py,sha256=IX-RZXgfzGMCMn7ZOL4UPnQJnQILfETHHdnk8uRueSI,55141
transformers/models/xlm/tokenization_xlm.py,sha256=zK3o3R5z6TW-YWLtDNeMQXINYfOdplI9Q0Vwo5mJCS4,24476
transformers/models/xlm_roberta/__init__.py,sha256=dhjej7PBi8UrfXRkTxh9CWXnw8wuLZHPT9FYFfCkIHg,1184
transformers/models/xlm_roberta/__pycache__/__init__.cpython-312.pyc,,
transformers/models/xlm_roberta/__pycache__/configuration_xlm_roberta.cpython-312.pyc,,
transformers/models/xlm_roberta/__pycache__/modeling_flax_xlm_roberta.cpython-312.pyc,,
transformers/models/xlm_roberta/__pycache__/modeling_tf_xlm_roberta.cpython-312.pyc,,
transformers/models/xlm_roberta/__pycache__/modeling_xlm_roberta.cpython-312.pyc,,
transformers/models/xlm_roberta/__pycache__/tokenization_xlm_roberta.cpython-312.pyc,,
transformers/models/xlm_roberta/__pycache__/tokenization_xlm_roberta_fast.cpython-312.pyc,,
transformers/models/xlm_roberta/configuration_xlm_roberta.py,sha256=OMf77FAVM4tvv7RAOP9aOFi7IyVOqGvwhtchDQ0Mx_A,7571
transformers/models/xlm_roberta/modeling_flax_xlm_roberta.py,sha256=0i0Ns1rlKIA7-118Nw-7hh6HeYVTn3Qj0mE82K7725U,58763
transformers/models/xlm_roberta/modeling_tf_xlm_roberta.py,sha256=izOMRENtdeo9HyJIwnDcxmGN83ZhD0a3o0VC2mBm-Mk,82124
transformers/models/xlm_roberta/modeling_xlm_roberta.py,sha256=qD0fmJFX-Gcft0zpdufB_FJbS2rU20tHWGk1_7KA1vs,79535
transformers/models/xlm_roberta/tokenization_xlm_roberta.py,sha256=QSxB42IVqgU1fpaYc4f_kvRPfElUreCByCqr0Qo22MA,12741
transformers/models/xlm_roberta/tokenization_xlm_roberta_fast.py,sha256=foyAhWec6tKr3fuvLBukYrE_K891Z-rhlnK87m7yQRg,7960
transformers/models/xlm_roberta_xl/__init__.py,sha256=V0fXTKk2hQmf5dKogCJ0HSiRBxVX-rs7c414ZoZIh28,1009
transformers/models/xlm_roberta_xl/__pycache__/__init__.cpython-312.pyc,,
transformers/models/xlm_roberta_xl/__pycache__/configuration_xlm_roberta_xl.cpython-312.pyc,,
transformers/models/xlm_roberta_xl/__pycache__/modeling_xlm_roberta_xl.cpython-312.pyc,,
transformers/models/xlm_roberta_xl/configuration_xlm_roberta_xl.py,sha256=q_TJlvjrKd8W1OEDniUEhtFolirVRuseCReHfgM84jA,7323
transformers/models/xlm_roberta_xl/modeling_xlm_roberta_xl.py,sha256=0OZBWFkE7qAM1RtccWkGEMcwQHLjOlTY2CuQFtdDArc,77739
transformers/models/xlnet/__init__.py,sha256=t-UvrFyorGF7VMuATzjrB_cUqKsM-8O9KqxiWjtJqhs,1109
transformers/models/xlnet/__pycache__/__init__.cpython-312.pyc,,
transformers/models/xlnet/__pycache__/configuration_xlnet.cpython-312.pyc,,
transformers/models/xlnet/__pycache__/modeling_tf_xlnet.cpython-312.pyc,,
transformers/models/xlnet/__pycache__/modeling_xlnet.cpython-312.pyc,,
transformers/models/xlnet/__pycache__/tokenization_xlnet.cpython-312.pyc,,
transformers/models/xlnet/__pycache__/tokenization_xlnet_fast.cpython-312.pyc,,
transformers/models/xlnet/configuration_xlnet.py,sha256=U_WpCoqALv86cbvTXgTVnJwOfl3nzcGTgZJd_9SDhvY,10953
transformers/models/xlnet/modeling_tf_xlnet.py,sha256=Ftt4x4FXax2ElbkZcU0jxIuc7c5U96V9ouiQgpL4EDY,77869
transformers/models/xlnet/modeling_xlnet.py,sha256=U30V5DpX0s42WxzmFLH6YA0V_MC1RsBZMluA_MZDyI8,93259
transformers/models/xlnet/tokenization_xlnet.py,sha256=iANzDlYVzbNkNNeT_j94W-ELSQ2i8cR2aBvXyiOK1Rw,15732
transformers/models/xlnet/tokenization_xlnet_fast.py,sha256=0DTH5b_d6hkErbGQHu3avDkrC4CnZ-yEYNrYrinbo5E,9399
transformers/models/xmod/__init__.py,sha256=WLxIbzC8oCEkMrerWHTy7GLopz0mqocSaacdcyb_BhQ,989
transformers/models/xmod/__pycache__/__init__.cpython-312.pyc,,
transformers/models/xmod/__pycache__/configuration_xmod.cpython-312.pyc,,
transformers/models/xmod/__pycache__/modeling_xmod.cpython-312.pyc,,
transformers/models/xmod/configuration_xmod.py,sha256=68vgi2zopSNso3NQRi68srEAel5XdV5q8DLsawGg8AA,9155
transformers/models/xmod/modeling_xmod.py,sha256=9Jxc3lCcDzoy7U_JsdnZzQbMAb0yCxFIV1bp5VJPw6I,75210
transformers/models/yolos/__init__.py,sha256=HLs9o0p_jTtnkcC0gYaa3ns1nw2uv3FC07RMOfNqptk,1077
transformers/models/yolos/__pycache__/__init__.cpython-312.pyc,,
transformers/models/yolos/__pycache__/configuration_yolos.cpython-312.pyc,,
transformers/models/yolos/__pycache__/feature_extraction_yolos.cpython-312.pyc,,
transformers/models/yolos/__pycache__/image_processing_yolos.cpython-312.pyc,,
transformers/models/yolos/__pycache__/modeling_yolos.cpython-312.pyc,,
transformers/models/yolos/configuration_yolos.py,sha256=IwkN8id70ZC9d5Pv6SHqvh0ycONeGBIle7yuRYdUKt4,7618
transformers/models/yolos/feature_extraction_yolos.py,sha256=DJS0_Q0qjxNUGEWhQAWDX_iHyQbBcz7U00gmAf3hyIs,1519
transformers/models/yolos/image_processing_yolos.py,sha256=IyzWO2CSFRFihlQ4rxnOAHYQ9xgjQZnNTfYo-lCKhTM,67865
transformers/models/yolos/modeling_yolos.py,sha256=ehfvFwtSpVX11jkPVDAacfsq6YkiboL5cJ_-6N7vhp4,39204
transformers/models/yoso/__init__.py,sha256=sCXsXYZuOQLFkZMexRb8qY7EJCftR54G_eO7qIUvdss,989
transformers/models/yoso/__pycache__/__init__.cpython-312.pyc,,
transformers/models/yoso/__pycache__/configuration_yoso.cpython-312.pyc,,
transformers/models/yoso/__pycache__/modeling_yoso.cpython-312.pyc,,
transformers/models/yoso/configuration_yoso.py,sha256=6PQqt0OjHQBTNnnhDE761sdwlq9_tqG48UJ-pBV3rBM,6715
transformers/models/yoso/modeling_yoso.py,sha256=vZTHSxKV5-xLEnI78UUoElfLoLvuTeMcENt4xHDArwA,54977
transformers/models/zamba/__init__.py,sha256=iqZnf8BQ49TLcB4mYwIfuJeF4aGvYhOBRiGI6_74ZFk,991
transformers/models/zamba/__pycache__/__init__.cpython-312.pyc,,
transformers/models/zamba/__pycache__/configuration_zamba.cpython-312.pyc,,
transformers/models/zamba/__pycache__/modeling_zamba.cpython-312.pyc,,
transformers/models/zamba/configuration_zamba.py,sha256=CUwysVd0zLr7HEkrcGMfyB6Utjt_p_DORQUr1wHT7NI,11284
transformers/models/zamba/modeling_zamba.py,sha256=mSRYbqr1oc5vfM6KwxJSKzi4D3CJyWeJKLh5NlvonSc,71675
transformers/models/zamba2/__init__.py,sha256=3FgH8KelorllnKF6ncpKGREwZXt6YwsQ7NPS8W6jcmQ,993
transformers/models/zamba2/__pycache__/__init__.cpython-312.pyc,,
transformers/models/zamba2/__pycache__/configuration_zamba2.cpython-312.pyc,,
transformers/models/zamba2/__pycache__/modeling_zamba2.cpython-312.pyc,,
transformers/models/zamba2/__pycache__/modular_zamba2.cpython-312.pyc,,
transformers/models/zamba2/configuration_zamba2.py,sha256=dxRvhBWlDsCM_ciPCVlrPhRSfUkCua5I1oObZbVOpqY,12673
transformers/models/zamba2/modeling_zamba2.py,sha256=yxZujbK2W84KFJU7317MZ7XTW3fXj69AfRgceg2exe4,95436
transformers/models/zamba2/modular_zamba2.py,sha256=92snkXVqAQ4CmrjP7wAt30n8VYp_Gt007U82m1aOY14,56952
transformers/models/zoedepth/__init__.py,sha256=rNum7_sa_6TE8LkLh0LEarnQnByBVHAWj8Bgnd-28kQ,1042
transformers/models/zoedepth/__pycache__/__init__.cpython-312.pyc,,
transformers/models/zoedepth/__pycache__/configuration_zoedepth.cpython-312.pyc,,
transformers/models/zoedepth/__pycache__/image_processing_zoedepth.cpython-312.pyc,,
transformers/models/zoedepth/__pycache__/modeling_zoedepth.cpython-312.pyc,,
transformers/models/zoedepth/configuration_zoedepth.py,sha256=UlEOSvaf754FvsbP2ZHXm_BAFe9JyBRaiolPKqK3GcM,12757
transformers/models/zoedepth/image_processing_zoedepth.py,sha256=w02YxQfV5lTrfi889-5E48KweLprqL4PyeoosObGLV8,28076
transformers/models/zoedepth/modeling_zoedepth.py,sha256=bwx8YgmIFoFxp1h3ZpjNzixsx5Lsr-jUlWdugcFaNrk,57258
transformers/onnx/__init__.py,sha256=wALLY4TPOK2iPrFcfZf_WiEmTRAU6dAWHElxGdexr58,1548
transformers/onnx/__main__.py,sha256=JZ9ZmeRsnDitwTMWb-dFT8W9AEmMoMKLQ3SvbyCkY0w,9497
transformers/onnx/__pycache__/__init__.cpython-312.pyc,,
transformers/onnx/__pycache__/__main__.cpython-312.pyc,,
transformers/onnx/__pycache__/config.cpython-312.pyc,,
transformers/onnx/__pycache__/convert.cpython-312.pyc,,
transformers/onnx/__pycache__/features.cpython-312.pyc,,
transformers/onnx/__pycache__/utils.cpython-312.pyc,,
transformers/onnx/config.py,sha256=zPDgC_HSLmMeqPkcLv_Y8EfbfLLEDLqPrvrfQCRyhl8,32556
transformers/onnx/convert.py,sha256=ZSh9jQE6B6cCxhlSbKLHxNmj48HkXXdl-HF7iGtZy5k,19369
transformers/onnx/features.py,sha256=GSuwZj760THxAkDmJYROt43La0GaY-bA19j2bE-XYVI,28264
transformers/onnx/utils.py,sha256=39Uw_GkFBsTb6ZvMIHRTnI289aQDhc6hwfEapaBGE-o,3625
transformers/optimization.py,sha256=3sjosJkgbtvc6FOFYstvYaLSCiQg_utmh0UWjQ5rvwA,41631
transformers/optimization_tf.py,sha256=UPtbbeR__ZoPG7eBD5XMBBiYfjAZR8a5L2zWJsCLL_8,16854
transformers/pipelines/__init__.py,sha256=O72BzwH-2MgV0feRqSiRber8vsBTsUccGIEd3seEy3I,55211
transformers/pipelines/__pycache__/__init__.cpython-312.pyc,,
transformers/pipelines/__pycache__/audio_classification.cpython-312.pyc,,
transformers/pipelines/__pycache__/audio_utils.cpython-312.pyc,,
transformers/pipelines/__pycache__/automatic_speech_recognition.cpython-312.pyc,,
transformers/pipelines/__pycache__/base.cpython-312.pyc,,
transformers/pipelines/__pycache__/depth_estimation.cpython-312.pyc,,
transformers/pipelines/__pycache__/document_question_answering.cpython-312.pyc,,
transformers/pipelines/__pycache__/feature_extraction.cpython-312.pyc,,
transformers/pipelines/__pycache__/fill_mask.cpython-312.pyc,,
transformers/pipelines/__pycache__/image_classification.cpython-312.pyc,,
transformers/pipelines/__pycache__/image_feature_extraction.cpython-312.pyc,,
transformers/pipelines/__pycache__/image_segmentation.cpython-312.pyc,,
transformers/pipelines/__pycache__/image_text_to_text.cpython-312.pyc,,
transformers/pipelines/__pycache__/image_to_image.cpython-312.pyc,,
transformers/pipelines/__pycache__/image_to_text.cpython-312.pyc,,
transformers/pipelines/__pycache__/mask_generation.cpython-312.pyc,,
transformers/pipelines/__pycache__/object_detection.cpython-312.pyc,,
transformers/pipelines/__pycache__/pt_utils.cpython-312.pyc,,
transformers/pipelines/__pycache__/question_answering.cpython-312.pyc,,
transformers/pipelines/__pycache__/table_question_answering.cpython-312.pyc,,
transformers/pipelines/__pycache__/text2text_generation.cpython-312.pyc,,
transformers/pipelines/__pycache__/text_classification.cpython-312.pyc,,
transformers/pipelines/__pycache__/text_generation.cpython-312.pyc,,
transformers/pipelines/__pycache__/text_to_audio.cpython-312.pyc,,
transformers/pipelines/__pycache__/token_classification.cpython-312.pyc,,
transformers/pipelines/__pycache__/video_classification.cpython-312.pyc,,
transformers/pipelines/__pycache__/visual_question_answering.cpython-312.pyc,,
transformers/pipelines/__pycache__/zero_shot_audio_classification.cpython-312.pyc,,
transformers/pipelines/__pycache__/zero_shot_classification.cpython-312.pyc,,
transformers/pipelines/__pycache__/zero_shot_image_classification.cpython-312.pyc,,
transformers/pipelines/__pycache__/zero_shot_object_detection.cpython-312.pyc,,
transformers/pipelines/audio_classification.py,sha256=zrtuKY6a39aZYxM7DmTJ2sLNMRpYWXjGszej_pKBNw0,10409
transformers/pipelines/audio_utils.py,sha256=5kg9oeQf5jhOKgMvcbiLMBqa5mzHwfXCb_DVd5gjpBA,12276
transformers/pipelines/automatic_speech_recognition.py,sha256=PG7D35He1CidDAsR4csDb9a7HJMb_mYuiVPzHrOB1YU,39330
transformers/pipelines/base.py,sha256=rSUnJxGWOpKEy-HuMe6xTmQHwURwsrFcnW1e1uiWpuU,63320
transformers/pipelines/depth_estimation.py,sha256=b-S2up5TFQO5OaiB--ZqE6UwXHfJKtjrS7P1-DeW_Ac,5748
transformers/pipelines/document_question_answering.py,sha256=6Qe-9p9eYghBH8DqtklahzNj5hOtXdzdTNm6rlBcRKk,24629
transformers/pipelines/feature_extraction.py,sha256=Ar_hPljY1Fa_xAsRYX4cCCss1vf-iC5uuKYHp3rejd0,3374
transformers/pipelines/fill_mask.py,sha256=DfU3fNqd-u1b8gdV1BcP46dToDFLpjkRrIpX7PizgPU,11641
transformers/pipelines/image_classification.py,sha256=XPVoxo33-HX-pdUYv9wfNCFXlnzG5EEQnvFTsH1WQug,9781
transformers/pipelines/image_feature_extraction.py,sha256=KIn3yldAUTxFmal-SNF7CPkcx1h3q_KMvyjUsHzsx9o,4732
transformers/pipelines/image_segmentation.py,sha256=j68l-F6zFJqjaVXurVH6JnUvIIRzBVMBkG1JwokBeFE,9614
transformers/pipelines/image_text_to_text.py,sha256=7viIvEtjQuvtUPfaUoTFCQyIJSMuHwlsZzO6haxcmsk,20484
transformers/pipelines/image_to_image.py,sha256=VHB7ElQIYrBGPQsD5QtTZ0p4piXJ_r13KU4fkWgbLms,5022
transformers/pipelines/image_to_text.py,sha256=i0M6APAlw2CAZoKT0_XQt-xAqZGrCHH0X68bOYJdPis,9505
transformers/pipelines/mask_generation.py,sha256=HRmUx-H9pl85go26t70Th7lsPQu_nDdnHgi62FkKL-s,13204
transformers/pipelines/object_detection.py,sha256=AYXkT4ItG1krxzRUNcXKLEkGqElVetA8EED3qkUGA0I,8219
transformers/pipelines/pt_utils.py,sha256=D-cFFKAaVtn3jaZGPKFr-U3JF3_YR5H3kO4QD1jrqQY,12762
transformers/pipelines/question_answering.py,sha256=CMlbvAdDSAWa9NGa2xfaO2ifCIRySsuHhKW4FrEjmCY,30356
transformers/pipelines/table_question_answering.py,sha256=j6jLgTZiT075nnl2G5hese2uH1Gc8Q3_yJVh81XoP78,20358
transformers/pipelines/text2text_generation.py,sha256=Fhu5MnZ6dj51OwuhPXzj1mU1c6MmXDv-tQY7Ybfuy34,17706
transformers/pipelines/text_classification.py,sha256=x2aqpPTMHnlxl7UaCiF7svNYFvGYQaLXFck0wjuREkM,11044
transformers/pipelines/text_generation.py,sha256=JzsdzcETzA2RJlhouHyjb-qRPKQiaBTtPiLKw_eVHLk,25057
transformers/pipelines/text_to_audio.py,sha256=Wtwt4TGuTHfTH8MY7am9lgRLmJ874GZl137F4RSNa3M,8804
transformers/pipelines/token_classification.py,sha256=h0GzOec0wAEofnGukueLcP8QHB14r3b4ne9UaFVpcSc,26943
transformers/pipelines/video_classification.py,sha256=6wOTeamFZZbFrYJrJrcxrlTn-NZ0kzKejw2JrT-ktkk,7885
transformers/pipelines/visual_question_answering.py,sha256=QfDj2u3s4jxkfwdpFWZiuZTZZ16GRAsjLk7n04vPQ4w,9192
transformers/pipelines/zero_shot_audio_classification.py,sha256=yxjBnd1f99GoJYOE51n8JhUiMRmkotMiFn-2uLnSsPo,6869
transformers/pipelines/zero_shot_classification.py,sha256=fBqB7-aNsKCk0nKMQPuiGFVb6HWxzAp8K-geg89-F9Q,12500
transformers/pipelines/zero_shot_image_classification.py,sha256=5poR-cQ-PgpLpN4TlHJpBFg6LsYgh9GZn0adKm0NG5Y,7818
transformers/pipelines/zero_shot_object_detection.py,sha256=A46-lyloP6XLyXCzuoYMgP0TDhchIZYr8IjiKNbJhos,10251
transformers/processing_utils.py,sha256=CzjU2SYNg1ysROTQc2IqviCpwOe58AdlLKldjOE8cRk,68300
transformers/pytorch_utils.py,sha256=1iuvUJp9VOUN6cwEqze86vzP4zRLkKNRjMXqWjbjcYk,15804
transformers/quantizers/__init__.py,sha256=hCprQnoI20-O1FSMSRgD-P9_NKEzN7kEfY66_BrQxz0,699
transformers/quantizers/__pycache__/__init__.cpython-312.pyc,,
transformers/quantizers/__pycache__/auto.cpython-312.pyc,,
transformers/quantizers/__pycache__/base.cpython-312.pyc,,
transformers/quantizers/__pycache__/quantizer_aqlm.cpython-312.pyc,,
transformers/quantizers/__pycache__/quantizer_awq.cpython-312.pyc,,
transformers/quantizers/__pycache__/quantizer_bitnet.cpython-312.pyc,,
transformers/quantizers/__pycache__/quantizer_bnb_4bit.cpython-312.pyc,,
transformers/quantizers/__pycache__/quantizer_bnb_8bit.cpython-312.pyc,,
transformers/quantizers/__pycache__/quantizer_compressed_tensors.cpython-312.pyc,,
transformers/quantizers/__pycache__/quantizer_eetq.cpython-312.pyc,,
transformers/quantizers/__pycache__/quantizer_fbgemm_fp8.cpython-312.pyc,,
transformers/quantizers/__pycache__/quantizer_finegrained_fp8.cpython-312.pyc,,
transformers/quantizers/__pycache__/quantizer_gptq.cpython-312.pyc,,
transformers/quantizers/__pycache__/quantizer_higgs.cpython-312.pyc,,
transformers/quantizers/__pycache__/quantizer_hqq.cpython-312.pyc,,
transformers/quantizers/__pycache__/quantizer_quanto.cpython-312.pyc,,
transformers/quantizers/__pycache__/quantizer_spqr.cpython-312.pyc,,
transformers/quantizers/__pycache__/quantizer_torchao.cpython-312.pyc,,
transformers/quantizers/__pycache__/quantizer_vptq.cpython-312.pyc,,
transformers/quantizers/__pycache__/quantizers_utils.cpython-312.pyc,,
transformers/quantizers/auto.py,sha256=ib0Wh3ruD6ZywZ5FvsPCru4OHhNUcfnonAbr3qZjrc4,9479
transformers/quantizers/base.py,sha256=nzciGVtvMbzp6otpGhQ8opO4fOHV3OXyjtt67pLXO2s,10502
transformers/quantizers/quantizer_aqlm.py,sha256=khCb8mQ2T0tYPwCJqA7wkO6_59R2ChtHHnwQJzhfABc,3692
transformers/quantizers/quantizer_awq.py,sha256=Kqxqd1RBp7t-qbAS8IVtj_Jpo54_IgSvIa4G6O8xeBg,6902
transformers/quantizers/quantizer_bitnet.py,sha256=CemDWbQrsFSlay_VK0fumF5EKPo6QsCEzJihkbC8PZg,4302
transformers/quantizers/quantizer_bnb_4bit.py,sha256=Fv9CvyaPKSDd_U7lMpYuUU3Jp-hUbbOTKrnZOi-Vpsc,16331
transformers/quantizers/quantizer_bnb_8bit.py,sha256=KE4Vj-l1gFzmL1dIiOpCvgEFPtg6v3Bsa1JwoSpbndc,13894
transformers/quantizers/quantizer_compressed_tensors.py,sha256=hBvOaMv78pwnnaRj3yUhP7MbsWs4580ZcMHRgBV1aMk,5367
transformers/quantizers/quantizer_eetq.py,sha256=9Vw168CJV2GTyzA_hIuwTtPEyFArhrjA6ZQQqMBTbzo,7326
transformers/quantizers/quantizer_fbgemm_fp8.py,sha256=i2Ad9lj4jZtwjd3nDRgLXy5I35qYYgItuZ8AwNSQlSo,8142
transformers/quantizers/quantizer_finegrained_fp8.py,sha256=BMGP9_57fZp0_gHDFc6KgtAU84Ov17NICY__bJHyc2g,8072
transformers/quantizers/quantizer_gptq.py,sha256=_tatFGRBX0n1tx3cwjMQvHQwIVUWNPy9LmDGe2M0Izw,5678
transformers/quantizers/quantizer_higgs.py,sha256=c0UyV3PHs-W2XmoUaVg6e5x6jUD_5rtXDy7RhUvf5Gc,8532
transformers/quantizers/quantizer_hqq.py,sha256=-9GP6MPqtRDdLLundZmpgkppjWZ9WFWFAO13xCttDHw,11438
transformers/quantizers/quantizer_quanto.py,sha256=6kba9vt6-q7zUh68HaMzLoGKEX0k0JvNhjjVe5OJXDY,8107
transformers/quantizers/quantizer_spqr.py,sha256=usOurQIPHCIinCzNFETazMqjDeaRyP0iYAgXSWjGmik,3082
transformers/quantizers/quantizer_torchao.py,sha256=KkJw-Q3Zj87SlThx37nfbM1_VeYslfvalctYt13eKB0,10120
transformers/quantizers/quantizer_vptq.py,sha256=qGiJrDQHuYTb5AQw7TuapZwwhsV--NAtBfLC1ntBPSY,3720
transformers/quantizers/quantizers_utils.py,sha256=6bgmf8mLxow6gXonTFX7PLfqFsf6plUj7DOeXnXhwMM,1066
transformers/safetensors_conversion.py,sha256=LjnFRVfXRsOhIHdyiw6pevDJcMdsKwc3kvQ6csPs9wA,4074
transformers/sagemaker/__init__.py,sha256=fKtKAHamz_CLL9jPGCa2E-1n8RmuS-58qGtzZuKc3qg,730
transformers/sagemaker/__pycache__/__init__.cpython-312.pyc,,
transformers/sagemaker/__pycache__/trainer_sm.cpython-312.pyc,,
transformers/sagemaker/__pycache__/training_args_sm.cpython-312.pyc,,
transformers/sagemaker/trainer_sm.py,sha256=7GsKLtjdMfKp98OwHD7RcBsl745OOwHAaBswkfLkfsE,1044
transformers/sagemaker/training_args_sm.py,sha256=4ZnQhITfMwT0y2Y2MvkI11PEB_yfTX5Z7WrPKt0VXD8,5389
transformers/testing_utils.py,sha256=l-Yu5YEOspiblrpAEM-a9jPt5J68AbOVxtKchUBtbOY,101717
transformers/tf_utils.py,sha256=v4iybFTb3eRDgGzhAUTVYim-qNZvYF2k6rlHk7wTii4,11386
transformers/time_series_utils.py,sha256=MT780YtbZhdZcz7I9WJ9XVpmZgCVUT2eJ4-g8snaYvQ,7521
transformers/tokenization_utils.py,sha256=-poyf1T1zu46IYClocA-w3wxZYf2hRcxdfEb2tEMrlQ,47793
transformers/tokenization_utils_base.py,sha256=o8B6LXY3we7-it49D9eJ11WTckD_ebWCXtn7ErMnpBY,207905
transformers/tokenization_utils_fast.py,sha256=WdT86ZfZ6asp_sa15JpiPjogmyM-VgIDUjgchEMs9E0,40724
transformers/trainer.py,sha256=HzGh5i3WVfAhIqyJK416ZKATFjMgKXiqDcCv_udLeqQ,255322
transformers/trainer_callback.py,sha256=RfoIVGBkx9189yrWV73EL2zx03oKhk6sOrfPsqiCokY,33593
transformers/trainer_pt_utils.py,sha256=_bjdCcG_O8MGEQ0rC0BzRIn9i_GUrgfxYqKncLCo2uw,61694
transformers/trainer_seq2seq.py,sha256=MIcblxlkEhfVMwxCIWNE9es3pDPwiY5E2_BqZHpC_bQ,18372
transformers/trainer_utils.py,sha256=6Cz8HjQLeQ4V6v2-jgsBVa-frsSyTV2fm5gXM-N5OYE,32880
transformers/training_args.py,sha256=J3eNQs_HsAi_2xM5GJo0yZ2uY2ZjnVjYjtcWxuv9Hjw,159609
transformers/training_args_seq2seq.py,sha256=J9_vJQR4VxWAHWVbRmxjXHSRLd6KSe8inisIVezlbXI,3896
transformers/training_args_tf.py,sha256=dfMUa9jVO-rVxvImlQZr_NVqpVgtgBafBUbR_EcbkPY,14572
transformers/utils/__init__.py,sha256=x5SHqDqqx6q8ue7y8MecpuVDmUiXK335iH2wxRqCHf0,9412
transformers/utils/__pycache__/__init__.cpython-312.pyc,,
transformers/utils/__pycache__/backbone_utils.cpython-312.pyc,,
transformers/utils/__pycache__/bitsandbytes.cpython-312.pyc,,
transformers/utils/__pycache__/chat_template_utils.cpython-312.pyc,,
transformers/utils/__pycache__/constants.cpython-312.pyc,,
transformers/utils/__pycache__/deprecation.cpython-312.pyc,,
transformers/utils/__pycache__/doc.cpython-312.pyc,,
transformers/utils/__pycache__/dummy_detectron2_objects.cpython-312.pyc,,
transformers/utils/__pycache__/dummy_essentia_and_librosa_and_pretty_midi_and_scipy_and_torch_objects.cpython-312.pyc,,
transformers/utils/__pycache__/dummy_flax_objects.cpython-312.pyc,,
transformers/utils/__pycache__/dummy_keras_nlp_objects.cpython-312.pyc,,
transformers/utils/__pycache__/dummy_music_objects.cpython-312.pyc,,
transformers/utils/__pycache__/dummy_pt_objects.cpython-312.pyc,,
transformers/utils/__pycache__/dummy_sentencepiece_and_tokenizers_objects.cpython-312.pyc,,
transformers/utils/__pycache__/dummy_sentencepiece_objects.cpython-312.pyc,,
transformers/utils/__pycache__/dummy_speech_objects.cpython-312.pyc,,
transformers/utils/__pycache__/dummy_tensorflow_text_objects.cpython-312.pyc,,
transformers/utils/__pycache__/dummy_tf_objects.cpython-312.pyc,,
transformers/utils/__pycache__/dummy_timm_and_torchvision_objects.cpython-312.pyc,,
transformers/utils/__pycache__/dummy_tokenizers_objects.cpython-312.pyc,,
transformers/utils/__pycache__/dummy_torchaudio_objects.cpython-312.pyc,,
transformers/utils/__pycache__/dummy_torchvision_objects.cpython-312.pyc,,
transformers/utils/__pycache__/dummy_vision_objects.cpython-312.pyc,,
transformers/utils/__pycache__/fx.cpython-312.pyc,,
transformers/utils/__pycache__/generic.cpython-312.pyc,,
transformers/utils/__pycache__/hp_naming.cpython-312.pyc,,
transformers/utils/__pycache__/hub.cpython-312.pyc,,
transformers/utils/__pycache__/import_utils.cpython-312.pyc,,
transformers/utils/__pycache__/logging.cpython-312.pyc,,
transformers/utils/__pycache__/model_parallel_utils.cpython-312.pyc,,
transformers/utils/__pycache__/notebook.cpython-312.pyc,,
transformers/utils/__pycache__/peft_utils.cpython-312.pyc,,
transformers/utils/__pycache__/quantization_config.cpython-312.pyc,,
transformers/utils/__pycache__/sentencepiece_model_pb2.cpython-312.pyc,,
transformers/utils/__pycache__/sentencepiece_model_pb2_new.cpython-312.pyc,,
transformers/utils/__pycache__/versions.cpython-312.pyc,,
transformers/utils/backbone_utils.py,sha256=BZJOavniwDKDkz_f7yD-m8ZGDUx-li5FwqVZtJjm3rM,17431
transformers/utils/bitsandbytes.py,sha256=LzOKwcHWAxxZZv-7Ts9Q0vlEYvHd18affVgVbiR3Tzs,1040
transformers/utils/chat_template_utils.py,sha256=eJBpXCunfVUQA3OFk_dPmJGJO6O7ReZIOk6z0YsPIjA,17270
transformers/utils/constants.py,sha256=sZsUwOnA3CbtN1svs9YoaNLTTsAc9RVaITsgpf8K4iI,282
transformers/utils/deprecation.py,sha256=rsbc7bbHPmvePSmkpf_nXQ7OIX6ITFSK6nJxHvu0bY4,8065
transformers/utils/doc.py,sha256=1oAZT_lRqLlvI-tL9BCd_whVOvGCN1dub8b5Z85n3GA,41001
transformers/utils/dummy_detectron2_objects.py,sha256=n7Pt_7sbVBNfohKGcOARB-ZcPcJRbjEAcoLd2vTXndU,340
transformers/utils/dummy_essentia_and_librosa_and_pretty_midi_and_scipy_and_torch_objects.py,sha256=n6pY4s7zCII3dzo7Ejd0RviHa_pMateuDEwbbHgsTUY,902
transformers/utils/dummy_flax_objects.py,sha256=-iOHQAjQUx2S0_lpUA2REHQUULUmox2iEINyBjM3xn0,34003
transformers/utils/dummy_keras_nlp_objects.py,sha256=AVWt2orICCUXi754bkavvqPzYO91PjER-FlUZAw2jZc,294
transformers/utils/dummy_music_objects.py,sha256=1lxIebYUOdHJWMQ_T5IQgPgcO_wp_8YM_HGc3skuGVg,458
transformers/utils/dummy_pt_objects.py,sha256=pfhc7ErdWm0ypElZvMBoehD7s4rE2XLs7RHrdCOs5eA,256002
transformers/utils/dummy_sentencepiece_and_tokenizers_objects.py,sha256=BgPLr8Wz8A-17K86x04N21CKXtWNQLJEWx2c4aZRqaA,286
transformers/utils/dummy_sentencepiece_objects.py,sha256=pBykNNg9IPDeshVOeaw4sxHvgmt3by9X4rIQtz0ONYg,6455
transformers/utils/dummy_speech_objects.py,sha256=9eFm1cjdsYOPBoAz9JTgP35Bg8WF2C9AZ_y1hFpKZdQ,465
transformers/utils/dummy_tensorflow_text_objects.py,sha256=43V0IA2kb9gtuL0S1OL1eRFFxzQwKg4pPjMVuXUB5qg,306
transformers/utils/dummy_tf_objects.py,sha256=txICpcOG4gLScATANIxeMkrVtBGauQUVHgbaLg6sNgk,65939
transformers/utils/dummy_timm_and_torchvision_objects.py,sha256=EFuC5z6IsKOqqowoUGviJ3KgTjzvdTTN7gGQ3it-4t0,324
transformers/utils/dummy_tokenizers_objects.py,sha256=HW_eUXlwV3VPXxfSHSX3l4taOLbrajkziGUKTx8PCtE,11456
transformers/utils/dummy_torchaudio_objects.py,sha256=9A7Y4643_hTaqqZKlL-O524wRnrmNtODxisuDdO_7kU,488
transformers/utils/dummy_torchvision_objects.py,sha256=h8rYIXeLNLYSK6IfuczEUZzE8zHuH104euALu0R5hjU,3018
transformers/utils/dummy_vision_objects.py,sha256=dqfuMxoweRa4S5fEx_KdVXLVS0rzbS6FKqztkn9Ri_Y,18600
transformers/utils/fx.py,sha256=_8340YkdY7oPlhCEFeo8TQTCjMfNDSzSNtZX5j3-aOg,57316
transformers/utils/generic.py,sha256=KgDoJBemQ_WSHrvhpzolJrhia6beKllCLOXwodz6IvQ,29585
transformers/utils/hp_naming.py,sha256=vqcOXcDOyqbISWo8-ClUJUOBVbZM1h08EcymTwcRthc,4979
transformers/utils/hub.py,sha256=Zh5f0gBW6PtHFxaolETY04qZMl6ze4PklS4Vs-O_IsU,48942
transformers/utils/import_utils.py,sha256=j600LxeKP_1eSm27WrgAsq1xG7jQ_f3iXzaOIf_WXdI,83021
transformers/utils/logging.py,sha256=Gu-d6SzVG8gZaStSDWc4_pJFhqlp1imGNt-c8f03HOw,12318
transformers/utils/model_parallel_utils.py,sha256=XbGU9IlFF59K_aplRxUGVnTfIZ9mpbLomKqQ08ooTew,2272
transformers/utils/notebook.py,sha256=u-0gxSgdQ6Ow1jdVljOcWMFJ4roll-6nEsS9lsYPkKc,15828
transformers/utils/peft_utils.py,sha256=Jw6MjvVLtQ7booot0zK6X_xqRl_PAOh3lFZj1A2Guc8,5207
transformers/utils/quantization_config.py,sha256=s3ZJ7NAvm3kVr-Wr3unUpkjN3NImd9QzL4J5ZDCZapo,74763
transformers/utils/sentencepiece_model_pb2.py,sha256=XiQs9uMEusfAZP6t6IBuTTX9yl7LiOyJEi7Ib-Wzmq0,50677
transformers/utils/sentencepiece_model_pb2_new.py,sha256=Is_lMJU8MlmXGTkRL-Ut9hDWJwEmYeXedPCHPFaqlwM,6622
transformers/utils/versions.py,sha256=C-Tqr4qGSHH64ygIBCSo8gA6azz7Dbzh8zdc_yjMkX8,4337
